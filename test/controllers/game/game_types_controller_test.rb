require "test_helper"

class Game::GameTypesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @game_type = game_types(:slot)
    @admin = admins(:one)
    sign_in_as @admin
  end

  test "should get index" do
    get game_game_types_url
    assert_response :success
    assert_select "h1", "游戏类型管理"
  end

  test "should get new" do
    get new_game_game_type_url
    assert_response :success
    assert_select "h1", "添加游戏类型"
  end

  test "should create game_type" do
    assert_difference("GameType.count") do
      post game_game_types_url, params: {
        game_type: {
          name: "Test Type",
          order: 1,
          status: "onlined"
        }
      }
    end

    assert_redirected_to game_game_types_url
    assert_equal "游戏类型创建成功", flash[:notice]
    assert_equal @admin.name, GameType.last.created_by
  end

  test "should not create game_type with invalid params" do
    assert_no_difference("GameType.count") do
      post game_game_types_url, params: {
        game_type: {
          name: "",
          order: -1,
          status: nil
        }
      }
    end

    assert_response :unprocessable_entity
  end

  test "should get edit" do
    get edit_game_game_type_url(@game_type)
    assert_response :success
    assert_select "h1", "编辑游戏类型"
  end

  test "should update game_type" do
    patch game_game_type_url(@game_type), params: {
      game_type: {
        name: "Updated Type",
        order: 2,
        status: "onlined"
      }
    }

    assert_redirected_to game_game_types_url
    assert_equal "游戏类型更新成功", flash[:notice]
    @game_type.reload
    assert_equal "Updated Type", @game_type.name
    assert_equal 2, @game_type.order
    assert_equal "onlined", @game_type.status
  end

  test "should not update game_type with invalid params" do
    patch game_game_type_url(@game_type), params: {
      game_type: {
        name: "",
        order: -1,
        status: nil
      }
    }

    assert_response :unprocessable_entity
  end

  test "should destroy game_type" do
    # 确保没有关联的games记录
    @game_type.games.destroy_all

    assert_difference("GameType.count", -1) do
      delete game_game_type_url(@game_type)
    end

    assert_redirected_to game_game_types_url
    assert_equal "游戏类型删除成功", flash[:notice]
  end
end
