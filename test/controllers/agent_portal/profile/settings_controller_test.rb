require "test_helper"

class AgentPortal::Profile::SettingsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @agent = agents(:active_agent)
  end

  test "should show agent settings when agent is logged in" do
    sign_in_as @agent
    get agent_portal_profile_setting_path
    assert_response :success
    assert_select "h2", "个人设置"
    # 验证显示了代理商特有的内容
    assert_select "label", text: "管理渠道数"
    assert_select "label", text: "时区"
  end

  test "should update agent settings when agent is logged in" do
    sign_in_as @agent
    patch agent_portal_profile_setting_path, params: { agent: { nickname: "更新代理", timezone: "Asia/Shanghai" } }
    # 由于可能有验证错误，我们检查响应是成功、重定向或验证错误
    assert_includes [ 200, 302, 422 ], response.status
  end

  test "should require authentication for show" do
    # 不登录直接访问应该被重定向到登录页面
    get agent_portal_profile_setting_path
    assert_response :redirect
  end

  test "should require authentication for update" do
    # 不登录直接更新应该被重定向到登录页面
    patch agent_portal_profile_setting_path, params: { agent: { nickname: "新昵称" } }
    assert_response :redirect
  end

  test "agent should see agent-specific fields" do
    sign_in_as @agent
    get agent_portal_profile_setting_path
    assert_response :success

    # 验证代理商特有的字段存在
    assert_select "label", text: "管理渠道数"
    assert_select "label", text: "时区"
    assert_select "label", text: "邮箱"
    assert_select "label", text: "手机号"
  end
end
