require "test_helper"

class RootControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = admins(:one)
    @agent = agents(:active_agent)
    @channel = channels(:active_channel)
  end

  test "root path redirects unauthenticated users to login" do
    get root_path
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "root path redirects admin to dashboard" do
    sign_in_as @admin
    get root_path
    assert_response :redirect
    assert_redirected_to dashboard_path
  end

  test "root path does not cause double redirect error" do
    # 这个测试主要是确保不会出现双重重定向错误
    # 即使 Agent 或 Channel 用户访问根路径也不应该报错
    get root_path
    assert_response :redirect
    # 应该重定向到某个登录页面，不应该出现双重重定向错误
    assert_includes [ new_session_path, new_agent_portal_session_path, new_channel_portal_session_path ],
                    response.location.split("://").last.split("/", 2).last.prepend("/")
  end
end
