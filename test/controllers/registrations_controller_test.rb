require "test_helper"

class RegistrationsControllerTest < ActionDispatch::IntegrationTest
  test "should get new" do
    get new_registration_path
    assert_response :success
  end

  test "should create user with channel_id" do
    assert_difference("User.count") do
      assert_difference("RegisterTrack.count") do
        post registrations_path, params: {
          user: {
            mobile: "13800000000",
            password: "password123",
            password_confirmation: "password123"
          },
          channel_id: "123",
          device_id: "device123",
          ad_id: "ad123",
          ad_click_id: "click123",
          device_type: "0"
        }
      end
    end

    user = User.last
    assert_equal "13800000000", user.mobile
    assert_equal "actived", user.status

    register_track = user.register_track
    assert_equal 123, register_track.channel_id
    assert_equal "device123", register_track.device_id
    assert_equal "ad123", register_track.ad_id
    assert_equal "click123", register_track.ad_click_id
    assert_equal "0", register_track.device_type

    assert_redirected_to root_path
  end

  test "should not create user with invalid params" do
    assert_no_difference("User.count") do
      post registrations_path, params: {
        user: {
          mobile: "",
          password: "short",
          password_confirmation: "short"
        }
      }
    end

    assert_response :unprocessable_entity
  end
end
