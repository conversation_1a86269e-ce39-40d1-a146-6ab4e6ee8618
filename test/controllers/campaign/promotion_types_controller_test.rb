require "test_helper"

class Campaign::PromotionTypesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @promotion_type = promotion_types(:facebook)
    @admin = admins(:one)
    sign_in_as @admin
  end

  test "should get index" do
    get campaign_promotion_types_url
    assert_response :success
  end

  test "should get new" do
    get new_campaign_promotion_type_url
    assert_response :success
  end

  test "should create promotion_type" do
    assert_difference("PromotionType.count") do
      post campaign_promotion_types_url, params: { promotion_type: { name: "Test Type", code: "TEST", sort: 1, status: "enabled" } }
    end

    assert_redirected_to campaign_promotion_types_url
    assert_equal "推广类型创建成功", flash[:notice]
  end

  test "should get edit" do
    get edit_campaign_promotion_type_url(@promotion_type)
    assert_response :success
  end

  test "should update promotion_type" do
    patch campaign_promotion_type_url(@promotion_type), params: { promotion_type: { name: "Updated Type", code: "UPDATED", sort: 2, status: "enabled" } }
    assert_redirected_to campaign_promotion_types_url
    assert_equal "推广类型更新成功", flash[:notice]
  end

  test "should destroy promotion_type" do
    @promotion_type.channels.destroy_all

    assert_difference("PromotionType.count", -1) do
      delete campaign_promotion_type_url(@promotion_type)
    end

    assert_redirected_to campaign_promotion_types_url
    assert_equal "推广类型删除成功", flash[:notice]
  end
end
