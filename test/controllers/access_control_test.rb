require "test_helper"

class AccessControlTest < ActionDispatch::IntegrationTest
  setup do
    @admin = admins(:one)
    @agent = agents(:active_agent)
    @channel = channels(:active_channel)
  end

  test "access control system is working" do
    # 测试未登录用户无法访问任何受保护的页面
    get profile_setting_path
    assert_response :redirect
    assert_redirected_to new_session_path

    get agent_portal_profile_setting_path
    assert_response :redirect
    assert_redirected_to new_agent_portal_session_path

    get channel_portal_profile_setting_path
    assert_response :redirect
    assert_redirected_to new_channel_portal_session_path
  end

  test "root path redirects correctly for unauthenticated users" do
    # 未登录用户访问根路径应该重定向到登录页面
    get root_path
    assert_response :redirect
    assert_redirected_to new_session_path
  end
end
