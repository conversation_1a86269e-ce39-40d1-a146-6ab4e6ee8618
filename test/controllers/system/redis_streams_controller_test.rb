require "test_helper"

class System::RedisStreamsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = admins(:admin)
    sign_in_as @admin
    # 初始化 Redis stream 和 group
    redis = Rails.cache.redis.with { |c| c }
    begin
      redis.xgroup(:create, RedisStreamProcessor::STREAM_KEY, RedisStreamProcessor::GROUP_NAME, "0", mkstream: true)
    rescue Redis::CommandError => e
      # group 已存在时忽略
    end
    # 添加一条消息，便于测试清空
    redis.xadd(RedisStreamProcessor::STREAM_KEY, { user_id: 1, bet: 100 })
  end

  test "should get index" do
    get system_redis_streams_url
    assert_response :success
    assert_select "h1", "Redis Stream 统计信息"
  end

  test "should show stream info" do
    get system_redis_streams_url
    assert_response :success
    assert_select "td", RedisStreamProcessor::STREAM_KEY
    assert_select "td", RedisStreamProcessor::G<PERSON>UP_NAME
    assert_select "td", RedisStreamProcessor::CONSUMER_NAME
  end

  test "should clear stream" do
    delete clear_stream_system_redis_streams_path
    assert_redirected_to system_redis_streams_url
    follow_redirect!
    assert_match "队列已清空", response.body
    len = Rails.cache.redis.with { |c| c.xlen(RedisStreamProcessor::STREAM_KEY) rescue 0 }
    assert_equal 0, len
  end
end
