require "test_helper"

class System::SettingItemsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @setting = settings(:system_setting)
    @admin = admins(:one)
    sign_in_as @admin
  end

  test "should get index" do
    get system_setting_items_url
    assert_response :success
  end

  test "should get edit" do
    get edit_system_setting_item_url(@setting)
    assert_response :success
  end

  test "should update setting" do
    patch system_setting_item_url(@setting), params: {
      setting: {
        label: "Updated Label",
        value: "Updated Value",
        value_type: "string",
        description: "Updated Description",
        status: :enabled,
        sort: 2
      }
    }
    assert_redirected_to system_setting_items_path(section: @setting.section)
    @setting.reload
    assert_equal "Updated Label", @setting.label
    assert_equal "Updated Value", @setting.value
    assert_equal "string", @setting.value_type
    assert_equal "Updated Description", @setting.description
    assert_equal "enabled", @setting.status
    assert_equal 2, @setting.sort
  end

  test "should not update setting with invalid params" do
    patch system_setting_item_url(@setting), params: {
      setting: {
        label: "",
        value_type: "invalid_type"
      }
    }
    assert_response :unprocessable_entity
  end
end
