require "test_helper"

class ChannelPortal::Profile::SettingsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @channel = channels(:active_channel)
  end

  test "should show channel settings when channel is logged in" do
    sign_in_as @channel
    get channel_portal_profile_setting_path
    assert_response :success
    assert_select "h2", "个人设置"
    # 验证显示了渠道特有的内容
    assert_select "label", text: "所属代理"
    assert_select "label", text: "推广类型"
  end

  test "should update channel settings when channel is logged in" do
    sign_in_as @channel
    patch channel_portal_profile_setting_path, params: { channel: { nickname: "更新渠道", timezone: "Asia/Shanghai" } }
    # 由于可能有验证错误，我们检查响应是成功或重定向
    assert_includes [ 200, 302 ], response.status
  end

  test "should require authentication for show" do
    # 不登录直接访问应该被重定向到登录页面
    get channel_portal_profile_setting_path
    assert_response :redirect
  end

  test "should require authentication for update" do
    # 不登录直接更新应该被重定向到登录页面
    patch channel_portal_profile_setting_path, params: { channel: { nickname: "新昵称" } }
    assert_response :redirect
  end

  test "channel should see channel-specific fields" do
    sign_in_as @channel
    get channel_portal_profile_setting_path
    assert_response :success

    # 验证渠道特有的字段存在
    assert_select "label", text: "所属代理"
    assert_select "label", text: "推广类型"
    assert_select "label", text: "时区"
    assert_select "label", text: "邮箱"
    assert_select "label", text: "手机号"
  end
end
