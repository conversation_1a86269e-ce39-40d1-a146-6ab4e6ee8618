ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
require "rails/test_help"

module ActiveSupport
  class TestCase
    # 在每个测试执行前清空缓存
    setup do
      Rails.cache.clear
      setup_redis_test_environment
    end

    # Run tests in parallel with specified workers
    parallelize(workers: :number_of_processors) do |worker|
      # 为每个并行进程设置唯一的缓存命名空间
      Rails.cache.options[:namespace] = "test_worker_#{worker}"
    end

    # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
    fixtures :all

    # Add more helper methods to be used by all tests here...

    def auth_headers(admin)
      { "Authorization" => "Bearer #{admin.token}" }
    end

    def sign_in_as(user)
      case user
      when Admin
        post session_url, params: {
          email_address: user.name,
          password: "password"
        }, headers: {
          "REMOTE_ADDR" => "127.0.0.1",
          "HTTP_USER_AGENT" => "Rails Testing"
        }
      when Agent
        post agent_portal_session_url, params: {
          email_address: user.name,
          password: "password"
        }, headers: {
          "REMOTE_ADDR" => "127.0.0.1",
          "HTTP_USER_AGENT" => "Rails Testing"
        }
      when Channel
        post channel_portal_session_url, params: {
          email_address: user.name,
          password: "password"
        }, headers: {
          "REMOTE_ADDR" => "127.0.0.1",
          "HTTP_USER_AGENT" => "Rails Testing"
        }
      end
    end

    private

    def setup_redis_test_environment
      Rails.cache.redis.with do |redis_client|
        begin
          # 清理所有测试相关的键
          keys = redis_client.keys("test:*")
          redis_client.del(*keys) if keys.any?

          # 确保 Stream 和 Consumer Group 存在
          begin
            redis_client.xgroup(:create, RedisStreamProcessor::STREAM_KEY, RedisStreamProcessor::GROUP_NAME, "0", mkstream: true)
          rescue Redis::CommandError => e
            # 如果 group 已存在，忽略错误
            puts "Group creation skipped: #{e.message}" unless e.message.include?("BUSYGROUP")
          end
        rescue => e
          puts "Redis setup failed: #{e.message}"
        end
      end
    end
  end
end
