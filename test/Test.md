# Quick test API with curl command

First generate token with rails runner command:

```
bin/rails runner "puts Admin.first.inspect"
bin/rails runner "puts \"'admin' Token: #{Admin.first.token}\""
```

curl http://localhost:3000/api/v1/agents \
   -H "Accept: application/json" \
   -H "Authorization: Bearer {token}"

## Test with httpie

```
sudo apt-get update && sudo apt-get install httpie
```

http -A bearer -a $JWT_TOKEN :3000/api/v1/agents

验证 agent create:

http -A bearer -a $JWT_TOKEN POST :3000/api/v1/agents \
  agent[name]="Agent Name1" \
  agent[nickname]="Agent Nick" 

## 验证 admins列表

http -A bearer -a $JWT_TOKEN GET :3000/api/v1/admins \

验证 admin create:

http -A bearer -a $JWT_TOKEN POST :3000/api/v1/admins \
  admin[name]="Admin Name123" \
  admin[nickname]="Admin Nick" \
  admin[password]="password123" \
  admin[mobile]="13800138000" \
  admin[department]="game" \
  admin[remark]="Admin remark" \
  admin[role_ids][]=1

验证 agent update:  

http -A bearer -a $JWT_TOKEN PUT :3000/api/v1/agents/1 \
  agent[name]="Agent Name edit" \
  agent[nickname]="Agent Nick" \
  agent[email_address]="<EMAIL>" \
  agent[mobile]="13800138000" \
  agent[avatar]="avatar_url" \
  agent[remark]="Agent remark" \
  agent[status]="active" \
  agent[timezone]="Asia/Shanghai"

验证删除 agent:  
http -A bearer -a $JWT_TOKEN DELETE :3000/api/v1/agents/1

验证 black_ips 列表:
http -A bearer -a $JWT_TOKEN :3000/api/v1/black_ips

验证 black_ip 创建:
http -A bearer -a $JWT_TOKEN POST :3000/api/v1/black_ips \
  black_ip[ip]="*************" \
  black_ip[remark]="Suspicious Activity"

验证 black_ip 更新:
http -A bearer -a $JWT_TOKEN PUT :3000/api/v1/black_ips/1 \
  black_ip[ip]="*************" \
  black_ip[remark]="Updated Remark"

验证 black_ip 删除:
http -A bearer -a $JWT_TOKEN DELETE :3000/api/v1/black_ips/1