require "test_helper"

class UserTest < ActiveSupport::TestCase
  def setup
    @user = users(:one)
  end

  test "should be valid" do
    assert @user.valid?
  end

  test "mobile should be present" do
    @user.mobile = nil
    assert_not @user.valid?
  end

  test "mobile should be unique" do
    duplicate_user = @user.dup
    assert_not duplicate_user.valid?
  end

  test "password_digest should be present" do
    @user.password_digest = nil
    assert_not @user.valid?
  end

  test "login_count should be present" do
    @user.login_count = nil
    assert_not @user.valid?
  end

  test "login_count should be non-negative" do
    @user.login_count = -1
    assert_not @user.valid?
  end

  test "should update last login info" do
    ip = "***********"
    user_agent = "Test Browser"
    login_count = @user.login_count
    @user.update_last_login_info(ip: ip, user_agent: user_agent)

    assert_equal ip, @user.last_login_ip
    assert_equal user_agent, @user.last_login_user_agent
    assert_not_nil @user.last_login_at
    # test login count changed by 1
    assert_equal login_count + 1, @user.login_count
  end

  test "should generate short_id" do
    short_id = @user.short_id
    assert_not_nil short_id
    assert_kind_of String, short_id
    assert_not_equal @user.id.to_s, short_id
  end

  test "should find user from short_id" do
    short_id = @user.short_id
    found_user = User.from_short_id(short_id)
    assert_equal @user, found_user
  end

  test "should create all associated records after user creation" do
    user = User.create!(
      name: "test_user",
      nickname: "Test User",
      mobile: "13800138002",
      password: "password123",
      status: :actived
    )

    assert_not_nil user.wallet
    assert_not_nil user.wallet_ext
    assert_not_nil user.register_track
    assert_not_nil user.user_order_summary

    assert_equal user.id, user.wallet.user_id
    assert_equal user.id, user.wallet_ext.user_id
    assert_equal user.id, user.register_track.user_id
    assert_equal user.id, user.user_order_summary.user_id
  end
end
