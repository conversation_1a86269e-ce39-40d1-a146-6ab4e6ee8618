require "test_helper"

class ButtonTest < ActiveSupport::TestCase
  setup do
    @button = buttons(:create_user)
    @role = roles(:admin)
  end

  test "should be valid" do
    assert @button.valid?
  end

  test "name should be present" do
    @button.name = nil
    assert_not @button.valid?
  end

  test "identifier should be present" do
    @button.identifier = nil
    assert_not @button.valid?
  end

  test "identifier should be unique" do
    duplicate_button = @button.dup
    duplicate_button.name = "Different Name"
    assert_not duplicate_button.valid?
  end

  test "should have default active status" do
    button = Button.new(
      name: "Test Button",
      identifier: "test_button"
    )
    assert_equal "active", button.status
  end

  test "can have roles" do
    @button.roles << @role
    assert_includes @button.roles, @role
  end

  test "can get role_ids" do
    @button.roles << @role
    assert_includes @button.roles.pluck(:id), @role.id
  end

  test "should destroy associated role_buttons when destroyed" do
    @button.roles << @role
    assert_difference "RoleButton.count", -1 do
      @button.destroy
    end
  end
end
