require "test_helper"

class DepositTest < ActiveSupport::TestCase
  test "should create deposit" do
    deposit = deposits(:one)
    assert_equal 10.0, deposit.amount
    assert_equal 5.0, deposit.bonus
    assert_equal 2.0, deposit.bonus_multiplier
    assert_equal 20.0, deposit.required_volume
    assert_equal "completed", deposit.status
    assert_equal 1, deposit.status_before_type_cast
    assert_equal "user1", deposit.user.name
  end
end
