require "test_helper"

class RoleTest < ActiveSupport::TestCase
  setup do
    @role = roles(:admin)
    @menu = menus(:dashboard)
    @button = buttons(:create_user)
  end

  test "should be valid" do
    assert @role.valid?
  end

  test "name should be present" do
    @role.name = nil
    assert_not @role.valid?
  end

  test "code should be present" do
    @role.code = nil
    assert_not @role.valid?
  end

  test "name should be unique" do
    duplicate_role = @role.dup
    assert_not duplicate_role.valid?
  end

  test "code should be unique" do
    duplicate_role = @role.dup
    duplicate_role.name = "Different Name"
    assert_not duplicate_role.valid?
  end

  test "can add and remove menu" do
    assert_not @role.has_menu?(@menu)
    @role.add_menu(@menu)
    assert @role.has_menu?(@menu)
    @role.remove_menu(@menu)
    assert_not @role.has_menu?(@menu)
  end

  test "can add and remove button" do
    assert_not @role.has_button?(@button)
    @role.add_button(@button)
    assert @role.has_button?(@button)
    @role.remove_button(@button)
    assert_not @role.has_button?(@button)
  end

  test "can get menu_ids" do
    @role.add_menu(@menu)
    assert_includes @role.menu_ids, @menu.id
  end

  test "can get button_ids" do
    @role.add_button(@button)
    assert_includes @role.button_ids, @button.id
  end
end
