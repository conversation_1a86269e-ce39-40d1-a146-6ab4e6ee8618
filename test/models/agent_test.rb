require "test_helper"

class AgentTest < ActiveSupport::<PERSON><PERSON><PERSON>
  def setup
    @agent = Agent.new(
      name: "<PERSON><PERSON><PERSON>",
      nickname: "<PERSON>",
      email_address: "<EMAIL>",
      mobile: "13800138000",
      password: "password123",
      timezone: "UTC"
    )
  end

  test "should be valid" do
    assert @agent.valid?
  end

  test "name should be present" do
    @agent.name = nil
    assert_not @agent.valid?
  end

  test "name should be unique" do
    duplicate_agent = @agent.dup
    @agent.save
    assert_not duplicate_agent.valid?
  end

  test "password_digest should be present" do
    @agent.password_digest = nil
    assert_not @agent.valid?
  end

  test "timezone should be present" do
    @agent.timezone = nil
    assert_not @agent.valid?
  end

  test "timezone should default to UTC" do
    agent = Agent.new(name: "New Agent", password_digest: "password123")
    assert_equal "UTC", agent.timezone
  end

  test "status should default to 'active'" do
    assert_equal "actived", @agent.status
  end

  test "login_count should default to 0" do
    assert_equal 0, @agent.login_count
  end

  test "should have many channels" do
    assert_respond_to @agent, :channels
  end
end
