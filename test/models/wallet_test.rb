require "test_helper"

class WalletTest < ActiveSupport::TestCase
  test "should create wallet for user" do
    user = users(:one)
    wallet = user.wallet
    assert_equal user.id, wallet.user_id
    assert_equal 100, wallet.balance # compare with integer
    assert_equal 100.0, wallet.balance # compare with float

    assert_equal 10.0, wallet.betting_volume # compare with float
    assert_equal 100, wallet.required_volume # compare with integer
    assert_equal 100.0, wallet.required_volume # compare with float
  end
end
