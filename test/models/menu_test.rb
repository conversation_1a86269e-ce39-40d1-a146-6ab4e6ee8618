require "test_helper"

class MenuTest < ActiveSupport::TestCase
  setup do
    @menu = menus(:dashboard)
    @role = roles(:admin)
  end

  test "should be valid" do
    assert @menu.valid?
  end

  test "name should be present" do
    @menu.name = nil
    assert_not @menu.valid?
  end

  test "should have default active status" do
    menu = Menu.new(
      name: "Test Menu",
      path: "/test"
    )
    assert_equal "active", menu.status
  end

  test "should have default order value" do
    menu = Menu.new(
      name: "Test Menu",
      path: "/test"
    )
    assert_equal 0, menu.order
  end

  test "can have parent menu" do
    parent = menus(:dashboard)
    child = Menu.new(
      name: "Child Menu #{rand(1000)}",
      route_name: "child_#{rand(1000)}",
      path: "/child_#{rand(1000)}",
      parent: parent
    )
    assert child.valid?
    assert_equal parent, child.parent
  end

  test "can have child menus" do
    parent = menus(:dashboard)
    child = Menu.create!(
      name: "Child Menu #{rand(1000)}",
      title: "Child Menu #{rand(1000)}",
      route_name: "child_#{rand(1000)}",
      path: "/child_#{rand(1000)}",
      parent: parent
    )
    assert_includes parent.children, child
  end

  test "can have roles" do
    @menu.roles << @role
    assert_includes @menu.roles, @role
  end

  test "can get role_ids" do
    @menu.roles << @role
    assert_includes @menu.roles.pluck(:id), @role.id
  end

  test "should destroy associated role_menus when destroyed" do
    @menu.roles << @role
    assert_difference "RoleMenu.count", -1 do
      @menu.destroy
    end
  end

  test "should destroy child menus when parent is destroyed" do
    parent = menus(:dashboard)
    child = Menu.create!(
      name: "Child Menu #{rand(1000)}",
      title: "Child Menu #{rand(1000)}",
      route_name: "child_#{rand(1000)}",
      path: "/child_#{rand(1000)}",
      parent: parent
    )
    assert_difference "Menu.count", -2 do
      parent.destroy
    end
  end
end
