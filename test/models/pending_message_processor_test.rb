require "test_helper"

class PendingMessageProcessorTest < ActiveSupport::TestCase
  setup do
    # 为每个测试使用唯一的 stream 名称避免并行测试冲突
    @test_id = SecureRandom.hex(8)
    @stream_key = "#{RedisStreamProcessor::STREAM_KEY}_test_#{@test_id}"
    @group_name = "#{RedisStreamProcessor::GROUP_NAME}_test_#{@test_id}"
    @consumer_name = "#{RedisStreamProcessor::CONSUMER_NAME}_test_#{@test_id}"

    # 确保每个测试开始时都有干净的 Redis Stream 环境
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除可能存在的 stream
        redis_client.del(@stream_key)
        # 创建新的 stream 和 consumer group
        redis_client.xgroup(:create, @stream_key, @group_name, "0", mkstream: true)
      rescue => e
        puts "Setup failed: #{e.message}"
      end
    end
  end

  test "process_pending_messages handles empty pending messages" do
    assert_nothing_raised do
      PendingMessageProcessor.process_pending_messages(
        stream_key: @stream_key,
        group_name: @group_name
      )
    end
  end

  test "process_pending_messages handles pending messages" do
    # 添加测试消息
    entry_id = nil
    Rails.cache.redis.with do |redis_client|
      entry_id = redis_client.xadd(@stream_key, { test: "pending_message" })
    end
    assert entry_id, "Failed to add test message to stream"

    # 读取消息但不确认
    messages = nil
    Rails.cache.redis.with do |redis_client|
      messages = redis_client.xreadgroup(@group_name, @consumer_name, @stream_key, ">", count: 1)
    end
    assert messages, "Failed to read message from stream"

    # 等待消息变为pending状态
    sleep 1

    # 处理pending消息
    assert_nothing_raised do
      PendingMessageProcessor.process_pending_messages(
        stream_key: @stream_key,
        group_name: @group_name
      )
    end
  end

  teardown do
    # 清理测试数据
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除 stream（这会自动删除相关的 consumer group）
        redis_client.del(@stream_key)
      rescue => e
        puts "Teardown failed: #{e.message}"
      end
    end
  end
end
