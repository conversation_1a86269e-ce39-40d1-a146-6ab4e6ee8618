require "test_helper"

class GameTypeTest < ActiveSupport::TestCase
  def setup
    @game_type = game_types(:slot)
  end

  test "should be valid" do
    assert @game_type.valid?
  end

  test "name should be present" do
    @game_type.name = nil
    assert_not @game_type.valid?
    assert_includes @game_type.errors[:name], "类型名称不能为空"
  end

  test "name should be unique" do
    duplicate_game_type = @game_type.dup
    assert_not duplicate_game_type.valid?
    assert_includes duplicate_game_type.errors[:name], "类型名称已被使用"
  end

  test "order should be present" do
    @game_type.order = nil
    assert_not @game_type.valid?
    assert_includes @game_type.errors[:order], "排序不能为空"
  end

  test "order should be a number" do
    @game_type.order = "abc"
    assert_not @game_type.valid?
    assert_includes @game_type.errors[:order], "排序必须是数字"
  end

  test "order should be greater than or equal to 0" do
    @game_type.order = -1
    assert_not @game_type.valid?
    assert_includes @game_type.errors[:order], "必须大于等于 0"
  end

  test "status should be present" do
    @game_type.status = nil
    assert_not @game_type.valid?
    assert_includes @game_type.errors[:status], "状态不能为空"
  end

  test "status should be valid" do
    @game_type.status = :onlined
    assert @game_type.valid?

    @game_type.status = :offlined
    assert @game_type.valid?

    assert_raise(ArgumentError) do
      @game_type.status = :invalid_status
    end
  end

  test "status_text should return correct translation" do
    @game_type.status = :onlined
    assert_equal "上线", @game_type.status_text

    @game_type.status = :offlined
    assert_equal "下线", @game_type.status_text
  end

  test "should be ordered by order desc and id asc" do
    game_types = GameType.all
    assert_equal [ 4, 3, 2, 1 ], game_types.map(&:order)
  end
end
