require "test_helper"

class SystemLogTest < ActiveSupport::TestCase
  setup do
    @admin = admins(:one)
  end

  test "should create system log" do
    assert_difference "SystemLog.count" do
      SystemLog.log(
        actor: @admin,
        loggable: @admin,
        operate: :admin_login,
        action: "管理员登录",
        snapshot: { email: @admin.email_address }
      )
    end
  end

  test "should require actor" do
    log = SystemLog.new(
      loggable: @admin,
      operate: :admin_login,
      action: "管理员登录"
    )
    assert_not log.valid?
    assert_includes log.errors[:actor], "不能为空"
  end

  test "should require loggable" do
    log = SystemLog.new(
      actor: @admin,
      operate: :admin_login,
      action: "管理员登录"
    )
    assert_not log.valid?
    assert_includes log.errors[:loggable], "不能为空"
  end

  test "should require operate" do
    log = SystemLog.new(
      actor: @admin,
      loggable: @admin,
      action: "管理员登录"
    )
    assert_not log.valid?
    assert_includes log.errors[:operate], "不能为空"
  end

  test "should accept valid operate values" do
    valid_operates = %w[admin_login admin_logout agent_login agent_logout channel_login channel_logout create_admin destroy_admin create_role destroy_role]
    valid_operates.each do |operate|
      log = SystemLog.new(
        actor: @admin,
        loggable: @admin,
        operate: operate
      )
      assert log.valid?, "#{operate} should be valid"
    end
  end
end
