require "test_helper"

class PaymentTest < ActiveSupport::TestCase
  test "recharge_method must be present" do
    payment = Payment.new
    assert_not payment.valid?
    assert_includes payment.errors[:recharge_method], "不在允许的范围内"
  end

  test "recharge_method must be included in payment_solutions keys" do
    payment = Payment.new(recharge_method: "invalid_method")
    assert_not payment.valid?
    assert_includes payment.errors[:recharge_method], "不在允许的范围内"
  end

  test "valid recharge_method should be accepted" do
    valid_method = Payment.payment_solutions.keys.first
    payment = Payment.new(recharge_method: valid_method)
    payment.valid?
    assert_not_includes payment.errors[:recharge_method], "is not included in the list"
  end
end
