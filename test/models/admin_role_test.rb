require "test_helper"

class AdminRoleTest < ActiveSupport::TestCase
  def setup
    @admin = Admin.create!(
      name: "test_admin",
      email_address: "<EMAIL>",
      password: "password123"
    )
    @role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: "test_role_#{rand(1000)}"
    )
  end

  test "should create valid admin role" do
    admin_role = AdminRole.new(admin: @admin, role: @role)
    assert admin_role.valid?
  end

  test "should not allow duplicate admin role combinations" do
    AdminRole.create!(admin: @admin, role: @role)
    duplicate = AdminRole.new(admin: @admin, role: @role)
    assert_not duplicate.valid?
  end

  test "should require admin" do
    admin_role = AdminRole.new(role: @role)
    assert_not admin_role.valid?
  end

  test "should require role" do
    admin_role = AdminRole.new(admin: @admin)
    assert_not admin_role.valid?
  end
end
