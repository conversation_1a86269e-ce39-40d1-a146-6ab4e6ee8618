require "test_helper"

class WhiteIpTest < ActiveSupport::TestCase
  def setup
    @white_ip = white_ips(:internal_network)
  end

  test "should be valid" do
    assert @white_ip.valid?
  end

  test "ip should be present" do
    @white_ip.ip = nil
    assert_not @white_ip.valid?
  end

  test "ip should be unique" do
    duplicate_ip = @white_ip.dup
    assert_not duplicate_ip.valid?
  end

  test "status should have a default value" do
    white_ip = WhiteIp.new(ip: "***********")
    assert_equal 0, white_ip.status
  end

  test "should accept valid IP addresses" do
    valid_ips = [ "***********", "********", "**********" ]
    valid_ips.each do |ip|
      @white_ip.ip = ip
      assert @white_ip.valid?, "#{ip} should be valid"
    end
  end
end
