require "test_helper"
require "active_support/testing/time_helpers"

class AdminTest < ActiveSupport::TestCase
  include ActiveSupport::Testing::TimeHelpers

  setup do
    @new_admin = Admin.new(
      name: "TestAdmin",
      email_address: "<EMAIL>",
      nickname: "<PERSON><PERSON><PERSON>",
      password: "password123",
      mobile: "1234567890",
      department: "game",
      status: "actived"
    )
    @admin = admins(:one)
    @role = roles(:admin)
    @menu = menus(:dashboard)
    @button = buttons(:create_user)
  end

  test "should be valid" do
    assert @new_admin.valid?
  end

  test "name should be present" do
    @new_admin.name = nil
    assert_not @new_admin.valid?
  end

  test "name should be unique" do
    duplicate_admin = @new_admin.dup
    @new_admin.save
    assert_not duplicate_admin.valid?
  end

  test "email_address should be unique" do
    duplicate_admin = @new_admin.dup
    @new_admin.save
    assert_not duplicate_admin.valid?
  end

  test "email_address should be normalized" do
    @new_admin.email_address = " <EMAIL> "
    @new_admin.save
    assert_equal "<EMAIL>", @new_admin.email_address
  end

  test "password should be present" do
    @new_admin.password = nil
    assert_not @new_admin.valid?
  end

  test "password should have minimum length" do
    @new_admin.password = "short12"
    assert_not @new_admin.valid?
  end

  test "department should have default value" do
    admin = Admin.new
    assert_equal "game", admin.department
  end

  test "status should have default value" do
    admin = Admin.new
    assert_equal "actived", admin.status

    @new_admin.save
    assert_equal "actived", @new_admin.status
  end

  test "should authenticate with correct password" do
    @new_admin.save
    assert @new_admin.authenticate("password123")
  end

  test "should not authenticate with incorrect password" do
    @new_admin.save
    assert_not @new_admin.authenticate("wrongpassword")
  end

  test "should generate valid token" do
    role_code = "test_role_#{rand(1000)}"
    role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: role_code
    )
    @admin.roles << role
    token = @admin.token
    assert_not_nil token

    decoded_token = JWT.decode(
      token,
      Rails.application.credentials.secret_key_base,
      true,
      { algorithm: "HS256" }
    ).first

    assert_equal @admin.id, decoded_token["id"]
    assert_equal @admin.name, decoded_token["name"]
    assert_equal @admin.department, decoded_token["department"]
    assert_includes decoded_token["roles"], role_code
    assert decoded_token["exp"] > Time.now.to_i
  end

  test "should generate valid refresh token" do
    refresh_token = @admin.refresh_token
    assert_not_nil refresh_token

    decoded_token = JWT.decode(
      refresh_token,
      Rails.application.credentials.secret_key_base,
      true,
      { algorithm: "HS256" }
    ).first

    assert_equal @admin.id, decoded_token["id"]
    assert decoded_token["exp"] > Time.now.to_i
  end

  test "token should expire after 24 hours" do
    token = @admin.token
    decoded_token = JWT.decode(
      token,
      Rails.application.credentials.secret_key_base,
      true,
      { algorithm: "HS256" }
    ).first

    assert_in_delta 24.hours.from_now.to_i, decoded_token["exp"], 1
  end

  test "refresh token should expire after 7 days" do
    refresh_token = @admin.refresh_token
    decoded_token = JWT.decode(
      refresh_token,
      Rails.application.credentials.secret_key_base,
      true,
      { algorithm: "HS256" }
    ).first

    assert_in_delta 7.days.from_now.to_i, decoded_token["exp"], 1
  end

  test "decode_token should decode valid token" do
    token = @admin.token
    decoded_token = Admin.decode_token(token)
    assert_not_nil decoded_token
    assert_equal @admin.id, decoded_token["id"]
    assert_equal @admin.name, decoded_token["name"]
    assert_equal @admin.department, decoded_token["department"]
  end

  test "decode_token should return nil for invalid token" do
    assert_nil Admin.decode_token("invalid_token")
  end

  test "decode_token should return nil for blank token" do
    assert_nil Admin.decode_token("")
    assert_nil Admin.decode_token(nil)
    assert_nil Admin.decode_token("   ")
  end

  test "decode_token should return nil for expired token" do
    token = JWT.encode(
      {
        id: @admin.id,
        exp: 1.hour.ago.to_i
      },
      Rails.application.credentials.secret_key_base,
      "HS256"
    )
    assert_nil Admin.decode_token(token)
  end

  test "should generate totp secret on create" do
    admin = Admin.new(
      name: "TOTPTestAdmin",
      email_address: "<EMAIL>",
      password: "password123"
    )
    assert_nil admin.totp_secret
    admin.save
    assert_not_nil admin.totp_secret
    assert_match(/^[A-Z2-7]{32}$/, admin.totp_secret)
  end

  test "should verify valid totp code" do
    @admin.save
    current_code = @admin.totp.now
    assert @admin.verify_totp(current_code)
  end

  test "should not verify invalid totp code" do
    @admin.save
    assert_not @admin.verify_totp("000000")
  end

  test "should not verify expired totp code" do
    @admin.save
    old_code = @admin.totp.at(Time.now - 60)
    assert_not @admin.verify_totp(old_code)
  end

  test "should allow clock drift in totp verification" do
    @admin.save
    code = @admin.totp.now
    # 模拟时钟漂移在允许范围内（30秒）
    travel 25.seconds do
      assert @admin.verify_totp(code)
    end
  end

  test "should have default game department" do
    @new_admin.save
    assert_equal "game", @new_admin.department
  end

  test "can have roles" do
    @new_admin.save
    role_code = "test_role_#{rand(1000)}"
    role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: role_code
    )
    @new_admin.roles << role
    assert_includes @new_admin.roles, role
    assert_includes @new_admin.roles_codes, role_code
  end

  test "has_role? should work correctly" do
    @new_admin.save!
    role_code = "test_role_#{rand(1000)}"
    role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: role_code
    )
    @new_admin.roles << role
    assert @new_admin.has_role?(role_code)
    assert_not @new_admin.has_role?("non_existent_role")
  end

  test "add_role should not add duplicate roles" do
    @new_admin.save
    role_code = "test_role_#{rand(1000)}"
    role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: role_code
    )
    @new_admin.add_role(role)
    assert_equal 1, @new_admin.roles.count
    @new_admin.add_role(role)
    assert_equal 1, @new_admin.roles.count
  end

  test "remove_role should work correctly" do
    @new_admin.save
    role_code = "test_role_#{rand(1000)}"
    role = Role.create!(
      name: "Test Role #{rand(1000)}",
      code: role_code
    )
    @new_admin.roles << role
    assert_includes @new_admin.roles, role
    @new_admin.remove_role(role)
    assert_not_includes @new_admin.roles, role
  end

  test "can check if admin has role" do
    assert_not @admin.has_role?(@role.code)
    @admin.add_role(@role)
    assert @admin.has_role?(@role.code)
  end

  test "can add and remove role" do
    assert_not @admin.has_role?(@role.code)
    @admin.add_role(@role)
    assert @admin.has_role?(@role.code)
    @admin.remove_role(@role)
    assert_not @admin.has_role?(@role.code)
  end

  test "can get roles codes" do
    @admin.add_role(@role)
    assert_includes @admin.roles_codes, @role.code
  end

  test "can get accessible menus" do
    @admin.add_role(@role)
    @role.add_menu(@menu)
    assert_includes @admin.accessible_menus, @menu
  end

  test "can get accessible buttons" do
    @admin.add_role(@role)
    @role.add_button(@button)
    assert_includes @admin.accessible_buttons, @button
  end

  test "can check button operation permission" do
    @admin.add_role(@role)
    @role.add_button(@button)
    assert @admin.can_operate_button?(@button.identifier)
    assert_not @admin.can_operate_button?("non_existent_button")
  end
end
