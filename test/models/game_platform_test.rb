require "test_helper"

class GamePlatformTest < ActiveSupport::TestCase
  def setup
    @game_platform = game_platforms(:pg_soft)
  end

  test "should be valid" do
    assert @game_platform.valid?
  end

  test "name should be present" do
    @game_platform.name = nil
    assert_not @game_platform.valid?
    assert_includes @game_platform.errors[:name], "不能为空"
  end

  test "name should be unique" do
    duplicate_game_platform = @game_platform.dup
    assert_not duplicate_game_platform.valid?
    assert_includes duplicate_game_platform.errors[:name], "已被使用"
  end
end
