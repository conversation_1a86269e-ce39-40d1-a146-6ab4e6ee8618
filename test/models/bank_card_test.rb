require "test_helper"

class BankCardTest < ActiveSupport::TestCase
  def setup
    @bank_card = bank_cards(:one)
  end

  test "should be valid" do
    assert @bank_card.valid?
  end

  test "user should be present" do
    @bank_card.user = nil
    assert_not @bank_card.valid?
  end

  test "card_number should be present" do
    @bank_card.card_number = nil
    assert_not @bank_card.valid?
  end

  test "card_username should be present" do
    @bank_card.card_username = nil
    assert_not @bank_card.valid?
  end

  test "card_type should be present" do
    @bank_card.card_type = nil
    assert_not @bank_card.valid?
  end

  test "card_bank should be present" do
    @bank_card.card_bank = nil
    assert_not @bank_card.valid?
  end

  test "card_province should be present" do
    @bank_card.card_province = nil
    assert_not @bank_card.valid?
  end

  test "card_city should be present" do
    @bank_card.card_city = nil
    assert_not @bank_card.valid?
  end

  test "card_branch should be present" do
    @bank_card.card_branch = nil
    assert_not @bank_card.valid?
  end

  test "status should have default value" do
    bank_card = BankCard.new
    assert_equal 0, bank_card.status
  end

  test "should accept valid card data" do
    valid_card = BankCard.new(
      user: users(:one),
      card_number: "6222021111111111111",
      card_username: "王五",
      card_type: "储蓄卡",
      card_bank: "农业银行",
      card_province: "上海市",
      card_city: "上海市",
      card_branch: "上海分行",
      remark: "新卡",
      status: 0
    )
    assert valid_card.valid?
  end
end
