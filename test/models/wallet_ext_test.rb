require "test_helper"

class WalletExtTest < ActiveSupport::TestCase
  test "should be valid" do
    wallet_ext = wallet_exts(:one)
    assert wallet_ext.valid?
  end

  test "should require user" do
    wallet_ext = WalletExt.new
    assert_not wallet_ext.valid?
    assert_includes wallet_ext.errors[:user], "不能为空"
  end

  test "should enforce unique user_id" do
    wallet_ext = WalletExt.new(user: users(:one))
    assert_not wallet_ext.valid?
    assert_includes wallet_ext.errors[:user_id], "已被使用"
  end

  # test "the truth" do
  #   assert true
  # end
end
