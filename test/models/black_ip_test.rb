require "test_helper"

class BlackIpTest < ActiveSupport::TestCase
  def setup
    @black_ip = black_ips(:suspicious)
  end

  test "should be valid" do
    assert @black_ip.valid?
  end

  test "ip should be present" do
    @black_ip.ip = nil
    assert_not @black_ip.valid?
  end

  test "ip should be unique" do
    duplicate_ip = @black_ip.dup
    assert_not duplicate_ip.valid?
  end

  test "status should have a default value" do
    black_ip = BlackIp.new(ip: "*************")
    assert_equal 0, black_ip.status
  end

  test "should accept valid IP addresses" do
    valid_ips = [
      "*************",
      "**********",
      "************",
      "*******",
      "2001:0db8:85a3:0000:0000:8a2e:0370:7334",  # IPv6
      "2001:db8:85a3::8a2e:370:7334"              # IPv6 compressed
    ]
    valid_ips.each do |ip|
      @black_ip.ip = ip
      assert @black_ip.valid?, "#{ip} should be valid"
    end
  end

  test "should reject invalid IP addresses" do
    invalid_ips = [
      "256.1.2.3",           # Invalid octet
      "*******.5",           # Too many octets
      "1.2.3",               # Too few octets
      "*******.5.6",         # Invalid format
      "***************",     # Leading zeros
      "192.168.1",           # Incomplete
      "192.168.1.",          # Trailing dot
      ".192.168.1",          # Leading dot
      "192.168.1.256",       # Out of range
      "192.168.1.-1",        # Negative number
      "192.168.1.abc",       # Non-numeric
      "invalid-ip",          # Completely invalid
      "2001:0db8:85a3:0000:0000:8a2e:0370:7334:extra",  # Invalid IPv6
      "2001:0db8:85a3:0000:0000:8a2e:0370"              # Incomplete IPv6
    ]
    invalid_ips.each do |ip|
      @black_ip.ip = ip
      assert_not @black_ip.valid?, "#{ip} should be invalid"
      assert_includes @black_ip.errors[:ip], "不是有效的IP地址"
    end
  end
end
