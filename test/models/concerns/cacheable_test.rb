require "test_helper"

class CacheableTest < ActiveSupport::TestCase
  class DummyModel < ApplicationRecord
    include Cacheable
    self.table_name = "ads" # 使用已存在的表进行测试

    def self.cached_all
      Rails.cache.fetch(cache_key, raw: true) do
        unscoped.all.to_json
      end
    end
  end

  setup do
    Rails.cache.clear
  end

  test "cache_key returns correct key" do
    assert_equal "cacheable_test/dummy_model:all", DummyModel.cache_key
  end

  test "cached_all returns json string" do
    # 创建测试数据
    ad = Ad.create!(name: "Test Ad", status: :onlined)

    # 获取缓存数据
    cached_data = Ad.cached_all
    parsed_data = JSON.parse(cached_data)

    # 验证缓存数据
    assert_equal 1, parsed_data.length
    assert_includes parsed_data.first.keys, "type"
    assert_includes parsed_data.first.keys, "media_url"
  end

  test "cache is updated after record changes" do
    # 创建测试数据
    ad = Ad.create!(name: "Test Ad", status: :onlined)

    # 获取初始缓存
    initial_cache = Ad.cached_all

    # 更新记录
    ad.update!(name: "Updated Ad")

    # 获取更新后的缓存
    updated_cache = Ad.cached_all

    # 验证缓存已更新
    assert_not_equal initial_cache, updated_cache
    assert_includes JSON.parse(updated_cache).map { |a| a["name"] }, "Updated Ad"
  end
end
