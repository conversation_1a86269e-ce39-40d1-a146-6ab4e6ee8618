require "test_helper"

class BlackBankCardTest < ActiveSupport::TestCase
  def setup
    @black_bank_card = black_bank_cards(:one)
  end

  test "should be valid" do
    assert @black_bank_card.valid?
  end

  test "card_number should be present" do
    @black_bank_card.card_number = nil
    assert_not @black_bank_card.valid?
  end

  test "card_number should be unique" do
    duplicate_card = @black_bank_card.dup
    assert_not duplicate_card.valid?
  end

  test "should accept valid card number" do
    valid_card = BlackBankCard.new(
      card_number: "6222021111111111111",
      remark: "新卡",
      created_by: "test_user",
      status: 0
    )
    assert valid_card.valid?
  end
end
