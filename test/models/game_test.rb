require "test_helper"

class GameTest < ActiveSupport::TestCase
  def setup
    @game = games(:tiger)
  end

  test "should be valid" do
    assert @game.valid?
  end

  test "name should be present" do
    @game.name = nil
    assert_not @game.valid?
    assert_includes @game.errors[:name], "不能为空"
  end

  test "game_platform should be present" do
    @game.game_platform = nil
    assert_not @game.valid?
    assert_includes @game.errors[:game_platform], "不能为空"
  end

  test "integrator should be present" do
    @game.integrator = nil
    assert_not @game.valid?
    assert_includes @game.errors[:integrator], "不能为空"
  end

  test "game_type should be present" do
    @game.game_type = nil
    assert_not @game.valid?
    assert_includes @game.errors[:game_type], "不能为空"
  end

  test "screen_direction should be present" do
    @game.screen_direction = nil
    assert_not @game.valid?
    assert_includes @game.errors[:screen_direction], "不能为空"
  end

  test "screen_direction should be valid" do
    @game.screen_direction = :auto
    assert @game.valid?

    @game.screen_direction = :portrait
    assert @game.valid?

    @game.screen_direction = :landscape
    assert @game.valid?

    assert_raise(ArgumentError) do
      @game.screen_direction = :invalid_direction
    end
  end

  test "status should be present" do
    @game.status = nil
    assert_not @game.valid?
    assert_includes @game.errors[:status], "不能为空"
  end

  test "status should be valid" do
    @game.status = :onlined
    assert @game.valid?

    @game.status = :offlined
    assert @game.valid?

    assert_raise(ArgumentError) do
      @game.status = :invalid_status
    end
  end

  test "status_text should return correct translation" do
    @game.status = :onlined
    assert_equal "上线", @game.status_text

    @game.status = :offlined
    assert_equal "下线", @game.status_text
  end

  test "should belong to game_platform" do
    assert_respond_to @game, :game_platform
    assert_instance_of GamePlatform, @game.game_platform
  end

  test "should belong to integrator" do
    assert_respond_to @game, :integrator
    assert_instance_of Integrator, @game.integrator
  end

  test "should belong to game_type" do
    assert_respond_to @game, :game_type
    assert_instance_of GameType, @game.game_type
  end
end
