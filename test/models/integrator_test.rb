require "test_helper"

class IntegratorTest < ActiveSupport::Test<PERSON>ase
  def setup
    @integrator = integrators(:zs_game_jili)
  end

  test "should be valid" do
    assert @integrator.valid?
  end

  test "name should be present" do
    @integrator.name = nil
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:name], "集成商名称不能为空"
  end

  test "name should be unique" do
    duplicate_integrator = @integrator.dup
    assert_not duplicate_integrator.valid?
    assert_includes duplicate_integrator.errors[:name], "集成商名称已被使用"
  end

  test "code should be present" do
    @integrator.code = nil
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:code], "不能为空"
  end

  test "code should be unique" do
    duplicate_integrator = @integrator.dup
    assert_not duplicate_integrator.valid?
    assert_includes duplicate_integrator.errors[:code], "已被使用"
  end

  test "order should be present" do
    @integrator.order = nil
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:order], "排序不能为空"
  end

  test "order should be a number" do
    @integrator.order = "abc"
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:order], "排序必须是数字"
  end

  test "order should be greater than or equal to 0" do
    @integrator.order = -1
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:order], "必须大于等于 0"
  end

  test "status should be present" do
    @integrator.status = nil
    assert_not @integrator.valid?
    assert_includes @integrator.errors[:status], "状态不能为空"
  end

  test "status should be valid" do
    @integrator.status = :onlined
    assert @integrator.valid?

    @integrator.status = :offlined
    assert @integrator.valid?

    assert_raise(ArgumentError) do
      @integrator.status = :invalid_status
    end
  end

  test "status_text should return correct translation" do
    @integrator.status = :onlined
    assert_equal "上线", @integrator.status_text

    @integrator.status = :offlined
    assert_equal "下线", @integrator.status_text
  end

  test "should be ordered by order asc" do
    integrators = Integrator.all
    assert_equal [ 3, 2, 1 ], integrators.map(&:order)
  end
end
