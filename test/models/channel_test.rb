require "test_helper"

class ChannelTest < ActiveSupport::Test<PERSON>ase
  def setup
    @agent = Agent.create!(
      name: "TestA<PERSON>",
      password: "password123"
    )

    @promotion_type = PromotionType.create!(
      name: "Test Type",
      code: "TEST"
    )

    @channel = Channel.new(
      name: "<PERSON><PERSON>han<PERSON>",
      nickname: "<PERSON>",
      email_address: "<EMAIL>",
      mobile: "13800138000",
      password: "password123",
      timezone: "UTC",
      agent: @agent,
      promotion_type: @promotion_type
    )
  end

  test "should be valid" do
    assert @channel.valid?
  end

  test "name should be present" do
    @channel.name = nil
    assert_not @channel.valid?
  end

  test "name should be unique" do
    duplicate_channel = @channel.dup
    @channel.save
    assert_not duplicate_channel.valid?
  end

  test "timezone should be present" do
    @channel.timezone = nil
    assert_not @channel.valid?
  end

  test "timezone should default to UTC" do
    channel = Channel.new(
      name: "NewChannel",
      agent: @agent,
      promotion_type: @promotion_type
    )
    assert_equal "UTC", channel.timezone
  end

  test "status should default to actived" do
    assert_equal "actived", @channel.status
  end

  test "login_count should default to 0" do
    assert_equal 0, @channel.login_count
  end

  test "should belong to agent" do
    assert_respond_to @channel, :agent
  end

  test "should belong to promotion_type" do
    assert_respond_to @channel, :promotion_type
  end

  test "agent should be present" do
    @channel.agent = nil
    assert_not @channel.valid?
  end

  test "promotion_type should be present" do
    @channel.promotion_type = nil
    assert_not @channel.valid?
  end
end
