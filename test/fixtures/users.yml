one:
  name: user1
  nickname: user1
  password_digest: <%= BCrypt::Password.create('password123') %>
  totp_secret: <%= ROTP::Base32.random %>
  mobile: '***********'
  status: 0
  avatar: 'https://example.com/avatar.jpg'
  remark: 'System administrator'
  last_login_ip: '127.0.0.1'
  last_login_at: <%= Time.current %>
  last_login_user_agent: 'Mozilla/5.0'
  login_count: 0
  
two:
  name: user2
  nickname: user2
  password_digest: <%= BCrypt::Password.create('password123') %>
  totp_secret: <%= ROTP::Base32.random %>
  mobile: '***********'
  status: 0
  avatar: 'https://example.com/avatar.jpg'
  remark: 'Test user account'
  last_login_ip: '127.0.0.1'
  last_login_at: <%= Time.current %>
  last_login_user_agent: 'Mozilla/5.0'
  login_count: 0