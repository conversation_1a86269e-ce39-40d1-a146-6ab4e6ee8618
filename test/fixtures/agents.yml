<% password_digest = BCrypt::Password.create("password") %>

active_agent:
  name: "ActiveAgent"
  nickname: "Active"
  email_address: "<EMAIL>"
  mobile: "13800138001"
  password_digest: <%= password_digest %>
  status: actived
  timezone: "UTC"
  login_count: 5
  last_login_at: <%= Time.current %>
  last_login_ip: "127.0.0.1"
  last_login_user_agent: "Mozilla/5.0"
  created_by: "system"

inactive_agent:
  name: "InactiveAgent"
  nickname: "Inactive"
  email_address: "<EMAIL>"
  mobile: "13800138002"
  password_digest: <%= password_digest %>
  status: inactived
  timezone: "Asia/Shanghai"
  login_count: 0
  created_by: "system"

deleted_agent:
  name: "DeletedAgent"
  nickname: "Deleted"
  email_address: "<EMAIL>"
  mobile: "13800138003"
  password_digest: <%= password_digest %>
  status: actived
  timezone: "UTC"
  login_count: 10
  deleted_at: <%= Time.current %>
  created_by: "system"
