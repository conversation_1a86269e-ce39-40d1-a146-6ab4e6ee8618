one:
  name: Parent Menu
  title: 父菜单
  icon: mdi:folder
  menu_type: menu
  route_name: parent_menu
  path: /parent
  component: layout.default
  order: 1
  status: active
  description: Parent menu for testing
  parent_id: 

two:
  name: Child Menu
  title: 子菜单
  icon: mdi:file
  menu_type: menu
  route_name: child_menu
  path: /parent/child
  component: layout.default$view.child
  order: 1
  status: active
  description: Child menu for testing
  parent_id: one

dashboard:
  name: Dashboard
  title: 首页
  icon: mdi:dashboard
  menu_type: menu
  route_name: dashboard
  path: /dashboard
  component: layout.default$view.dashboard
  order: 2
  status: active
  description: Main dashboard page
  parent_id: 

users:
  name: Users
  title: 用户
  icon: mdi:account-group
  menu_type: menu
  route_name: users
  path: /users
  component: layout.default$view.users
  order: 3
  status: active
  description: User management page
  parent_id: 