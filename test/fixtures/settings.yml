# 系统设置
system_setting:
  key: system_title
  label: 系统标题
  value: Catalyst Admin
  value_type: string
  description: 系统标题设置
  status: 0
  sort: 0
  section: system
  created_by: system

# 充值设置
recharge_setting:
  key: recharge_min
  label: 最小充值金额
  value: 100
  value_type: integer
  description: 最小充值金额设置
  status: 0
  sort: 0
  section: recharge
  created_by: system

# 提现设置
withdraw_setting:
  key: withdraw_fee_rate
  label: 提现手续费率
  value: 0.01
  value_type: float
  description: 提现手续费率设置
  status: 0
  sort: 0
  section: withdraw
  created_by: system

# 布尔类型设置
boolean_setting:
  key: maintenance_mode
  label: 维护模式
  value: 0
  value_type: boolean
  description: 系统维护模式开关
  status: 0
  sort: 0
  section: system
  created_by: system

# 数组类型设置
array_setting:
  key: allowed_ips
  label: 允许的IP地址
  value: ***********,***********
  value_type: array
  description: 允许访问的IP地址列表
  status: 0
  sort: 0
  section: security
  created_by: system

# JSON类型设置
json_setting:
  key: api_config
  label: API配置
  value: '{"timeout": 30, "retry": 3}'
  value_type: json
  description: API接口配置信息
  status: 0
  sort: 0
  section: api
  created_by: system 