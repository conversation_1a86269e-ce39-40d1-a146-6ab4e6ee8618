// alpinejs@3.14.9 downloaded from https://ga.jspm.io/npm:alpinejs@3.14.9/dist/module.esm.js

var e=false;var t=false;var n=[];var r=-1;function scheduler(e){queueJob(e)}function queueJob(e){n.includes(e)||n.push(e);queueFlush()}function dequeueJob(e){let t=n.indexOf(e);t!==-1&&t>r&&n.splice(t,1)}function queueFlush(){if(!t&&!e){e=true;queueMicrotask(flushJobs)}}function flushJobs(){e=false;t=true;for(let e=0;e<n.length;e++){n[e]();r=e}n.length=0;r=-1;t=false}var i;var a;var o;var s;var l=true;function disableEffectScheduling(e){l=false;e();l=true}function setReactivityEngine(e){i=e.reactive;o=e.release;a=t=>e.effect(t,{scheduler:e=>{l?scheduler(e):e()}});s=e.raw}function overrideEffect(e){a=e}function elementBoundEffect(e){let cleanup2=()=>{};let wrappedEffect=t=>{let n=a(t);if(!e._x_effects){e._x_effects=new Set;e._x_runEffects=()=>{e._x_effects.forEach((e=>e()))}}e._x_effects.add(n);cleanup2=()=>{if(n!==void 0){e._x_effects.delete(n);o(n)}};return n};return[wrappedEffect,()=>{cleanup2()}]}function watch(e,t){let n=true;let r;let i=a((()=>{let i=e();JSON.stringify(i);n?r=i:queueMicrotask((()=>{t(i,r);r=i}));n=false}));return()=>o(i)}var c=[];var u=[];var f=[];function onElAdded(e){f.push(e)}function onElRemoved(e,t){if(typeof t==="function"){e._x_cleanups||(e._x_cleanups=[]);e._x_cleanups.push(t)}else{t=e;u.push(t)}}function onAttributesAdded(e){c.push(e)}function onAttributeRemoved(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={});e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]);e._x_attributeCleanups[t].push(n)}function cleanupAttributes(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach((([n,r])=>{if(t===void 0||t.includes(n)){r.forEach((e=>e()));delete e._x_attributeCleanups[n]}}))}function cleanupElement(e){e._x_effects?.forEach(dequeueJob);while(e._x_cleanups?.length)e._x_cleanups.pop()()}var d=new MutationObserver(onMutate);var p=false;function startObservingMutations(){d.observe(document,{subtree:true,childList:true,attributes:true,attributeOldValue:true});p=true}function stopObservingMutations(){flushObserver();d.disconnect();p=false}var _=[];function flushObserver(){let e=d.takeRecords();_.push((()=>e.length>0&&onMutate(e)));let t=_.length;queueMicrotask((()=>{if(_.length===t)while(_.length>0)_.shift()()}))}function mutateDom(e){if(!p)return e();stopObservingMutations();let t=e();startObservingMutations();return t}var g=false;var m=[];function deferMutations(){g=true}function flushAndStopDeferringMutations(){g=false;onMutate(m);m=[]}function onMutate(e){if(g){m=m.concat(e);return}let t=[];let n=new Set;let r=new Map;let i=new Map;for(let a=0;a<e.length;a++)if(!e[a].target._x_ignoreMutationObserver){if(e[a].type==="childList"){e[a].removedNodes.forEach((e=>{e.nodeType===1&&e._x_marker&&n.add(e)}));e[a].addedNodes.forEach((e=>{e.nodeType===1&&(n.has(e)?n.delete(e):e._x_marker||t.push(e))}))}if(e[a].type==="attributes"){let t=e[a].target;let n=e[a].attributeName;let o=e[a].oldValue;let add2=()=>{r.has(t)||r.set(t,[]);r.get(t).push({name:n,value:t.getAttribute(n)})};let remove=()=>{i.has(t)||i.set(t,[]);i.get(t).push(n)};if(t.hasAttribute(n)&&o===null)add2();else if(t.hasAttribute(n)){remove();add2()}else remove()}}i.forEach(((e,t)=>{cleanupAttributes(t,e)}));r.forEach(((e,t)=>{c.forEach((n=>n(t,e)))}));for(let e of n)t.some((t=>t.contains(e)))||u.forEach((t=>t(e)));for(let e of t)e.isConnected&&f.forEach((t=>t(e)));t=null;n=null;r=null;i=null}function scope(e){return mergeProxies(closestDataStack(e))}function addScopeToNode(e,t,n){e._x_dataStack=[t,...closestDataStack(n||e)];return()=>{e._x_dataStack=e._x_dataStack.filter((e=>e!==t))}}function closestDataStack(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot==="function"&&e instanceof ShadowRoot?closestDataStack(e.host):e.parentNode?closestDataStack(e.parentNode):[]}function mergeProxies(e){return new Proxy({objects:e},h)}var h={ownKeys({objects:e}){return Array.from(new Set(e.flatMap((e=>Object.keys(e)))))},has({objects:e},t){return t!=Symbol.unscopables&&e.some((e=>Object.prototype.hasOwnProperty.call(e,t)||Reflect.has(e,t)))},get({objects:e},t,n){return t=="toJSON"?collapseProxies:Reflect.get(e.find((e=>Reflect.has(e,t)))||{},t,n)},set({objects:e},t,n,r){const i=e.find((e=>Object.prototype.hasOwnProperty.call(e,t)))||e[e.length-1];const a=Object.getOwnPropertyDescriptor(i,t);return a?.set&&a?.get?a.set.call(r,n)||true:Reflect.set(i,t,n)}};function collapseProxies(){let e=Reflect.ownKeys(this);return e.reduce(((e,t)=>{e[t]=Reflect.get(this,t);return e}),{})}function initInterceptors(e){let isObject2=e=>typeof e==="object"&&!Array.isArray(e)&&e!==null;let recurse=(t,n="")=>{Object.entries(Object.getOwnPropertyDescriptors(t)).forEach((([r,{value:i,enumerable:a}])=>{if(a===false||i===void 0)return;if(typeof i==="object"&&i!==null&&i.__v_skip)return;let o=n===""?r:`${n}.${r}`;typeof i==="object"&&i!==null&&i._x_interceptor?t[r]=i.initialize(e,o,r):!isObject2(i)||i===t||i instanceof Element||recurse(i,o)}))};return recurse(e)}function interceptor(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:true,initialize(t,n,r){return e(this.initialValue,(()=>get(t,n)),(e=>set(t,n,e)),n,r)}};t(n);return e=>{if(typeof e==="object"&&e!==null&&e._x_interceptor){let t=n.initialize.bind(n);n.initialize=(r,i,a)=>{let o=e.initialize(r,i,a);n.initialValue=o;return t(r,i,a)}}else n.initialValue=e;return n}}function get(e,t){return t.split(".").reduce(((e,t)=>e[t]),e)}function set(e,t,n){typeof t==="string"&&(t=t.split("."));if(t.length!==1){if(t.length===0)throw error;if(e[t[0]])return set(e[t[0]],t.slice(1),n);e[t[0]]={};return set(e[t[0]],t.slice(1),n)}e[t[0]]=n}var v={};function magic(e,t){v[e]=t}function injectMagics(e,t){let n=getUtilities(t);Object.entries(v).forEach((([r,i])=>{Object.defineProperty(e,`$${r}`,{get(){return i(t,n)},enumerable:false})}));return e}function getUtilities(e){let[t,n]=getElementBoundUtilities(e);let r={interceptor:interceptor,...t};onElRemoved(e,n);return r}function tryCatch(e,t,n,...r){try{return n(...r)}catch(n){handleError(n,e,t)}}function handleError(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n});console.warn(`Alpine Expression Error: ${e.message}\n\n${n?'Expression: "'+n+'"\n\n':""}`,t);setTimeout((()=>{throw e}),0)}var y=true;function dontAutoEvaluateFunctions(e){let t=y;y=false;let n=e();y=t;return n}function evaluate(e,t,n={}){let r;evaluateLater(e,t)((e=>r=e),n);return r}function evaluateLater(...e){return x(...e)}var x=normalEvaluator;function setEvaluator(e){x=e}function normalEvaluator(e,t){let n={};injectMagics(n,e);let r=[n,...closestDataStack(e)];let i=typeof t==="function"?generateEvaluatorFromFunction(r,t):generateEvaluatorFromString(r,t,e);return tryCatch.bind(null,e,t,i)}function generateEvaluatorFromFunction(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let a=t.apply(mergeProxies([r,...e]),i);runIfTypeOfFunction(n,a)}}var b={};function generateFunctionFromString(e,t){if(b[e])return b[e];let n=Object.getPrototypeOf((async function(){})).constructor;let r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e;const safeAsyncFunction=()=>{try{let t=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);Object.defineProperty(t,"name",{value:`[Alpine] ${e}`});return t}catch(n){handleError(n,t,e);return Promise.resolve()}};let i=safeAsyncFunction();b[e]=i;return i}function generateEvaluatorFromString(e,t,n){let r=generateFunctionFromString(t,n);return(i=()=>{},{scope:a={},params:o=[]}={})=>{r.result=void 0;r.finished=false;let s=mergeProxies([a,...e]);if(typeof r==="function"){let e=r(r,s).catch((e=>handleError(e,n,t)));if(r.finished){runIfTypeOfFunction(i,r.result,s,o,n);r.result=void 0}else e.then((e=>{runIfTypeOfFunction(i,e,s,o,n)})).catch((e=>handleError(e,n,t))).finally((()=>r.result=void 0))}}}function runIfTypeOfFunction(e,t,n,r,i){if(y&&typeof t==="function"){let a=t.apply(n,r);a instanceof Promise?a.then((t=>runIfTypeOfFunction(e,t,n,r))).catch((e=>handleError(e,i,t))):e(a)}else typeof t==="object"&&t instanceof Promise?t.then((t=>e(t))):e(t)}var w="x-";function prefix(e=""){return w+e}function setPrefix(e){w=e}var k={};function directive(e,t){k[e]=t;return{before(t){if(!k[t]){console.warn(String.raw`Cannot find directive \`${t}\`. \`${e}\` will use the default order of execution`);return}const n=R.indexOf(t);R.splice(n>=0?n:R.indexOf("DEFAULT"),0,e)}}}function directiveExists(e){return Object.keys(k).includes(e)}function directives(e,t,n){t=Array.from(t);if(e._x_virtualDirectives){let n=Object.entries(e._x_virtualDirectives).map((([e,t])=>({name:e,value:t})));let r=attributesOnly(n);n=n.map((e=>r.find((t=>t.name===e.name))?{name:`x-bind:${e.name}`,value:`"${e.value}"`}:e));t=t.concat(n)}let r={};let i=t.map(toTransformedAttributes(((e,t)=>r[e]=t))).filter(outNonAlpineAttributes).map(toParsedDirectives(r,n)).sort(byPriority);return i.map((t=>getDirectiveHandler(e,t)))}function attributesOnly(e){return Array.from(e).map(toTransformedAttributes()).filter((e=>!outNonAlpineAttributes(e)))}var E=false;var S=new Map;var A=Symbol();function deferHandlingDirectives(e){E=true;let t=Symbol();A=t;S.set(t,[]);let flushHandlers=()=>{while(S.get(t).length)S.get(t).shift()();S.delete(t)};let stopDeferring=()=>{E=false;flushHandlers()};e(flushHandlers);stopDeferring()}function getElementBoundUtilities(e){let t=[];let cleanup2=e=>t.push(e);let[n,r]=elementBoundEffect(e);t.push(r);let i={Alpine:J,effect:n,cleanup:cleanup2,evaluateLater:evaluateLater.bind(evaluateLater,e),evaluate:evaluate.bind(evaluate,e)};let doCleanup=()=>t.forEach((e=>e()));return[i,doCleanup]}function getDirectiveHandler(e,t){let noop=()=>{};let n=k[t.type]||noop;let[r,i]=getElementBoundUtilities(e);onAttributeRemoved(e,t.original,i);let fullHandler=()=>{if(!e._x_ignore&&!e._x_ignoreSelf){n.inline&&n.inline(e,t,r);n=n.bind(n,e,t,r);E?S.get(A).push(n):n()}};fullHandler.runCleanups=i;return fullHandler}var startingWith=(e,t)=>({name:n,value:r})=>{n.startsWith(e)&&(n=n.replace(e,t));return{name:n,value:r}};var into=e=>e;function toTransformedAttributes(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=C.reduce(((e,t)=>t(e)),{name:t,value:n});r!==t&&e(r,t);return{name:r,value:i}}}var C=[];function mapAttributes(e){C.push(e)}function outNonAlpineAttributes({name:e}){return alpineAttributeRegex().test(e)}var alpineAttributeRegex=()=>new RegExp(`^${w}([^:^.]+)\\b`);function toParsedDirectives(e,t){return({name:n,value:r})=>{let i=n.match(alpineAttributeRegex());let a=n.match(/:([a-zA-Z0-9\-_:]+)/);let o=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[];let s=t||e[n]||n;return{type:i?i[1]:null,value:a?a[1]:null,modifiers:o.map((e=>e.replace(".",""))),expression:r,original:s}}}var O="DEFAULT";var R=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",O,"teleport"];function byPriority(e,t){let n=R.indexOf(e.type)===-1?O:e.type;let r=R.indexOf(t.type)===-1?O:t.type;return R.indexOf(n)-R.indexOf(r)}function dispatch(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:true,composed:true,cancelable:true}))}function walk(e,t){if(typeof ShadowRoot==="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach((e=>walk(e,t)));return}let n=false;t(e,(()=>n=true));if(n)return;let r=e.firstElementChild;while(r){walk(r,t,false);r=r.nextElementSibling}}function warn(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var T=false;function start(){T&&warn("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems.");T=true;document.body||warn("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?");dispatch(document,"alpine:init");dispatch(document,"alpine:initializing");startObservingMutations();onElAdded((e=>initTree(e,walk)));onElRemoved((e=>destroyTree(e)));onAttributesAdded(((e,t)=>{directives(e,t).forEach((e=>e()))}));let outNestedComponents=e=>!closestRoot(e.parentElement,true);Array.from(document.querySelectorAll(allSelectors().join(","))).filter(outNestedComponents).forEach((e=>{initTree(e)}));dispatch(document,"alpine:initialized");setTimeout((()=>{warnAboutMissingPlugins()}))}var M=[];var j=[];function rootSelectors(){return M.map((e=>e()))}function allSelectors(){return M.concat(j).map((e=>e()))}function addRootSelector(e){M.push(e)}function addInitSelector(e){j.push(e)}function closestRoot(e,t=false){return findClosest(e,(e=>{const n=t?allSelectors():rootSelectors();if(n.some((t=>e.matches(t))))return true}))}function findClosest(e,t){if(e){if(t(e))return e;e._x_teleportBack&&(e=e._x_teleportBack);if(e.parentElement)return findClosest(e.parentElement,t)}}function isRoot(e){return rootSelectors().some((t=>e.matches(t)))}var P=[];function interceptInit(e){P.push(e)}var D=1;function initTree(e,t=walk,n=()=>{}){findClosest(e,(e=>e._x_ignore))||deferHandlingDirectives((()=>{t(e,((e,t)=>{if(!e._x_marker){n(e,t);P.forEach((n=>n(e,t)));directives(e,e.attributes).forEach((e=>e()));e._x_ignore||(e._x_marker=D++);e._x_ignore&&t()}}))}))}function destroyTree(e,t=walk){t(e,(e=>{cleanupElement(e);cleanupAttributes(e);delete e._x_marker}))}function warnAboutMissingPlugins(){let e=[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]];e.forEach((([e,t,n])=>{directiveExists(t)||n.some((t=>{if(document.querySelector(t)){warn(`found "${t}", but missing ${e} plugin`);return true}}))}))}var N=[];var I=false;function nextTick(e=()=>{}){queueMicrotask((()=>{I||setTimeout((()=>{releaseNextTicks()}))}));return new Promise((t=>{N.push((()=>{e();t()}))}))}function releaseNextTicks(){I=false;while(N.length)N.shift()()}function holdNextTicks(){I=true}function setClasses(e,t){return Array.isArray(t)?setClassesFromString(e,t.join(" ")):typeof t==="object"&&t!==null?setClassesFromObject(e,t):typeof t==="function"?setClasses(e,t()):setClassesFromString(e,t)}function setClassesFromString(e,t){let missingClasses=t=>t.split(" ").filter((t=>!e.classList.contains(t))).filter(Boolean);let addClassesAndReturnUndo=t=>{e.classList.add(...t);return()=>{e.classList.remove(...t)}};t=t===true?t="":t||"";return addClassesAndReturnUndo(missingClasses(t))}function setClassesFromObject(e,t){let split=e=>e.split(" ").filter(Boolean);let n=Object.entries(t).flatMap((([e,t])=>!!t&&split(e))).filter(Boolean);let r=Object.entries(t).flatMap((([e,t])=>!t&&split(e))).filter(Boolean);let i=[];let a=[];r.forEach((t=>{if(e.classList.contains(t)){e.classList.remove(t);a.push(t)}}));n.forEach((t=>{if(!e.classList.contains(t)){e.classList.add(t);i.push(t)}}));return()=>{a.forEach((t=>e.classList.add(t)));i.forEach((t=>e.classList.remove(t)))}}function setStyles(e,t){return typeof t==="object"&&t!==null?setStylesFromObject(e,t):setStylesFromString(e,t)}function setStylesFromObject(e,t){let n={};Object.entries(t).forEach((([t,r])=>{n[t]=e.style[t];t.startsWith("--")||(t=kebabCase(t));e.style.setProperty(t,r)}));setTimeout((()=>{e.style.length===0&&e.removeAttribute("style")}));return()=>{setStyles(e,n)}}function setStylesFromString(e,t){let n=e.getAttribute("style",t);e.setAttribute("style",t);return()=>{e.setAttribute("style",n||"")}}function kebabCase(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function once(e,t=()=>{}){let n=false;return function(){if(n)t.apply(this,arguments);else{n=true;e.apply(this,arguments)}}}directive("transition",((e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r==="function"&&(r=i(r));r!==false&&(r&&typeof r!=="boolean"?registerTransitionsFromClassString(e,r,t):registerTransitionsFromHelper(e,n,t))}));function registerTransitionsFromClassString(e,t,n){registerTransitionObject(e,setClasses,"");let r={enter:t=>{e._x_transition.enter.during=t},"enter-start":t=>{e._x_transition.enter.start=t},"enter-end":t=>{e._x_transition.enter.end=t},leave:t=>{e._x_transition.leave.during=t},"leave-start":t=>{e._x_transition.leave.start=t},"leave-end":t=>{e._x_transition.leave.end=t}};r[n](t)}function registerTransitionsFromHelper(e,t,n){registerTransitionObject(e,setStyles);let r=!t.includes("in")&&!t.includes("out")&&!n;let i=r||t.includes("in")||["enter"].includes(n);let a=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter(((e,n)=>n<t.indexOf("out"))));t.includes("out")&&!r&&(t=t.filter(((e,n)=>n>t.indexOf("out"))));let o=!t.includes("opacity")&&!t.includes("scale");let s=o||t.includes("opacity");let l=o||t.includes("scale");let c=s?0:1;let u=l?modifierValue(t,"scale",95)/100:1;let f=modifierValue(t,"delay",0)/1e3;let d=modifierValue(t,"origin","center");let p="opacity, transform";let _=modifierValue(t,"duration",150)/1e3;let g=modifierValue(t,"duration",75)/1e3;let m="cubic-bezier(0.4, 0.0, 0.2, 1)";if(i){e._x_transition.enter.during={transformOrigin:d,transitionDelay:`${f}s`,transitionProperty:p,transitionDuration:`${_}s`,transitionTimingFunction:m};e._x_transition.enter.start={opacity:c,transform:`scale(${u})`};e._x_transition.enter.end={opacity:1,transform:"scale(1)"}}if(a){e._x_transition.leave.during={transformOrigin:d,transitionDelay:`${f}s`,transitionProperty:p,transitionDuration:`${g}s`,transitionTimingFunction:m};e._x_transition.leave.start={opacity:1,transform:"scale(1)"};e._x_transition.leave.end={opacity:c,transform:`scale(${u})`}}}function registerTransitionObject(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(n=()=>{},r=()=>{}){transition(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},n,r)},out(n=()=>{},r=()=>{}){transition(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},n,r)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let clickAwayCompatibleShow=()=>i(n);if(t)e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):clickAwayCompatibleShow():e._x_transition?e._x_transition.in(n):clickAwayCompatibleShow();else{e._x_hidePromise=e._x_transition?new Promise(((t,n)=>{e._x_transition.out((()=>{}),(()=>t(r)));e._x_transitioning&&e._x_transitioning.beforeCancel((()=>n({isFromCancelledTransition:true})))})):Promise.resolve(r);queueMicrotask((()=>{let t=closestHide(e);if(t){t._x_hideChildren||(t._x_hideChildren=[]);t._x_hideChildren.push(e)}else i((()=>{let hideAfterChildren=e=>{let t=Promise.all([e._x_hidePromise,...(e._x_hideChildren||[]).map(hideAfterChildren)]).then((([e])=>e?.()));delete e._x_hidePromise;delete e._x_hideChildren;return t};hideAfterChildren(e).catch((e=>{if(!e.isFromCancelledTransition)throw e}))}))}))}};function closestHide(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:closestHide(t)}function transition(e,t,{during:n,start:r,end:i}={},a=()=>{},o=()=>{}){e._x_transitioning&&e._x_transitioning.cancel();if(Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){a();o();return}let s,l,c;performTransition(e,{start(){s=t(e,r)},during(){l=t(e,n)},before:a,end(){s();c=t(e,i)},after:o,cleanup(){l();c()}})}function performTransition(e,t){let n,r,i;let a=once((()=>{mutateDom((()=>{n=true;r||t.before();if(!i){t.end();releaseNextTicks()}t.after();e.isConnected&&t.cleanup();delete e._x_transitioning}))}));e._x_transitioning={beforeCancels:[],beforeCancel(e){this.beforeCancels.push(e)},cancel:once((function(){while(this.beforeCancels.length)this.beforeCancels.shift()();a()})),finish:a};mutateDom((()=>{t.start();t.during()}));holdNextTicks();requestAnimationFrame((()=>{if(n)return;let a=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3;let o=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;a===0&&(a=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3);mutateDom((()=>{t.before()}));r=true;requestAnimationFrame((()=>{if(!n){mutateDom((()=>{t.end()}));releaseNextTicks();setTimeout(e._x_transitioning.finish,a+o);i=true}}))}))}function modifierValue(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r)return n;if(t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let e=r.match(/([0-9]+)ms/);if(e)return e[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var $=false;function skipDuringClone(e,t=()=>{}){return(...n)=>$?t(...n):e(...n)}function onlyDuringClone(e){return(...t)=>$&&e(...t)}var L=[];function interceptClone(e){L.push(e)}function cloneNode(e,t){L.forEach((n=>n(e,t)));$=true;dontRegisterReactiveSideEffects((()=>{initTree(t,((e,t)=>{t(e,(()=>{}))}))}));$=false}var F=false;function clone(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack);$=true;F=true;dontRegisterReactiveSideEffects((()=>{cloneTree(t)}));$=false;F=false}function cloneTree(e){let t=false;let shallowWalker=(e,n)=>{walk(e,((e,r)=>{if(t&&isRoot(e))return r();t=true;n(e,r)}))};initTree(e,shallowWalker)}function dontRegisterReactiveSideEffects(e){let t=a;overrideEffect(((e,n)=>{let r=t(e);o(r);return()=>{}}));e();overrideEffect(t)}function bind(e,t,n,r=[]){e._x_bindings||(e._x_bindings=i({}));e._x_bindings[t]=n;t=r.includes("camel")?camelCase(t):t;switch(t){case"value":bindInputValue(e,n);break;case"style":bindStyles(e,n);break;case"class":bindClasses(e,n);break;case"selected":case"checked":bindAttributeAndProperty(e,t,n);break;default:bindAttribute(e,t,n);break}}function bindInputValue(e,t){if(isRadio(e)){e.attributes.value===void 0&&(e.value=t);window.fromModel&&(e.checked=typeof t==="boolean"?safeParseBoolean(e.value)===t:checkedAttrLooseCompare(e.value,t))}else if(isCheckbox(e))Number.isInteger(t)?e.value=t:Array.isArray(t)||typeof t==="boolean"||[null,void 0].includes(t)?Array.isArray(t)?e.checked=t.some((t=>checkedAttrLooseCompare(t,e.value))):e.checked=!!t:e.value=String(t);else if(e.tagName==="SELECT")updateSelect(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function bindClasses(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses();e._x_undoAddedClasses=setClasses(e,t)}function bindStyles(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles();e._x_undoAddedStyles=setStyles(e,t)}function bindAttributeAndProperty(e,t,n){bindAttribute(e,t,n);setPropertyIfChanged(e,t,n)}function bindAttribute(e,t,n){if([null,void 0,false].includes(n)&&attributeShouldntBePreservedIfFalsy(t))e.removeAttribute(t);else{isBooleanAttr(t)&&(n=t);setIfChanged(e,t,n)}}function setIfChanged(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function setPropertyIfChanged(e,t,n){e[t]!==n&&(e[t]=n)}function updateSelect(e,t){const n=[].concat(t).map((e=>e+""));Array.from(e.options).forEach((e=>{e.selected=n.includes(e.value)}))}function camelCase(e){return e.toLowerCase().replace(/-(\w)/g,((e,t)=>t.toUpperCase()))}function checkedAttrLooseCompare(e,t){return e==t}function safeParseBoolean(e){return!![1,"1","true","on","yes",true].includes(e)||![0,"0","false","off","no",false].includes(e)&&(e?Boolean(e):null)}var B=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function isBooleanAttr(e){return B.has(e)}function attributeShouldntBePreservedIfFalsy(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function getBinding(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:getAttributeBinding(e,t,n)}function extractProp(e,t,n,r=true){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let n=e._x_inlineBindings[t];n.extract=r;return dontAutoEvaluateFunctions((()=>evaluate(e,n.expression)))}return getAttributeBinding(e,t,n)}function getAttributeBinding(e,t,n){let r=e.getAttribute(t);return r===null?typeof n==="function"?n():n:r===""||(isBooleanAttr(t)?!![t,"true"].includes(r):r)}function isCheckbox(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function isRadio(e){return e.type==="radio"||e.localName==="ui-radio"}function debounce(e,t){var n;return function(){var r=this,i=arguments;var later=function(){n=null;e.apply(r,i)};clearTimeout(n);n=setTimeout(later,t)}}function throttle(e,t){let n;return function(){let r=this,i=arguments;if(!n){e.apply(r,i);n=true;setTimeout((()=>n=false),t)}}}function entangle({get:e,set:t},{get:n,set:r}){let i=true;let s;let l;let c=a((()=>{let a=e();let o=n();if(i){r(cloneIfObject(a));i=false}else{let e=JSON.stringify(a);let n=JSON.stringify(o);e!==s?r(cloneIfObject(a)):e!==n&&t(cloneIfObject(o))}s=JSON.stringify(e());l=JSON.stringify(n())}));return()=>{o(c)}}function cloneIfObject(e){return typeof e==="object"?JSON.parse(JSON.stringify(e)):e}function plugin(e){let t=Array.isArray(e)?e:[e];t.forEach((e=>e(J)))}var z={};var V=false;function store(e,t){if(!V){z=i(z);V=true}if(t===void 0)return z[e];z[e]=t;initInterceptors(z[e]);typeof t==="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init==="function"&&z[e].init()}function getStores(){return z}var q={};function bind2(e,t){let n=typeof t!=="function"?()=>t:t;if(e instanceof Element)return applyBindingsObject(e,n());q[e]=n;return()=>{}}function injectBindingProviders(e){Object.entries(q).forEach((([t,n])=>{Object.defineProperty(e,t,{get(){return(...e)=>n(...e)}})}));return e}function applyBindingsObject(e,t,n){let r=[];while(r.length)r.pop()();let i=Object.entries(t).map((([e,t])=>({name:e,value:t})));let a=attributesOnly(i);i=i.map((e=>a.find((t=>t.name===e.name))?{name:`x-bind:${e.name}`,value:`"${e.value}"`}:e));directives(e,i,n).map((e=>{r.push(e.runCleanups);e()}));return()=>{while(r.length)r.pop()()}}var K={};function data(e,t){K[e]=t}function injectDataProviders(e,t){Object.entries(K).forEach((([n,r])=>{Object.defineProperty(e,n,{get(){return(...e)=>r.bind(t)(...e)},enumerable:false})}));return e}var H={get reactive(){return i},get release(){return o},get effect(){return a},get raw(){return s},version:"3.14.9",flushAndStopDeferringMutations:flushAndStopDeferringMutations,dontAutoEvaluateFunctions:dontAutoEvaluateFunctions,disableEffectScheduling:disableEffectScheduling,startObservingMutations:startObservingMutations,stopObservingMutations:stopObservingMutations,setReactivityEngine:setReactivityEngine,onAttributeRemoved:onAttributeRemoved,onAttributesAdded:onAttributesAdded,closestDataStack:closestDataStack,skipDuringClone:skipDuringClone,onlyDuringClone:onlyDuringClone,addRootSelector:addRootSelector,addInitSelector:addInitSelector,interceptClone:interceptClone,addScopeToNode:addScopeToNode,deferMutations:deferMutations,mapAttributes:mapAttributes,evaluateLater:evaluateLater,interceptInit:interceptInit,setEvaluator:setEvaluator,mergeProxies:mergeProxies,extractProp:extractProp,findClosest:findClosest,onElRemoved:onElRemoved,closestRoot:closestRoot,destroyTree:destroyTree,interceptor:interceptor,transition:transition,setStyles:setStyles,mutateDom:mutateDom,directive:directive,entangle:entangle,throttle:throttle,debounce:debounce,evaluate:evaluate,initTree:initTree,nextTick:nextTick,prefixed:prefix,prefix:setPrefix,plugin:plugin,magic:magic,store:store,start:start,clone:clone,cloneNode:cloneNode,bound:getBinding,$data:scope,watch:watch,walk:walk,data:data,bind:bind2};var J=H;function makeMap(e,t){const n=Object.create(null);const r=e.split(",");for(let e=0;e<r.length;e++)n[r[e]]=true;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}var U="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly";makeMap(U+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected");var W=Object.freeze({});Object.freeze([]);var G=Object.prototype.hasOwnProperty;var hasOwn=(e,t)=>G.call(e,t);var X=Array.isArray;var isMap=e=>toTypeString(e)==="[object Map]";var isString=e=>typeof e==="string";var isSymbol=e=>typeof e==="symbol";var isObject=e=>e!==null&&typeof e==="object";var Z=Object.prototype.toString;var toTypeString=e=>Z.call(e);var toRawType=e=>toTypeString(e).slice(8,-1);var isIntegerKey=e=>isString(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e;var cacheStringFunction=e=>{const t=Object.create(null);return n=>{const r=t[n];return r||(t[n]=e(n))}};var Y=/-(\w)/g;cacheStringFunction((e=>e.replace(Y,((e,t)=>t?t.toUpperCase():""))));var Q=/\B([A-Z])/g;cacheStringFunction((e=>e.replace(Q,"-$1").toLowerCase()));var ee=cacheStringFunction((e=>e.charAt(0).toUpperCase()+e.slice(1)));cacheStringFunction((e=>e?`on${ee(e)}`:""));var hasChanged=(e,t)=>e!==t&&(e===e||t===t);var te=new WeakMap;var ne=[];var re;var ie=Symbol("iterate");var ae=Symbol("Map key iterate");function isEffect(e){return e&&e._isEffect===true}function effect2(e,t=W){isEffect(e)&&(e=e.raw);const n=createReactiveEffect(e,t);t.lazy||n();return n}function stop(e){if(e.active){cleanup(e);e.options.onStop&&e.options.onStop();e.active=false}}var oe=0;function createReactiveEffect(e,t){const n=function reactiveEffect(){if(!n.active)return e();if(!ne.includes(n)){cleanup(n);try{enableTracking();ne.push(n);re=n;return e()}finally{ne.pop();resetTracking();re=ne[ne.length-1]}}};n.id=oe++;n.allowRecurse=!!t.allowRecurse;n._isEffect=true;n.active=true;n.raw=e;n.deps=[];n.options=t;return n}function cleanup(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var se=true;var le=[];function pauseTracking(){le.push(se);se=false}function enableTracking(){le.push(se);se=true}function resetTracking(){const e=le.pop();se=e===void 0||e}function track(e,t,n){if(!se||re===void 0)return;let r=te.get(e);r||te.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set);if(!i.has(re)){i.add(re);re.deps.push(i);re.options.onTrack&&re.options.onTrack({effect:re,target:e,type:t,key:n})}}function trigger(e,t,n,r,i,a){const o=te.get(e);if(!o)return;const s=new Set;const add2=e=>{e&&e.forEach((e=>{(e!==re||e.allowRecurse)&&s.add(e)}))};if(t==="clear")o.forEach(add2);else if(n==="length"&&X(e))o.forEach(((e,t)=>{(t==="length"||t>=r)&&add2(e)}));else{n!==void 0&&add2(o.get(n));switch(t){case"add":if(X(e))isIntegerKey(n)&&add2(o.get("length"));else{add2(o.get(ie));isMap(e)&&add2(o.get(ae))}break;case"delete":if(!X(e)){add2(o.get(ie));isMap(e)&&add2(o.get(ae))}break;case"set":isMap(e)&&add2(o.get(ie));break}}const run=o=>{o.options.onTrigger&&o.options.onTrigger({effect:o,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:a});o.options.scheduler?o.options.scheduler(o):o()};s.forEach(run)}var ce=makeMap("__proto__,__v_isRef,__isVue");var ue=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter(isSymbol));var fe=createGetter();var de=createGetter(true);var pe=createArrayInstrumentations();function createArrayInstrumentations(){const e={};["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=toRaw(this);for(let e=0,t=this.length;e<t;e++)track(n,"get",e+"");const r=n[t](...e);return r===-1||r===false?n[t](...e.map(toRaw)):r}}));["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){pauseTracking();const n=toRaw(this)[t].apply(this,e);resetTracking();return n}}));return e}function createGetter(e=false,t=false){return function get3(n,r,i){if(r==="__v_isReactive")return!e;if(r==="__v_isReadonly")return e;if(r==="__v_raw"&&i===(e?t?Ae:Se:t?Ee:ke).get(n))return n;const a=X(n);if(!e&&a&&hasOwn(pe,r))return Reflect.get(pe,r,i);const o=Reflect.get(n,r,i);if(isSymbol(r)?ue.has(r):ce(r))return o;e||track(n,"get",r);if(t)return o;if(isRef(o)){const e=!a||!isIntegerKey(r);return e?o.value:o}return isObject(o)?e?readonly(o):reactive2(o):o}}var _e=createSetter();function createSetter(e=false){return function set3(t,n,r,i){let a=t[n];if(!e){r=toRaw(r);a=toRaw(a);if(!X(t)&&isRef(a)&&!isRef(r)){a.value=r;return true}}const o=X(t)&&isIntegerKey(n)?Number(n)<t.length:hasOwn(t,n);const s=Reflect.set(t,n,r,i);t===toRaw(i)&&(o?hasChanged(r,a)&&trigger(t,"set",n,r,a):trigger(t,"add",n,r));return s}}function deleteProperty(e,t){const n=hasOwn(e,t);const r=e[t];const i=Reflect.deleteProperty(e,t);i&&n&&trigger(e,"delete",t,void 0,r);return i}function has(e,t){const n=Reflect.has(e,t);isSymbol(t)&&ue.has(t)||track(e,"has",t);return n}function ownKeys(e){track(e,"iterate",X(e)?"length":ie);return Reflect.ownKeys(e)}var ge={get:fe,set:_e,deleteProperty:deleteProperty,has:has,ownKeys:ownKeys};var me={get:de,set(e,t){true;console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e);return true},deleteProperty(e,t){true;console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e);return true}};var toReactive=e=>isObject(e)?reactive2(e):e;var toReadonly=e=>isObject(e)?readonly(e):e;var toShallow=e=>e;var getProto=e=>Reflect.getPrototypeOf(e);function get$1(e,t,n=false,r=false){e=e.__v_raw;const i=toRaw(e);const a=toRaw(t);t!==a&&!n&&track(i,"get",t);!n&&track(i,"get",a);const{has:o}=getProto(i);const s=r?toShallow:n?toReadonly:toReactive;if(o.call(i,t))return s(e.get(t));if(o.call(i,a))return s(e.get(a));e!==i&&e.get(t)}function has$1(e,t=false){const n=this.__v_raw;const r=toRaw(n);const i=toRaw(e);e!==i&&!t&&track(r,"has",e);!t&&track(r,"has",i);return e===i?n.has(e):n.has(e)||n.has(i)}function size(e,t=false){e=e.__v_raw;!t&&track(toRaw(e),"iterate",ie);return Reflect.get(e,"size",e)}function add(e){e=toRaw(e);const t=toRaw(this);const n=getProto(t);const r=n.has.call(t,e);if(!r){t.add(e);trigger(t,"add",e,e)}return this}function set$1(e,t){t=toRaw(t);const n=toRaw(this);const{has:r,get:i}=getProto(n);let a=r.call(n,e);if(a){true;checkIdentityKeys(n,r,e)}else{e=toRaw(e);a=r.call(n,e)}const o=i.call(n,e);n.set(e,t);a?hasChanged(t,o)&&trigger(n,"set",e,t,o):trigger(n,"add",e,t);return this}function deleteEntry(e){const t=toRaw(this);const{has:n,get:r}=getProto(t);let i=n.call(t,e);if(i){true;checkIdentityKeys(t,n,e)}else{e=toRaw(e);i=n.call(t,e)}const a=r?r.call(t,e):void 0;const o=t.delete(e);i&&trigger(t,"delete",e,void 0,a);return o}function clear(){const e=toRaw(this);const t=e.size!==0;const n=isMap(e)?new Map(e):new Set(e);const r=e.clear();t&&trigger(e,"clear",void 0,void 0,n);return r}function createForEach(e,t){return function forEach(n,r){const i=this;const a=i.__v_raw;const o=toRaw(a);const s=t?toShallow:e?toReadonly:toReactive;!e&&track(o,"iterate",ie);return a.forEach(((e,t)=>n.call(r,s(e),s(t),i)))}}function createIterableMethod(e,t,n){return function(...r){const i=this.__v_raw;const a=toRaw(i);const o=isMap(a);const s=e==="entries"||e===Symbol.iterator&&o;const l=e==="keys"&&o;const c=i[e](...r);const u=n?toShallow:t?toReadonly:toReactive;!t&&track(a,"iterate",l?ae:ie);return{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){true;{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${ee(e)} operation ${n}failed: target is readonly.`,toRaw(this))}return e!=="delete"&&this}}function createInstrumentations(){const e={get(e){return get$1(this,e)},get size(){return size(this)},has:has$1,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(false,false)};const t={get(e){return get$1(this,e,false,true)},get size(){return size(this)},has:has$1,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(false,true)};const n={get(e){return get$1(this,e,true)},get size(){return size(this,true)},has(e){return has$1.call(this,e,true)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(true,false)};const r={get(e){return get$1(this,e,true,true)},get size(){return size(this,true)},has(e){return has$1.call(this,e,true)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(true,true)};const i=["keys","values","entries",Symbol.iterator];i.forEach((i=>{e[i]=createIterableMethod(i,false,false);n[i]=createIterableMethod(i,true,false);t[i]=createIterableMethod(i,false,true);r[i]=createIterableMethod(i,true,true)}));return[e,n,t,r]}var[he,ve,ye,xe]=createInstrumentations();function createInstrumentationGetter(e,t){const n=t?e?xe:ye:e?ve:he;return(t,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?t:Reflect.get(hasOwn(n,r)&&r in t?n:t,r,i)}var be={get:createInstrumentationGetter(false,false)};var we={get:createInstrumentationGetter(true,false)};function checkIdentityKeys(e,t,n){const r=toRaw(n);if(r!==n&&t.call(e,r)){const t=toRawType(e);console.warn(`Reactive ${t} contains both the raw and reactive versions of the same object${t==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var ke=new WeakMap;var Ee=new WeakMap;var Se=new WeakMap;var Ae=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive2(e){return e&&e.__v_isReadonly?e:createReactiveObject(e,false,ge,be,ke)}function readonly(e){return createReactiveObject(e,true,me,we,Se)}function createReactiveObject(e,t,n,r,i){if(!isObject(e)){true;console.warn(`value cannot be made reactive: ${String(e)}`);return e}if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=i.get(e);if(a)return a;const o=getTargetType(e);if(o===0)return e;const s=new Proxy(e,o===2?r:n);i.set(e,s);return s}function toRaw(e){return e&&toRaw(e.__v_raw)||e}function isRef(e){return Boolean(e&&e.__v_isRef===true)}magic("nextTick",(()=>nextTick));magic("dispatch",(e=>dispatch.bind(dispatch,e)));magic("watch",((e,{evaluateLater:t,cleanup:n})=>(e,r)=>{let i=t(e);let getter=()=>{let e;i((t=>e=t));return e};let a=watch(getter,r);n(a)}));magic("store",getStores);magic("data",(e=>scope(e)));magic("root",(e=>closestRoot(e)));magic("refs",(e=>{if(e._x_refs_proxy)return e._x_refs_proxy;e._x_refs_proxy=mergeProxies(getArrayOfRefObject(e));return e._x_refs_proxy}));function getArrayOfRefObject(e){let t=[];findClosest(e,(e=>{e._x_refs&&t.push(e._x_refs)}));return t}var Ce={};function findAndIncrementId(e){Ce[e]||(Ce[e]=0);return++Ce[e]}function closestIdRoot(e,t){return findClosest(e,(e=>{if(e._x_ids&&e._x_ids[t])return true}))}function setIdRoot(e,t){e._x_ids||(e._x_ids={});e._x_ids[t]||(e._x_ids[t]=findAndIncrementId(t))}magic("id",((e,{cleanup:t})=>(n,r=null)=>{let i=`${n}${r?`-${r}`:""}`;return cacheIdByNameOnElement(e,i,t,(()=>{let t=closestIdRoot(e,n);let i=t?t._x_ids[n]:findAndIncrementId(n);return r?`${n}-${i}-${r}`:`${n}-${i}`}))}));interceptClone(((e,t)=>{e._x_id&&(t._x_id=e._x_id)}));function cacheIdByNameOnElement(e,t,n,r){e._x_id||(e._x_id={});if(e._x_id[t])return e._x_id[t];let i=r();e._x_id[t]=i;n((()=>{delete e._x_id[t]}));return i}magic("el",(e=>e));warnMissingPluginMagic("Focus","focus","focus");warnMissingPluginMagic("Persist","persist","persist");function warnMissingPluginMagic(e,t,n){magic(t,(r=>warn(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r)))}directive("modelable",((e,{expression:t},{effect:n,evaluateLater:r,cleanup:i})=>{let a=r(t);let innerGet=()=>{let e;a((t=>e=t));return e};let o=r(`${t} = __placeholder`);let innerSet=e=>o((()=>{}),{scope:{__placeholder:e}});let s=innerGet();innerSet(s);queueMicrotask((()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let t=e._x_model.get;let n=e._x_model.set;let r=entangle({get(){return t()},set(e){n(e)}},{get(){return innerGet()},set(e){innerSet(e)}});i(r)}))}));directive("teleport",((e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&warn("x-teleport can only be used on a <template> tag",e);let i=getTarget(n);let a=e.content.cloneNode(true).firstElementChild;e._x_teleport=a;a._x_teleportBack=e;e.setAttribute("data-teleport-template",true);a.setAttribute("data-teleport-target",true);e._x_forwardEvents&&e._x_forwardEvents.forEach((t=>{a.addEventListener(t,(t=>{t.stopPropagation();e.dispatchEvent(new t.constructor(t.type,t))}))}));addScopeToNode(a,{},e);let placeInDom=(e,t,n)=>{n.includes("prepend")?t.parentNode.insertBefore(e,t):n.includes("append")?t.parentNode.insertBefore(e,t.nextSibling):t.appendChild(e)};mutateDom((()=>{placeInDom(a,i,t);skipDuringClone((()=>{initTree(a)}))()}));e._x_teleportPutBack=()=>{let r=getTarget(n);mutateDom((()=>{placeInDom(e._x_teleport,r,t)}))};r((()=>mutateDom((()=>{a.remove();destroyTree(a)}))))}));var Oe=document.createElement("div");function getTarget(e){let t=skipDuringClone((()=>document.querySelector(e)),(()=>Oe))();t||warn(`Cannot find x-teleport element for selector: "${e}"`);return t}var handler=()=>{};handler.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=true:e._x_ignore=true;n((()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore}))};directive("ignore",handler);directive("effect",skipDuringClone(((e,{expression:t},{effect:n})=>{n(evaluateLater(e,t))})));function on(e,t,n,r){let i=e;let handler4=e=>r(e);let a={};let wrapHandler=(e,t)=>n=>t(e,n);n.includes("dot")&&(t=dotSyntax(t));n.includes("camel")&&(t=camelCase2(t));n.includes("passive")&&(a.passive=true);n.includes("capture")&&(a.capture=true);n.includes("window")&&(i=window);n.includes("document")&&(i=document);if(n.includes("debounce")){let e=n[n.indexOf("debounce")+1]||"invalid-wait";let t=isNumeric(e.split("ms")[0])?Number(e.split("ms")[0]):250;handler4=debounce(handler4,t)}if(n.includes("throttle")){let e=n[n.indexOf("throttle")+1]||"invalid-wait";let t=isNumeric(e.split("ms")[0])?Number(e.split("ms")[0]):250;handler4=throttle(handler4,t)}n.includes("prevent")&&(handler4=wrapHandler(handler4,((e,t)=>{t.preventDefault();e(t)})));n.includes("stop")&&(handler4=wrapHandler(handler4,((e,t)=>{t.stopPropagation();e(t)})));n.includes("once")&&(handler4=wrapHandler(handler4,((e,n)=>{e(n);i.removeEventListener(t,handler4,a)})));if(n.includes("away")||n.includes("outside")){i=document;handler4=wrapHandler(handler4,((t,n)=>{e.contains(n.target)||n.target.isConnected!==false&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==false&&t(n))}))}n.includes("self")&&(handler4=wrapHandler(handler4,((t,n)=>{n.target===e&&t(n)})));(isKeyEvent(t)||isClickEvent(t))&&(handler4=wrapHandler(handler4,((e,t)=>{isListeningForASpecificKeyThatHasntBeenPressed(t,n)||e(t)})));i.addEventListener(t,handler4,a);return()=>{i.removeEventListener(t,handler4,a)}}function dotSyntax(e){return e.replace(/-/g,".")}function camelCase2(e){return e.toLowerCase().replace(/-(\w)/g,((e,t)=>t.toUpperCase()))}function isNumeric(e){return!Array.isArray(e)&&!isNaN(e)}function kebabCase2(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function isKeyEvent(e){return["keydown","keyup"].includes(e)}function isClickEvent(e){return["contextmenu","click","mouse"].some((t=>e.includes(t)))}function isListeningForASpecificKeyThatHasntBeenPressed(e,t){let n=t.filter((e=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(e)));if(n.includes("debounce")){let e=n.indexOf("debounce");n.splice(e,isNumeric((n[e+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let e=n.indexOf("throttle");n.splice(e,isNumeric((n[e+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0)return false;if(n.length===1&&keyToModifiers(e.key).includes(n[0]))return false;const r=["ctrl","shift","alt","meta","cmd","super"];const i=r.filter((e=>n.includes(e)));n=n.filter((e=>!i.includes(e)));if(i.length>0){const t=i.filter((t=>{t!=="cmd"&&t!=="super"||(t="meta");return e[`${t}Key`]}));if(t.length===i.length){if(isClickEvent(e.type))return false;if(keyToModifiers(e.key).includes(n[0]))return false}}return true}function keyToModifiers(e){if(!e)return[];e=kebabCase2(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};t[e]=e;return Object.keys(t).map((n=>{if(t[n]===e)return n})).filter((e=>e))}directive("model",((e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let a=e;t.includes("parent")&&(a=e.parentNode);let o=evaluateLater(a,n);let s;s=typeof n==="string"?evaluateLater(a,`${n} = __placeholder`):typeof n==="function"&&typeof n()==="string"?evaluateLater(a,`${n()} = __placeholder`):()=>{};let getValue=()=>{let e;o((t=>e=t));return isGetterSetter(e)?e.get():e};let setValue=e=>{let t;o((e=>t=e));isGetterSetter(t)?t.set(e):s((()=>{}),{scope:{__placeholder:e}})};typeof n==="string"&&e.type==="radio"&&mutateDom((()=>{e.hasAttribute("name")||e.setAttribute("name",n)}));var l=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let c=$?()=>{}:on(e,l,t,(n=>{setValue(getInputValue(e,t,n,getValue()))}));t.includes("fill")&&([void 0,null,""].includes(getValue())||isCheckbox(e)&&Array.isArray(getValue())||e.tagName.toLowerCase()==="select"&&e.multiple)&&setValue(getInputValue(e,t,{target:e},getValue()));e._x_removeModelListeners||(e._x_removeModelListeners={});e._x_removeModelListeners.default=c;i((()=>e._x_removeModelListeners.default()));if(e.form){let n=on(e.form,"reset",[],(n=>{nextTick((()=>e._x_model&&e._x_model.set(getInputValue(e,t,{target:e},getValue()))))}));i((()=>n()))}e._x_model={get(){return getValue()},set(e){setValue(e)}};e._x_forceModelUpdate=t=>{t===void 0&&typeof n==="string"&&n.match(/\./)&&(t="");window.fromModel=true;mutateDom((()=>bind(e,"value",t)));delete window.fromModel};r((()=>{let n=getValue();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(n)}))}));function getInputValue(e,t,n,r){return mutateDom((()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(isCheckbox(e)){if(Array.isArray(r)){let e=null;e=t.includes("number")?safeParseNumber(n.target.value):t.includes("boolean")?safeParseBoolean(n.target.value):n.target.value;return n.target.checked?r.includes(e)?r:r.concat([e]):r.filter((t=>!checkedAttrLooseCompare2(t,e)))}return n.target.checked}if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map((e=>{let t=e.value||e.text;return safeParseNumber(t)})):t.includes("boolean")?Array.from(n.target.selectedOptions).map((e=>{let t=e.value||e.text;return safeParseBoolean(t)})):Array.from(n.target.selectedOptions).map((e=>e.value||e.text));{let i;i=isRadio(e)?n.target.checked?n.target.value:r:n.target.value;return t.includes("number")?safeParseNumber(i):t.includes("boolean")?safeParseBoolean(i):t.includes("trim")?i.trim():i}}))}function safeParseNumber(e){let t=e?parseFloat(e):null;return isNumeric2(t)?t:e}function checkedAttrLooseCompare2(e,t){return e==t}function isNumeric2(e){return!Array.isArray(e)&&!isNaN(e)}function isGetterSetter(e){return e!==null&&typeof e==="object"&&typeof e.get==="function"&&typeof e.set==="function"}directive("cloak",(e=>queueMicrotask((()=>mutateDom((()=>e.removeAttribute(prefix("cloak"))))))));addInitSelector((()=>`[${prefix("init")}]`));directive("init",skipDuringClone(((e,{expression:t},{evaluate:n})=>typeof t==="string"?!!t.trim()&&n(t,{},false):n(t,{},false))));directive("text",((e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n((()=>{i((t=>{mutateDom((()=>{e.textContent=t}))}))}))}));directive("html",((e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n((()=>{i((t=>{mutateDom((()=>{e.innerHTML=t;e._x_ignoreSelf=true;initTree(e);delete e._x_ignoreSelf}))}))}))}));mapAttributes(startingWith(":",into(prefix("bind:"))));var handler2=(e,{value:t,modifiers:n,expression:r,original:i},{effect:a,cleanup:o})=>{if(!t){let t={};injectBindingProviders(t);let n=evaluateLater(e,r);n((t=>{applyBindingsObject(e,t,i)}),{scope:t});return}if(t==="key")return storeKeyForXFor(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let s=evaluateLater(e,r);a((()=>s((i=>{i===void 0&&typeof r==="string"&&r.match(/\./)&&(i="");mutateDom((()=>bind(e,t,i,n)))}))));o((()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses();e._x_undoAddedStyles&&e._x_undoAddedStyles()}))};handler2.inline=(e,{value:t,modifiers:n,expression:r})=>{if(t){e._x_inlineBindings||(e._x_inlineBindings={});e._x_inlineBindings[t]={expression:r,extract:false}}};directive("bind",handler2);function storeKeyForXFor(e,t){e._x_keyExpression=t}addRootSelector((()=>`[${prefix("data")}]`));directive("data",((e,{expression:t},{cleanup:n})=>{if(shouldSkipRegisteringDataDuringClone(e))return;t=t===""?"{}":t;let r={};injectMagics(r,e);let a={};injectDataProviders(a,r);let o=evaluate(e,t,{scope:a});o!==void 0&&o!==true||(o={});injectMagics(o,e);let s=i(o);initInterceptors(s);let l=addScopeToNode(e,s);s.init&&evaluate(e,s.init);n((()=>{s.destroy&&evaluate(e,s.destroy);l()}))}));interceptClone(((e,t)=>{if(e._x_dataStack){t._x_dataStack=e._x_dataStack;t.setAttribute("data-has-alpine-state",true)}}));function shouldSkipRegisteringDataDuringClone(e){return!!$&&(!!F||e.hasAttribute("data-has-alpine-state"))}directive("show",((e,{modifiers:t,expression:n},{effect:r})=>{let i=evaluateLater(e,n);e._x_doHide||(e._x_doHide=()=>{mutateDom((()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)}))});e._x_doShow||(e._x_doShow=()=>{mutateDom((()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")}))});let hide=()=>{e._x_doHide();e._x_isShown=false};let show=()=>{e._x_doShow();e._x_isShown=true};let clickAwayCompatibleShow=()=>setTimeout(show);let a=once((e=>e?show():hide()),(t=>{typeof e._x_toggleAndCascadeWithTransitions==="function"?e._x_toggleAndCascadeWithTransitions(e,t,show,hide):t?clickAwayCompatibleShow():hide()}));let o;let s=true;r((()=>i((e=>{if(s||e!==o){t.includes("immediate")&&(e?clickAwayCompatibleShow():hide());a(e);o=e;s=false}}))))}));directive("for",((e,{expression:t},{effect:n,cleanup:r})=>{let i=parseForExpression(t);let a=evaluateLater(e,i.items);let o=evaluateLater(e,e._x_keyExpression||"index");e._x_prevKeys=[];e._x_lookup={};n((()=>loop(e,i,a,o)));r((()=>{Object.values(e._x_lookup).forEach((e=>mutateDom((()=>{destroyTree(e);e.remove()}))));delete e._x_prevKeys;delete e._x_lookup}))}));function loop(e,t,n,r){let isObject2=e=>typeof e==="object"&&!Array.isArray(e);let a=e;n((n=>{isNumeric3(n)&&n>=0&&(n=Array.from(Array(n).keys(),(e=>e+1)));n===void 0&&(n=[]);let o=e._x_lookup;let s=e._x_prevKeys;let l=[];let c=[];if(isObject2(n))n=Object.entries(n).map((([i,a])=>{let o=getIterationScopeVariables(t,a,i,n);r((t=>{c.includes(t)&&warn("Duplicate key on x-for",e);c.push(t)}),{scope:{index:i,...o}});l.push(o)}));else for(let i=0;i<n.length;i++){let a=getIterationScopeVariables(t,n[i],i,n);r((t=>{c.includes(t)&&warn("Duplicate key on x-for",e);c.push(t)}),{scope:{index:i,...a}});l.push(a)}let u=[];let f=[];let d=[];let p=[];for(let e=0;e<s.length;e++){let t=s[e];c.indexOf(t)===-1&&d.push(t)}s=s.filter((e=>!d.includes(e)));let _="template";for(let e=0;e<c.length;e++){let t=c[e];let n=s.indexOf(t);if(n===-1){s.splice(e,0,t);u.push([_,e])}else if(n!==e){let t=s.splice(e,1)[0];let r=s.splice(n-1,1)[0];s.splice(e,0,r);s.splice(n,0,t);f.push([t,r])}else p.push(t);_=t}for(let e=0;e<d.length;e++){let t=d[e];if(t in o){mutateDom((()=>{destroyTree(o[t]);o[t].remove()}));delete o[t]}}for(let e=0;e<f.length;e++){let[t,n]=f[e];let r=o[t];let i=o[n];let s=document.createElement("div");mutateDom((()=>{i||warn('x-for ":key" is undefined or invalid',a,n,o);i.after(s);r.after(i);i._x_currentIfEl&&i.after(i._x_currentIfEl);s.before(r);r._x_currentIfEl&&r.after(r._x_currentIfEl);s.remove()}));i._x_refreshXForScope(l[c.indexOf(n)])}for(let e=0;e<u.length;e++){let[t,n]=u[e];let r=t==="template"?a:o[t];r._x_currentIfEl&&(r=r._x_currentIfEl);let s=l[n];let f=c[n];let d=document.importNode(a.content,true).firstElementChild;let p=i(s);addScopeToNode(d,p,a);d._x_refreshXForScope=e=>{Object.entries(e).forEach((([e,t])=>{p[e]=t}))};mutateDom((()=>{r.after(d);skipDuringClone((()=>initTree(d)))()}));typeof f==="object"&&warn("x-for key cannot be an object, it must be a string or an integer",a);o[f]=d}for(let e=0;e<p.length;e++)o[p[e]]._x_refreshXForScope(l[c.indexOf(p[e])]);a._x_prevKeys=c}))}function parseForExpression(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/;let n=/^\s*\(|\)\s*$/g;let r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/;let i=e.match(r);if(!i)return;let a={};a.items=i[2].trim();let o=i[1].replace(n,"").trim();let s=o.match(t);if(s){a.item=o.replace(t,"").trim();a.index=s[1].trim();s[2]&&(a.collection=s[2].trim())}else a.item=o;return a}function getIterationScopeVariables(e,t,n,r){let i={};if(/^\[.*\]$/.test(e.item)&&Array.isArray(t)){let n=e.item.replace("[","").replace("]","").split(",").map((e=>e.trim()));n.forEach(((e,n)=>{i[e]=t[n]}))}else if(/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t==="object"){let n=e.item.replace("{","").replace("}","").split(",").map((e=>e.trim()));n.forEach((e=>{i[e]=t[e]}))}else i[e.item]=t;e.index&&(i[e.index]=n);e.collection&&(i[e.collection]=r);return i}function isNumeric3(e){return!Array.isArray(e)&&!isNaN(e)}function handler3(){}handler3.inline=(e,{expression:t},{cleanup:n})=>{let r=closestRoot(e);r._x_refs||(r._x_refs={});r._x_refs[t]=e;n((()=>delete r._x_refs[t]))};directive("ref",handler3);directive("if",((e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&warn("x-if can only be used on a <template> tag",e);let i=evaluateLater(e,t);let show=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let t=e.content.cloneNode(true).firstElementChild;addScopeToNode(t,{},e);mutateDom((()=>{e.after(t);skipDuringClone((()=>initTree(t)))()}));e._x_currentIfEl=t;e._x_undoIf=()=>{mutateDom((()=>{destroyTree(t);t.remove()}));delete e._x_currentIfEl};return t};let hide=()=>{if(e._x_undoIf){e._x_undoIf();delete e._x_undoIf}};n((()=>i((e=>{e?show():hide()}))));r((()=>e._x_undoIf&&e._x_undoIf()))}));directive("id",((e,{expression:t},{evaluate:n})=>{let r=n(t);r.forEach((t=>setIdRoot(e,t)))}));interceptClone(((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)}));mapAttributes(startingWith("@",into(prefix("on:"))));directive("on",skipDuringClone(((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let a=r?evaluateLater(e,r):()=>{};if(e.tagName.toLowerCase()==="template"){e._x_forwardEvents||(e._x_forwardEvents=[]);e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t)}let o=on(e,t,n,(e=>{a((()=>{}),{scope:{$event:e},params:[e]})}));i((()=>o()))})));warnMissingPluginDirective("Collapse","collapse","collapse");warnMissingPluginDirective("Intersect","intersect","intersect");warnMissingPluginDirective("Focus","trap","focus");warnMissingPluginDirective("Mask","mask","mask");function warnMissingPluginDirective(e,t,n){directive(t,(r=>warn(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r)))}J.setEvaluator(normalEvaluator);J.setReactivityEngine({reactive:reactive2,effect:effect2,release:stop,raw:toRaw});var Re=J;var Te=Re;export{Re as Alpine,Te as default};

