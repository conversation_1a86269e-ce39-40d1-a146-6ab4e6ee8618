package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"github.com/go-redis/redis/v8"
)

const (
	redisQueueKey    = "game:userBetpop"
	dynamoDBTableName = "UserBets" // 你的 DynamoDB 表名
	batchSize        = 25           // 批量写入 DynamoDB 的最大数量 (DynamoDB BatchWriteItem 限制为 25)
	batchTimeout     = 5 * time.Second // 达到该时间间隔，即使未达到 batchSize 也会写入
	redisPollTimeout = 1 * time.Second // Redis BLPOP 的超时时间
)

// UserBet 结构体，用于解析 Redis 中的数据和写入 DynamoDB
type UserBet struct {
	UserID    string  `json:"userId"`
	BetAmount float64 `json:"betAmount"`
	GameID    string  `json:"gameId"`
	Timestamp int64   `json:"timestamp"` // Unix Timestamp
	// 根据你的实际数据结构添加更多字段
}

func main() {
	// 配置 Redis 客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // 替换为你的 Redis 地址
		Password: "",               // 替换为你的 Redis 密码，如果没有则留空
		DB:       0,                // Redis DB
	})

	// 测试 Redis 连接
	_, err := rdb.Ping(context.Background()).Result()
	if err != nil {
		log.Fatalf("无法连接到 Redis: %v", err)
	}
	log.Println("成功连接到 Redis")

	// 配置 AWS Session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("ap-southeast-1"), // 替换为你的 AWS Region
	})
	if err != nil {
		log.Fatalf("无法创建 AWS Session: %v", err)
	}

	// 配置 DynamoDB 客户端
	svc := dynamodb.New(sess)

	log.Println("服务启动，开始处理 Redis 队列数据...")

	// 用于优雅关机的上下文和信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动消费者 Goroutine
	go consumeRedisQueue(ctx, rdb, svc)

	// 等待关机信号
	<-sigCh
	log.Println("接收到关机信号，正在优雅退出...")
	cancel() // 通知消费者 Goroutine 退出

	// 等待消费者 Goroutine 结束 (可以根据需要添加一个等待组来确保所有操作完成)
	log.Println("服务已停止。")
}

func consumeRedisQueue(ctx context.Context, rdb *redis.Client, svc *dynamodb.DynamoDB) {
	betsBatch := make([]*UserBet, 0, batchSize)
	batchTimer := time.NewTimer(batchTimeout)
	defer batchTimer.Stop() // 确保定时器被停止

	for {
		select {
		case <-ctx.Done():
			// 接收到关机信号，处理剩余批次数据并退出
			if len(betsBatch) > 0 {
				log.Printf("正在处理剩余的 %d 条数据...", len(betsBatch))
				writeBatchToDynamoDB(svc, betsBatch)
			}
			return
		case <-batchTimer.C:
			// 达到超时时间，写入当前批次数据
			if len(betsBatch) > 0 {
				log.Printf("达到批量超时，正在写入 %d 条数据...", len(betsBatch))
				writeBatchToDynamoDB(svc, betsBatch)
				betsBatch = betsBatch[:0] // 清空批次
			}
			// 重置定时器
			batchTimer.Reset(batchTimeout)
		default:
			// 从 Redis 队列中阻塞式弹出数据
			// BLPOP 会阻塞，直到有数据或超时
			result, err := rdb.BLPop(ctx, redisPollTimeout, redisQueueKey).Result()
			if err == redis.Nil {
				// 没有数据，继续循环
				continue
			}
			if err != nil {
				log.Printf("从 Redis 队列中弹出数据失败: %v", err)
				// 可以在这里添加指数退避重试逻辑
				time.Sleep(time.Second) // 简单等待后重试
				continue
			}

			// 解析 Redis 返回的数据，BLPOP 返回的是 [key, value]
			if len(result) < 2 {
				log.Printf("Redis BLPOP 返回数据格式不正确: %v", result)
				continue
			}
			jsonStr := result[1]

			var userBet UserBet
			if err := json.Unmarshal([]byte(jsonStr), &userBet); err != nil {
				log.Printf("解析 Redis 数据失败: %v, 数据: %s", err, jsonStr)
				continue
			}

			betsBatch = append(betsBatch, &userBet)

			if len(betsBatch) >= batchSize {
				log.Printf("达到批量大小限制 %d，正在写入数据...", batchSize)
				writeBatchToDynamoDB(svc, betsBatch)
				betsBatch = betsBatch[:0] // 清空批次
				// 重置定时器，因为已经写入了一批数据
				batchTimer.Reset(batchTimeout)
			}
		}
	}
}

func writeBatchToDynamoDB(svc *dynamodb.DynamoDB, bets []*UserBet) {
	if len(bets) == 0 {
		return
	}

	writeRequests := make([]*dynamodb.WriteRequest, len(bets))
	for i, bet := range bets {
		av, err := dynamodbattribute.MarshalMap(bet)
		if err != nil {
			log.Printf("Marshal DynamoDB attribute 失败: %v, 数据: %+v", err, bet)
			continue // 跳过当前错误数据
		}
		writeRequests[i] = &dynamodb.WriteRequest{
			PutRequest: &dynamodb.PutRequest{
				Item: av,
			},
		}
	}

	// 构造 BatchWriteItemInput
	input := &dynamodb.BatchWriteItemInput{
		RequestItems: map[string][]*dynamodb.WriteRequest{
			dynamoDBTableName: writeRequests,
		},
	}

	// 循环重试 BatchWriteItem，处理 UnprocessedItems
	maxRetries := 5
	for retry := 0; retry < maxRetries; retry++ {
		_, err := svc.BatchWriteItem(input)
		if err != nil {
			log.Printf("写入 DynamoDB 失败 (重试 %d/%d): %v", retry+1, maxRetries, err)
			// 可以在这里添加指数退避
			time.Sleep(time.Duration(retry+1) * time.Second)
			continue
		}

		// DynamoDB 的 BatchWriteItem 可能会返回 UnprocessedItems
		// 需要检查并处理这些未处理的项
		// TODO: 在这里添加对 UnprocessedItems 的处理逻辑
		// 例如：如果存在 UnprocessedItems，构建新的 input 并再次尝试写入
		// 这个示例为了简洁，省略了 UnprocessedItems 的详细处理
		// 实际生产环境中需要仔细处理 UnprocessedItems，确保所有数据都被写入
		// 例如：
		// if output.UnprocessedItems != nil && len(output.UnprocessedItems[dynamoDBTableName]) > 0 {
		// 	log.Printf("存在 %d 条未处理项，准备重试...", len(output.UnprocessedItems[dynamoDBTableName]))
		// 	input.RequestItems = output.UnprocessedItems
		// 	continue // 继续重试
		// }

		log.Printf("成功写入 %d 条数据到 DynamoDB", len(bets))
		return // 所有数据写入成功
	}

	log.Printf("多次重试后仍未能成功写入所有数据到 DynamoDB。未写入数据数量: %d", len(bets))
}