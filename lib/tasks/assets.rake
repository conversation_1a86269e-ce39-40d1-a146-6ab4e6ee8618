namespace :assets do
  desc "Synchronize assets to S3"
  task sync_to_s3: :environment do
    require "aws-sdk-s3"

    s3 = Aws::S3::Resource.new(
      region: ENV["S3_AWS_REGION"],
      access_key_id: ENV["S3_AWS_ACCESS_KEY_ID"],
      secret_access_key: ENV["S3_AWS_SECRET_ACCESS_KEY"]
    )
    bucket = s3.bucket(ENV["S3_AWS_BUCKET"])

    assets_dir = Rails.root.join("public", "assets")
    Dir.glob(File.join(assets_dir, "**", "*")).each do |file|
      next if File.directory?(file)

      key = "assets/#{file.sub(assets_dir.to_s + '/', '')}"
      obj = bucket.object(key)

      # Determine content type based on file extension
      content_type = case File.extname(file)
      when ".js"
        "application/javascript"
      when ".css"
        "text/css"
      when ".png"
        "image/png"
      when ".jpg", ".jpeg"
        "image/jpeg"
      when ".gif"
        "image/gif"
      when ".svg"
        "image/svg+xml"
      when ".woff"
        "font/woff"
      when ".woff2"
        "font/woff2"
      else
        "application/octet-stream"
      end

      puts "Uploading #{file} to #{key} with content type: #{content_type}"
      obj.upload_file(
        file,
        acl: "public-read",
        content_type: content_type
      )
    end

    puts "Assets sync completed!"
  end
end
