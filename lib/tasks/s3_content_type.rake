namespace :s3 do
  desc "Inspect and update content type for JavaScript and CSS files in S3"
  task inspect_content_types: :environment do
    require "aws-sdk-s3"

    s3_client = Aws::S3::Client.new(
      region: ENV["S3_AWS_REGION"],
      access_key_id: ENV["S3_AWS_ACCESS_KEY_ID"],
      secret_access_key: ENV["S3_AWS_SECRET_ACCESS_KEY"]
    )

    bucket = ENV["S3_AWS_BUCKET"]

    # List all objects in the bucket
    objects = s3_client.list_objects_v2(bucket: bucket)

    puts "\nInspecting file content types:"
    puts "----------------------------"

    objects.contents.each do |object|
      if object.key.end_with?(".js") || object.key.end_with?(".css")
        # Get object metadata
        metadata = s3_client.head_object(bucket: bucket, key: object.key)
        content_type = metadata.content_type

        puts "File: #{object.key}"
        puts "Current Content-Type: #{content_type}"

        # Determine correct content type
        correct_content_type = if object.key.end_with?(".js")
          "application/javascript"
        elsif object.key.end_with?(".css")
          "text/css"
        end

        if content_type != correct_content_type
          puts "Updating content type to: #{correct_content_type}"
          s3_client.copy_object(
            bucket: bucket,
            key: object.key,
            copy_source: "#{bucket}/#{object.key}",
            metadata_directive: "REPLACE",
            content_type: correct_content_type
          )
        end

        puts "----------------------------"
      end
    end

    puts "\nInspection and update completed!"
  end
end
