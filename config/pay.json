{"epay": {"funcCode": "epay", "requestParameters": [{"key": "app_id", "type": "String", "explain": "appId"}, {"key": "trade_no", "type": "String", "explain": "商户号"}, {"key": "secret_key", "type": "String", "explain": "密钥"}, {"key": "pay_code", "type": "String", "explain": "代收产品编码"}, {"key": "withdraw_code", "type": "String", "explain": "代付产品编码"}]}, "maga": {"funcCode": "maga", "requestParameters": [{"key": "app_id", "type": "String", "explain": "appId"}, {"key": "trade_no", "type": "String", "explain": "商户号"}, {"key": "secret_key", "type": "String", "explain": "密钥"}, {"key": "pay_code", "type": "String", "explain": "代收产品编码"}, {"key": "withdraw_code", "type": "String", "explain": "代付产品编码"}]}, "pay4z": {"funcCode": "pay4z", "requestParameters": [{"key": "merchantID", "type": "String", "explain": "商户id"}, {"key": "secret<PERSON>ey", "type": "String", "explain": "密钥"}]}, "win_win_pay": {"funcCode": "win_win_pay", "requestParameters": [{"key": "mch_id", "type": "String", "explain": "商户id"}, {"key": "secret_key", "type": "String", "explain": "密钥"}]}}