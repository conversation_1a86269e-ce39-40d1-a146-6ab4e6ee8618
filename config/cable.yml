# Async adapter only works within the same process, so for manually triggering cable updates from a console,
# and seeing results in the browser, you must do so from the web console (running inside the dev process),
# not a terminal started via bin/rails console! Add "console" to any action or any ERB template view
# to make the web console appear.
development:
  adapter: async

test:
  adapter: test

production:
  adapter: redis
  url: redis://<%= ENV.fetch("REDIS_HOST") { "localhost" } %>:<%= ENV.fetch("REDIS_PORT") { "6379" } %>/<%= ENV.fetch("REDIS_DB") { "0" } %>
  channel_prefix: catalyst
