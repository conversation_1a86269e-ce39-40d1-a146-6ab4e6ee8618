# Redis Stream 初始化配置
Rails.application.config.after_initialize do
  begin
    # 确保 Redis Stream 和 Consumer Group 存在
    Rails.cache.redis.with do |redis_client|
      # 创建 Stream 和 Consumer Group
      redis_client.xgroup(:create, RedisStreamProcessor::STREAM_KEY, RedisStreamProcessor::GROUP_NAME, "0", mkstream: true)
    end
    Rails.logger.info "Redis Stream 和 Consumer Group 初始化成功"
  rescue Redis::CommandError => e
    # 如果 group 已存在，忽略错误
    if e.message.include?("BUSYGROUP")
      Rails.logger.info "Redis Stream Consumer Group 已存在"
    else
      Rails.logger.error "Redis Stream 初始化失败: #{e.message}"
    end
  rescue => e
    Rails.logger.error "Redis Stream 初始化出错: #{e.message}"
  end
end
