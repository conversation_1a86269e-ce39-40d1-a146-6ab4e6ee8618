S3_AWS_ACCESS_KEY_ID = ENV["S3_AWS_ACCESS_KEY_ID"]
S3_AWS_SECRET_ACCESS_KEY = ENV["S3_AWS_SECRET_ACCESS_KEY"]
S3_AWS_REGION = ENV["S3_AWS_REGION"]
S3_AWS_BUCKET = ENV["S3_AWS_BUCKET"]

if S3_AWS_ACCESS_KEY_ID.present? && S3_AWS_SECRET_ACCESS_KEY.present? && S3_AWS_REGION.present? && S3_AWS_BUCKET.present?
  CarrierWave.configure do |config|
    config.fog_credentials = {
      provider:              "AWS",                        # required
      aws_access_key_id:     S3_AWS_ACCESS_KEY_ID,                        # required unless using use_iam_profile
      aws_secret_access_key: S3_AWS_SECRET_ACCESS_KEY,                        # required unless using use_iam_profile
      use_iam_profile:       false,                         # optional, defaults to false
      region:                S3_AWS_REGION                  # optional, defaults to 'us-east-1'
    }
    config.fog_directory  = S3_AWS_BUCKET                                      # required
    config.fog_public     = true                                                 # optional, defaults to true
    config.fog_attributes = { cache_control: "public, max-age=#{365.days.to_i}" } # optional, defaults to {}
      # Use this if you have AWS S3 ACLs disabled.
      # config.fog_attributes = { 'x-amz-acl' => 'bucket-owner-full-control' }
      # Use this if you have Google Cloud Storage uniform bucket-level access enabled.
      # config.fog_attributes = { uniform: true }
      # For an application which utilizes multiple servers but does not need caches persisted across requests,
      # uncomment the line :file instead of the default :storage.  Otherwise, it will use AWS as the temp cache store.
      # config.cache_storage = :file
    end
end
