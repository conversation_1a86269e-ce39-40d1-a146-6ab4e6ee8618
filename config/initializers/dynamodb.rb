access_key_id = ENV["DYNAMODB_AWS_ACCESS_KEY_ID"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :access_key_id)
secret_access_key = ENV["DYNAMODB_AWS_SECRET_ACCESS_KEY"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :secret_access_key)
region = ENV["DYNAMODB_AWS_REGION"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :region)

if access_key_id.present? && secret_access_key.present? && region.present?
  Aws.config.update({
    region: region,
    credentials: Aws::Credentials.new(access_key_id, secret_access_key)
  })

  DYNAMO_DB_CLIENT = Aws::DynamoDB::Client.new
  puts "Aws DynamoDB Client initialized."
else
  puts "No Aws DynamoDB Environment variables detect."
end
