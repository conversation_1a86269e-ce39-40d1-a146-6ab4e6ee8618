redis_url = "redis://"
if ENV["REDIS_PASSWORD"].present?
  redis_url += "#{ENV["REDIS_PASSWORD"]}@"
end
redis_url += "#{ENV.fetch("REDIS_HOST", "localhost")}:#{ENV.fetch("REDIS_PORT", "6379")}"
if ENV["REDIS_DB"].present?
  redis_url += "/#{ENV["REDIS_DB"]}"
end

Sidekiq.configure_server do |config|
  config.redis = { url: redis_url }
end

Sidekiq.configure_client do |config|
  config.redis = { url: redis_url }
end
