require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module CatalystAdminBackend
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # 设置默认时区为北京时区
    config.time_zone = ENV["TIME_ZONE"] || "Beijing"
    # 设置 ActiveRecord 存储时间时使用 UTC
    config.active_record.default_timezone = :utc
    # 设置 ActiveRecord 读取时间时转换为本地时区
    # config.active_record.time_zone_aware_attributes = true
    # config.eager_load_paths << Rails.root.join("extras")

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    # config.api_only = true

    config.i18n.default_locale = "zh-CN"
    config.i18n.available_locales = [ :"zh-CN" ]
  end
end
