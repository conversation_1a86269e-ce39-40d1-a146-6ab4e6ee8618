[Unit]
Description=bms Rails Application Server
After=syslog.target network.target

[Service]
Type=simple        
WorkingDirectory=/var/www/bms/current
EnvironmentFile=/var/www/bms/current/.env

ExecStart=/home/<USER>/.rbenv/bin/rbenv exec bundle exec puma -C config/puma.rb
ExecReload=touch /var/www/bms/current/tmp/restart.txt
Restart=always
RestartSec=10s

StandardOutput=journal

[Install]
WantedBy=default.target