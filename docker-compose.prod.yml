services:
  web:
    image: ghcr.io/sancatalyst/catalyst-admin-backend:latest
    ports:
      - "3000:3000"
    restart: always
    volumes:
      - ${HOME}/catalyst-admin/credentials:/rails/config/credentials
      - ${HOME}/catalyst-admin/storage:/rails/storage
      - ${HOME}/catalyst-admin/public_static:/rails/public/static
      - ${HOME}/catalyst-admin/public_uploads:/rails/public/uploads
    env_file:
      - .env
    depends_on:
      - db
      - redis

  job:
    image: ghcr.io/sancatalyst/catalyst-admin-backend:latest
    restart: always
    command: bundle exec sidekiq
    volumes:
      - ${HOME}/catalyst-admin/credentials:/rails/config/credentials
      - ${HOME}/catalyst-admin/storage:/rails/storage
      - ${HOME}/catalyst-admin/public_static:/rails/public/static
      - ${HOME}/catalyst-admin/public_uploads:/rails/public/uploads
    env_file:
      - .env
    depends_on:
      - db
      - redis

  front-api:
    image: ghcr.io/sancatalyst/catalyst-front-api:latest
    container_name: catalyst-app
    env_file:
      - .env
    depends_on:
      - redis
      - db
    ports:
      - "3001:3001"

  redis_stream_processor:
    image: ghcr.io/sancatalyst/catalyst-admin-backend:latest
    restart: always
    command: ["bundle", "exec", "rails", "redis_stream:processor"]
    volumes:
      - ${HOME}/catalyst-admin/credentials:/rails/config/credentials
      - ${HOME}/catalyst-admin/storage:/rails/storage
      - ${HOME}/catalyst-admin/public_static:/rails/public/static
      - ${HOME}/catalyst-admin/public_uploads:/rails/public/uploads
    env_file:
      - .env
    depends_on:
      - db
      - redis

  redis:
    image: redis:7.4.1-alpine
    restart: unless-stopped
    ports:
      - 6379:6379
    volumes:
      - ./redis-data:/data
      - ./redis/redis.conf:/etc/redis.conf
    command: redis-server /etc/redis.conf
    env_file:
      - .env

  db:
    image: mysql:8.0
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - mysql-data:/var/lib/mysql

volumes:
  mysql-data: