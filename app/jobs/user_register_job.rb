class UserRegisterJob # < ApplicationJob
  include Sidekiq::Job
  queue_as :default

  # args[0] is the user id
  # args[1] is the user name
  # args[3] is the user mobile
  def perform(*args)
    # Log user registered event
    puts "User registered: #{args.inspect}"
    user = User.find(args[0])
    SystemLog.log(
      actor: user,
      loggable: user,
      operate: :user_register,
      action: "用户注册",
      snapshot: { name: user.name, mobile: user.mobile }
    )
  end
end
