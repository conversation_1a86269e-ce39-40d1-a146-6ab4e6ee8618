class RedisStreamProcessor
  STREAM_KEY = "game_bet_stream".freeze
  GROUP_NAME = "game_bet_stream_group".freeze
  CONSUMER_NAME = "game_bet_stream_consumer".freeze
  BATCH_SIZE = 100

  def self.start
    new.start
  end

  def start
    ensure_consumer_group_exists
    process_stream
  end

  private

  def ensure_consumer_group_exists
    Rails.cache.redis.with do |redis_client|
      redis_client.xgroup(:create, STREAM_KEY, GROUP_NAME, "0", mkstream: true)
    end
  rescue Redis::CommandError => e
    # Group might already exist, which is fine
    Rails.logger.info "Consumer group might already exist: #{e.message}"
  end

  def process_stream
    loop do
      # puts "#{Time.now} Processing stream..."
      Rails.cache.redis.with do |redis_client|
        begin
          messages = redis_client.xreadgroup(
            GROUP_NAME,
            CONSUMER_NAME,
            STREAM_KEY,
            ">",
            count: BATCH_SIZE,
            block: 500,
            noack: true
          )

          if messages.empty?
            next
          end

          bets_to_process = []
          entry_ids = []
          messages.each do |stream, entries|
            puts "entries count: #{entries.count}"
            entries.each do |entry_id, fields|
              entry_ids << entry_id
              bets_to_process << {
                entry_id: entry_id,
                user_id: fields["user_id"].to_i,
                bet: fields["bet"].to_i,
                real_win: fields["real_win"].to_i,
                win: fields["win"].to_i,
                game_number: fields["game_number"].to_s,
                round_id: fields["round_id"].to_s,
                before_money: fields["before_money"].to_i,
                after_money: fields["after_money"].to_i,
                action_time: fields["bet_time"].to_i
              }
            end
          end

          unless bets_to_process.empty?
            # 批量写入DynamoDB
            result = DynamodbLogger.batch_log_bet(bets_to_process)

            # 只确认成功写入的消息
            if result[:success]
              acknowledge_messages(redis_client, entry_ids)
            else
              # 只确认成功写入的消息
              successful_entry_ids = entry_ids.select.with_index do |_, index|
                result[:processed].include?(bets_to_process[index])
              end
              acknowledge_messages(redis_client, successful_entry_ids)

              # 记录失败的消息
              failed_entry_ids = entry_ids.select.with_index do |_, index|
                result[:failed].include?(bets_to_process[index])
              end
              puts "Failed to process #{failed_entry_ids.size} messages: #{failed_entry_ids.join(', ')}"
            end
          end
        rescue => e
          Rails.logger.error "Error processing stream: #{e.message}"
          sleep 5 # Wait before retrying
        end
      end
    end
  end

  def process_entry(entry_id, fields)
    # 这个方法保留但不再使用，因为我们现在使用批量处理
    user_id = fields["user_id"].to_i
    action_time = fields["bet_time"].to_i

    DynamodbLogger.log_bet(
      user_id: user_id,
      bet: fields["bet"].to_f,
      real_win: fields["real_win"],
      win: fields["win"],
      game_number: fields["game_number"],
      round_id: fields["round_id"],
      before_money: fields["before_money"],
      after_money: fields["after_money"],
      action_time: action_time
    )
  end

  def acknowledge_messages(redis_client, entry_ids)
    return if entry_ids.empty?
    redis_client.xack(STREAM_KEY, GROUP_NAME, *entry_ids)
  end

  # 保留单个确认方法以备不时之需
  def acknowledge_message(redis_client, stream, entry_id)
    redis_client.xack(STREAM_KEY, GROUP_NAME, entry_id)
  end
end
