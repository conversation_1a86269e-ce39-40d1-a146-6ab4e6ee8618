module ApplicationHelper
  include Pagy::Frontend

  def help_text(text)
    content_tag :span, class: "text-xs text-gray-500" do
      raw text
    end
  end

  def link_to_back(path)
    link_to path do
      raw <<-SVG
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
    SVG
    end
  end

  def status_badge(model)
    status_classes = {
      "actived" => "bg-green-100 text-green-800",
      "inactived" => "bg-gray-100 text-gray-800",
      "locked" => "bg-yellow-100 text-yellow-800",
      "deleted" => "bg-red-100 text-red-800"
    }

    content_tag :span, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_classes[model.status]}" do
      model.status_text
    end
  end

  def format_time(time, format = "%Y-%m-%d %H:%M")
    return nil unless time
    time.in_time_zone(Time.zone).strftime(format)
  end
end
