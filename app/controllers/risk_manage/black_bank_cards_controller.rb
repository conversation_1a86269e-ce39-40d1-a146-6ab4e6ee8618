class RiskManage::BlackBankCardsController < ApplicationController
  before_action :set_black_bank_card, only: [ :destroy ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by card number
    search_params = params[:search]
    if search_params.present?
      black_bank_card_scope = BlackBankCard.where("card_number LIKE ? OR remark LIKE ?", "%#{search_params}%", "%#{search_params}%")
    else
      black_bank_card_scope = BlackBankCard.all
    end

    @pagy, @black_bank_cards = pagy(black_bank_card_scope, limit: size, page: current)
  end

  def new
    @black_bank_card = BlackBankCard.new
  end

  def create
    @black_bank_card = BlackBankCard.new(black_bank_card_params)
    @black_bank_card.card_number = @black_bank_card.card_number.to_s.strip
    @black_bank_card.remark = @black_bank_card.remark.to_s.strip
    @black_bank_card.created_by = Current.session.admin&.name

    if @black_bank_card.save
      redirect_to risk_manage_black_bank_cards_path, notice: "黑名单银行卡创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @black_bank_card.destroy
    redirect_to risk_manage_black_bank_cards_path, notice: "黑名单银行卡删除成功"
  end

  private

  def set_black_bank_card
    @black_bank_card = BlackBankCard.find(params[:id])
  end

  def black_bank_card_params
    params.require(:black_bank_card).permit(:card_number, :remark)
  end
end
