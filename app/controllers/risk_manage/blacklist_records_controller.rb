class RiskManage::BlacklistRecordsController < ApplicationController
  before_action :set_blacklist_record, only: [ :destroy ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by user_id or reason
    search_params = params[:search]
    if search_params.present?
      blacklist_record_scope = BlacklistRecord.where("user_id LIKE ? OR reason LIKE ?", "%#{search_params}%", "%#{search_params}%")
    else
      blacklist_record_scope = BlacklistRecord.all
    end

    @pagy, @blacklist_records = pagy(blacklist_record_scope, limit: size, page: current)
  end

  def new
    @blacklist_record = BlacklistRecord.new
  end

  def create
    @blacklist_record = BlacklistRecord.new(blacklist_record_params)
    @blacklist_record.reason = @blacklist_record.reason.to_s.strip
    @blacklist_record.created_by = Current.session.admin&.name
    @blacklist_record.operator_id = Current.session.admin&.id

    if @blacklist_record.save
      redirect_to risk_manage_blacklist_records_path, notice: "用户黑名单创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @blacklist_record.destroy
    redirect_to risk_manage_blacklist_records_path, notice: "用户黑名单删除成功"
  end

  private

  def set_blacklist_record
    @blacklist_record = BlacklistRecord.find(params[:id])
  end

  def blacklist_record_params
    params.require(:blacklist_record).permit(:user_id, :ban_type, :reason, :actived, :expires_at)
  end
end
