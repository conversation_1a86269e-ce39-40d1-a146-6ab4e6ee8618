class RiskManage::BlackIpsController < ApplicationController
  before_action :set_black_ip, only: [ :destroy ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by ip
    search_params = params[:search]
    if search_params.present?
      black_ip_scope = BlackIp.where("ip LIKE ? OR remark LIKE ?", "%#{search_params}%", "%#{search_params}%")
    else
      black_ip_scope = BlackIp.all
    end

    @pagy, @black_ips = pagy(black_ip_scope, limit: size, page: current)
  end

  def new
    @black_ip = BlackIp.new
  end

  def create
    @black_ip = BlackIp.new(black_ip_params)
    @black_ip.ip = @black_ip.ip.to_s.strip
    @black_ip.remark = @black_ip.remark.to_s.strip
    @black_ip.created_by = Current.session.admin&.name

    if @black_ip.save
      redirect_to risk_manage_black_ips_path, notice: "黑名单IP创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @black_ip.destroy
    redirect_to risk_manage_black_ips_path, notice: "黑名单IP删除成功"
  end

  private

  def set_black_ip
    @black_ip = BlackIp.find(params[:id])
  end

  def black_ip_params
    params.require(:black_ip).permit(:ip, :remark)
  end
end
