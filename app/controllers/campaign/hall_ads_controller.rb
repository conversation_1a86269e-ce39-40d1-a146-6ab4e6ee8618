class Campaign::HallAdsController < ApplicationController
  # 运营管理模块: 站点管理 -> 大厅广告
  before_action :set_hall_ad, only: [ :show, :edit, :update, :destroy ]

  # GET /campaign/hall_ads
  # 广告管理 / 大厅广告
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    hall_ad_scope = HallAd.order(order: :desc)

    # 按广告名称搜索
    hall_ad_scope = hall_ad_scope.where("name LIKE ?", "%#{params[:search].to_s.strip}%") if params[:search].present?

    # 按广告位置筛选
    hall_ad_scope = hall_ad_scope.where(ad_position: params[:ad_position]) if params[:ad_position].present?

    # 按跳转类型筛选
    hall_ad_scope = hall_ad_scope.where(redirect_type: params[:redirect_type]) if params[:redirect_type].present?

    # 按可见性筛选
    hall_ad_scope = hall_ad_scope.where(visibility: params[:visibility]) if params[:visibility].present?

    # 按状态筛选
    hall_ad_scope = hall_ad_scope.where(status: params[:status]) if params[:status].present?

    @pagy, @hall_ads = pagy(hall_ad_scope, limit: size, page: current)
  end

  # GET /campaign/hall_ads/:id
  def show
  end

  # GET /campaign/hall_ads/new
  def new
    @hall_ad = HallAd.new(status: :onlined)
  end

  # GET /campaign/hall_ads/:id/edit
  def edit
  end

  # POST /campaign/hall_ads
  def create
    @hall_ad = HallAd.new(hall_ad_params)
    @hall_ad.created_by = Current.session.admin&.name

    if @hall_ad.save
      redirect_to campaign_hall_ads_path, notice: "大厅广告创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaign/hall_ads/:id
  def update
    if @hall_ad.update(hall_ad_params)
      redirect_to campaign_hall_ads_path, notice: "大厅广告更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaign/hall_ads/:id
  def destroy
    @hall_ad.destroy
    redirect_to campaign_hall_ads_path, notice: "大厅广告删除成功"
  end

  private

  def set_hall_ad
    @hall_ad = HallAd.find(params[:id])
  end

  def hall_ad_params
    params.require(:hall_ad).permit(
      :name,
      :media,
      :media_type,
      :ad_position,
      :redirect_type,
      :redirect_link,
      :display_occasion,
      :display_platform,
      :visibility,
      :status,
      :order,
      :expired_at
    )
  end
end
