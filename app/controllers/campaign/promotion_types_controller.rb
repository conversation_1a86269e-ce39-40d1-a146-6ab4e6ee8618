class Campaign::PromotionTypesController < ApplicationController
  before_action :set_promotion_type, only: [ :edit, :update, :destroy, :toggle_status ]

  # GET /campaign/promotion_types
  # 运营管理 / 推广类型
  def index
    @promotion_types = PromotionType.order(sort: :desc, id: :asc)
  end

  # GET /campaign/promotion_types/new
  def new
    @promotion_type = PromotionType.new
  end

  # GET /campaign/promotion_types/:id/edit
  def edit
  end

  # POST /campaign/promotion_types
  def create
    @promotion_type = PromotionType.new(promotion_type_params)
    @promotion_type.created_by = Current.user&.name

    if @promotion_type.save
      SystemLog.log(
        actor: Current.user,
        loggable: @promotion_type,
        operate: :create_promotion_type,
        action: "推广类型 #{@promotion_type.name} 已创建"
      )
      redirect_to campaign_promotion_types_path, notice: "推广类型创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaign/promotion_types/:id
  def update
    if @promotion_type.update(promotion_type_params)
      SystemLog.log(
        actor: Current.admin,
        loggable: @promotion_type,
        operate: :update_promotion_type,
        action: "推广类型 #{@promotion_type.name} 已更新"
      )
      redirect_to campaign_promotion_types_path, notice: "推广类型更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def toggle_status
    @promotion_type.update(status: @promotion_type.enabled? ? :disabled : :enabled)
    # 记录日志
    SystemLog.log(
      actor: Current.admin,
      loggable: @promotion_type,
      operate: @promotion_type.enabled? ? :enable_promotion_type : :disable_promotion_type,
      action: "推广类型 #{@promotion_type.name} #{@promotion_type.enabled? ? '启用' : '禁用'}"
    )
    redirect_to campaign_promotion_types_path, notice: @promotion_type.enabled? ? "推广类型<#{@promotion_type.name}>已启用" : "推广类型<#{@promotion_type.name}>已禁用"
  end

  # DELETE /campaign/promotion_types/:id
  def destroy
    @promotion_type.destroy
    SystemLog.log(
      actor: Current.admin,
      loggable: @promotion_type,
      operate: :destroy_promotion_type,
      action: "推广类型 #{@promotion_type.name} 已删除"
    )
    redirect_to campaign_promotion_types_path, notice: "推广类型删除成功"
  end

  private

  def set_promotion_type
    @promotion_type = PromotionType.find(params[:id])
  end

  def promotion_type_params
    params.require(:promotion_type).permit(:name, :code, :sort)
  end
end
