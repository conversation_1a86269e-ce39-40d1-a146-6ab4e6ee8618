class Campaign::SettingsController < ApplicationController
  # 运营管理模块: 全局配置
  # GET /campaign/setting?section=register_login
  def show
    # check section is valid
    unless Setting.sections.include?(params[:section])
      redirect_to campaign_setting_path(section: Setting.sections.first), alert: "无效的配置项"
      return
    end

    @settings = Setting.where(section: params[:section]).order(sort: :asc)
  end

  def update
    # check section is valid
    unless Setting.sections.include?(params[:section])
      redirect_to campaign_setting_path(section: Setting.sections.first), alert: "无效的配置项"
      return
    end

    settings_params.each do |key, value|
      setting = Setting.find_by(key: key, section: params[:section])
      next unless setting

      setting.update(value: setting.parsed_typed_value(value))
    end

    redirect_to campaign_setting_path(section: params[:section]), notice: "设置已更新"
  rescue => e
    redirect_to campaign_setting_path(section: params[:section]), alert: "更新设置失败: #{e.message}"
  end

  private

  def settings_params
    keys = Setting.get_keys(params[:section])
    params.require(:settings).permit(keys)
  end
end
