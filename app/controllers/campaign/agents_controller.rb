class Campaign::AgentsController < ApplicationController
  # 运营管理模块: 渠道管理 -> 代理管理
  before_action :set_agent, only: [ :edit, :update, :destroy, :reset_password ]
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # Need filter deleted agents
    agent_scope = Agent.where.not(status: :deleted)

    # add search params by name
    search_params = params[:search]
    if search_params.present?
      agent_scope = agent_scope.where("name LIKE ? OR nickname LIKE ?", "%#{search_params}%", "%#{search_params}%")
    end

    if params[:status].present?
      agent_scope = agent_scope.where(status: params[:status])
    end

    # order
    if params[:order].present?
      agent_scope = agent_scope.order(params[:order])
    else
      agent_scope = agent_scope.order(id: :desc)
    end

    @pagy, @agents = pagy(agent_scope, limit: size, page: current)
  end

  def new
    @agent = Agent.new
  end

  def edit
    @agent = Agent.find params[:id]
  end

  def create
    @agent = Agent.new(agent_params)
    @agent.nickname ||= @agent.name
    @agent.password ||= Agent::DEFAULT_PASSWORD
    @agent.totp_secret = ROTP::Base32.random_base32
    @agent.created_by = Current.admin&.name
    @agent.timezone ||= "UTC"

    if @agent.save
      SystemLog.log(
        actor: Current.admin,
        loggable: @agent,
        operate: :create_agent,
        action: "创建代理商",
        snapshot: @agent.attributes
      )
      redirect_to campaign_agent_path(@agent), notice: "代理商 #{@agent.name} 添加成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @agent.update(agent_params)
      SystemLog.log(
        actor: Current.admin,
        loggable: @agent,
        operate: :update_agent,
        action: "更新代理商",
        snapshot: @agent.attributes
      )
      redirect_to campaign_agents_path, notice: "代理商 #{@agent.name} 修改成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def reset_password
    password = params[:password].presence || SecureRandom.hex(8)
    if password.length < 8
      flash.now[:alert] = "密码长度不能少于8位"
      return render turbo_stream: turbo_stream.update(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    end

    begin
      @agent.reset_password(password)
    rescue ActiveRecord::RecordInvalid => e
      flash.now[:alert] = "密码重置失败: #{e.message}"
      render turbo_stream: turbo_stream.replace(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    else
      SystemLog.log(
        actor: Current.admin,
        loggable: @agent,
        operate: :reset_agent_password,
        action: "重置代理商密码",
        snapshot: {
          name: @agent.name,
          nickname: @agent.nickname,
          remark: @agent.remark
        }
      )
      render turbo_stream: turbo_stream.replace(
        "modal",
        partial: "campaign/agents/password_modal",
        locals: { password: password }
      )
    end
  end

  def destroy
    if @agent.update(status: :deleted, deleted_at: Time.current)
      SystemLog.log(
        actor: Current.admin,
        loggable: @agent,
        operate: :destroy_agent,
        action: "删除代理商",
        snapshot: @agent.attributes
      )
      redirect_to campaign_agents_path, notice: "删除成功"
    else
      redirect_to campaign_agents_path, alert: "删除失败"
    end
  end

  def show
    @agent = Agent.where.not(status: :deleted).find(params[:id])
  end

  private

  def set_agent
    @agent = Agent.where.not(status: :deleted).find(params[:id])
  end

  def agent_params
    params.require(:agent).permit(
      :name,
      :password,
      :nickname,
      :email_address,
      :mobile,
      :remark,
      :status,
      :timezone
    )
  end
end
