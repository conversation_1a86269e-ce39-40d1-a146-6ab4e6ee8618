class Campaign::PopupAdsController < ApplicationController
  # 运营管理模块: 站点管理 -> 弹窗广告
  before_action :set_popup_ad, only: [ :show, :edit, :update, :destroy ]

  # GET /campaign/popup_ads
  # 广告管理 / 弹窗广告
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    popup_ad_scope = PopupAd.order(order: :desc)

    # 按广告名称搜索
    popup_ad_scope = popup_ad_scope.where("name LIKE ?", "%#{params[:search].to_s.strip}%") if params[:search].present?

    # 按广告位置筛选
    popup_ad_scope = popup_ad_scope.where(ad_position: params[:ad_position]) if params[:ad_position].present?

    # 按跳转类型筛选
    popup_ad_scope = popup_ad_scope.where(redirect_type: params[:redirect_type]) if params[:redirect_type].present?

    # 按可见性筛选
    popup_ad_scope = popup_ad_scope.where(visibility: params[:visibility]) if params[:visibility].present?

    # 按状态筛选
    popup_ad_scope = popup_ad_scope.where(status: params[:status]) if params[:status].present?

    @pagy, @popup_ads = pagy(popup_ad_scope, limit: size, page: current)
  end

  # GET /campaign/popup_ads/:id
  def show
  end

  # GET /campaign/popup_ads/new
  def new
    @popup_ad = PopupAd.new(status: :onlined)
  end

  # GET /campaign/popup_ads/:id/edit
  def edit
  end

  # POST /campaign/popup_ads
  def create
    @popup_ad = PopupAd.new(popup_ad_params)
    @popup_ad.created_by = Current.session.admin&.name

    if @popup_ad.save
      redirect_to campaign_popup_ads_path, notice: "弹窗广告创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaign/popup_ads/:id
  def update
    if @popup_ad.update(popup_ad_params)
      redirect_to campaign_popup_ads_path, notice: "弹窗广告更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaign/popup_ads/:id
  def destroy
    @popup_ad.destroy
    redirect_to campaign_popup_ads_path, notice: "弹窗广告删除成功"
  end

  private

  def set_popup_ad
    @popup_ad = PopupAd.find(params[:id])
  end

  def popup_ad_params
    params.require(:popup_ad).permit(
      :name,
      :media,
      :media_type,
      :redirect_type,
      :redirect_link,
      :display_occasion,
      :visibility,
      :status,
      :order,
      :expired_at
    )
  end
end
