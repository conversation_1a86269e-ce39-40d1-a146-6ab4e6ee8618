class Campaign::FloatingAdsController < ApplicationController
  # 运营管理模块: 站点管理 -> 悬浮广告
  before_action :set_floating_ad, only: [ :show, :edit, :update, :destroy ]

  # GET /campaign/floating_ads
  # 广告管理 / 悬浮广告
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    floating_ad_scope = FloatingAd.order(order: :desc)

    # 按广告名称搜索
    floating_ad_scope = floating_ad_scope.where("name LIKE ?", "%#{params[:search].to_s.strip}%") if params[:search].present?

    # 按广告位置筛选
    floating_ad_scope = floating_ad_scope.where(ad_position: params[:ad_position]) if params[:ad_position].present?

    # 按跳转类型筛选
    floating_ad_scope = floating_ad_scope.where(redirect_type: params[:redirect_type]) if params[:redirect_type].present?

    # 按可见性筛选
    floating_ad_scope = floating_ad_scope.where(visibility: params[:visibility]) if params[:visibility].present?

    # 按状态筛选
    floating_ad_scope = floating_ad_scope.where(status: params[:status]) if params[:status].present?

    @pagy, @floating_ads = pagy(floating_ad_scope, limit: size, page: current)
  end

  # GET /campaign/floating_ads/:id
  def show
  end

  # GET /campaign/floating_ads/new
  def new
    @floating_ad = FloatingAd.new(status: :onlined)
  end

  # GET /campaign/floating_ads/:id/edit
  def edit
  end

  # POST /campaign/floating_ads
  def create
    @floating_ad = FloatingAd.new(floating_ad_params)
    @floating_ad.created_by = Current.session.admin&.name

    if @floating_ad.save
      redirect_to campaign_floating_ads_path, notice: "悬浮广告创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaign/floating_ads/:id
  def update
    if @floating_ad.update(floating_ad_params)
      redirect_to campaign_floating_ads_path, notice: "悬浮广告更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaign/floating_ads/:id
  def destroy
    @floating_ad.destroy
    redirect_to campaign_floating_ads_path, notice: "悬浮广告删除成功"
  end

  private

  def set_floating_ad
    @floating_ad = FloatingAd.find(params[:id])
  end

  def floating_ad_params
    params.require(:floating_ad).permit(
      :name,
      :media,
      :ad_position,
      :redirect_type,
      :redirect_link,
      :status,
      :order,
      :expired_at
    )
  end
end
