
class Pay::PaymentTypesController < ApplicationController
  before_action :set_payment_type, only: [ :edit, :update, :destroy ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = PaymentType.order(id: :desc)
    if search_params.present?
      scope = query.where("name LIKE ? ", "%#{search_params}%")
    else
      scope = query.all
    end

    @pagy, @payment_types = pagy(scope, limit: size, page: current)
  end

  def new
    @payment_type = PaymentType.new
  end

  # POST /payment_type
  def create
    @payment_type = PaymentType.new(type_params)
    if @payment_type.save
      redirect_to pay_payment_types_path, notice: "创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  # PATCH/PUT
  def update
    if @payment_type.update(type_params)
      redirect_to pay_payment_types_path, notice: "修改成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
     if @payment_type.destroy
      redirect_to pay_payment_types_path, notice: "删除成功"
     else
      redirect_to pay_payment_types_path, alert: "删除失败"
     end
  end

  private

  def set_payment_type
    @payment_type = PaymentType.find(params[:id])
  end

  def type_params
    params.require(:payment_type).permit(:name, :status, :weight)
  end
end
