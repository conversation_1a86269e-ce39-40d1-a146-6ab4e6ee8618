class Pay::PaymentSettingsController < ApplicationController
  # 财务管理模块：充值配置
  # GET /pay/payment_setting?section=recharge
  # GET /pay/payment_setting?section=withdraw
  def show
    unless [ "recharge", "withdraw" ].include?(params[:section])
      render file: "#{Rails.root}/public/404.html", layout: false, status: :not_found
      return
    end

    @settings = Setting.where(section: params[:section]).order(sort: :asc)
  end

  # PATCH /pay/payment_setting?section=recharge
  # PATCH /pay/payment_setting?section=withdraw
  def update
    # Check section
    unless [ "recharge", "withdraw" ].include?(params[:section])
      render file: "#{Rails.root}/public/404.html", layout: false, status: :not_found
      return
    end

    settings_params.each do |key, value|
      setting = Setting.find_by(key: key, section: params[:section])
      next unless setting

      setting.update(value: setting.parsed_typed_value(value))
    end
    # Refresh cache
    Setting.notify_section_refreshed(params[:section])
    Setting.notify_all_refreshed
    redirect_to pay_payment_setting_path(section: params[:section]), notice: "<#{params[:section]}> 设置已更新"
  rescue => e
    redirect_to pay_payment_setting_path(section: params[:section]), alert: "更新设置失败: #{e.message}"
  end

  private

  def settings_params
    keys = Setting.get_keys(params[:section])
    params.require(:settings).permit(keys)
  end
end
