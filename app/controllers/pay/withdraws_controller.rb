class Pay::WithdrawsController < ApplicationController
    before_action :set_withdraw, only: [ :edit, :update ]
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Withdraw.left_joins(:user, :channel, :agent).select("withdraws.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
    query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
    query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
    query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
    query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
    query = query.where(status: params[:status].to_i) if params[:status].present?

    @pagy, @withdraws = pagy(query, limit: size, page: current)
  end

  def edit
  end

    # PATCH/PUT /api/v1/withdraws/1
    def update
    # 开启事务
    begin
        if @withdraw.status!=0
        redirect_to pay_withdraw_types_path, alert: "已经处理过了"
        return
        end
        ActiveRecord::Base.transaction do
        @withdraw.update(withdraw_params)
        status = withdraw_params[:status].to_i
        if status==2
            # 退回余额
            wallet = Wallet.find_by(user_id: @withdraw.user_id)
            wallet.balance = wallet.balance + @withdraw.amount
            wallet.save!
        end
        save_log(status)
        end
    rescue => e
        redirect_to pay_withdraw_types_path, alert:  "修改失败,#{e.message}"
        return
    end
    redirect_to pay_withdraw_types_path, notice: "修改成功"
    end

    private

    def set_withdraw
    @withdraw = Withdraw.find(params[:id])
    end

    def withdraw_params
    params.require(:withdraw).permit(
        :status,
        :risk,
        :remark
    )
    end

    # 记录操作日志
    def save_log(status)
        @log = SystemLog.new(
            admin_id: current_admin&.id,
            admin_name: current_admin&.name,
            operate_type: 2,
            ip: request.remote_ip,
            user_id: @withdraw.user_id,
            ordre_no: @withdraw.order_no
        )
        status = withdraw_params[:status].to_i
        case status
        when 1
        # 提款成功
        @log.operate = 5
        @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现成功"
        when 2
        # 拒绝
        @log.operate = 6
        @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现拒绝"
        when 3
        # 没收
        @log.operate = 7
        @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现没收"
        else
        @log.operate_desc = "未知状态操作"
        end
        @log.save if @log.operate.present?
    end
end
