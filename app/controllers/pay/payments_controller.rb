class Pay::PaymentsController < ApplicationController
  before_action :set_payment, only: [ :edit, :update, :destroy ]
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Payment.order(id: :desc)
    if search_params.present?
      payment_scope = query.where("name LIKE ? ", "%#{search_params}%")
    else
      payment_scope = query.all
    end

    @pagy, @payments = pagy(payment_scope, limit: size, page: current)
  end

  def new
    if not Payment.payment_solutions.keys.include?(params[:payment_solution])
      render file: "#{Rails.root}/public/404.html", layout: false, status: :not_found
      return
    end

    @payment = Payment.new
    @payment.recharge_method = params[:payment_solution]
    @payment.extras = {}
  end

  # POST /payments
  def create
    @payment = Payment.new(payment_params)
    @payment.extras ||= {}

    if @payment.save
      redirect_to pay_payments_path, notice: "创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @payment
  end

  # PATCH/PUT
  def update
    if @payment.update(payment_params)
      redirect_to pay_payments_path, notice: "修改成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
     if @payment.destroy
      redirect_to pay_payments_path, notice: "删除成功"
     else
      redirect_to payments_path, alert: "删除失败"
     end
  end

  private

  def set_payment
    @payment = Payment.find(params[:id])
  end

  def payment_params
    params.require(:payment).permit(:name, :recharge_fee_rate, :recharge_weight, :recharge_enabled, :min_recharge, :max_recharge, :withdraw_fee_rate, :withdraw_enabled, :min_withdraw, :max_withdraw, :withdraw_weight, :payment_type_id, :payment_way, :recharge_method,
    :recharge_request_url, :withdraw_request_url, :ip_whitelist).tap do |whitelisted|
      if params[:payment][:extras].present?
        allowed_keys = Payment.payment_solutions[params[:payment][:recharge_method]]&.dig("requestParameters")&.map { |p| p["key"] } || []
        whitelisted[:extras] = params[:payment][:extras].permit(*allowed_keys).to_h
      end
    end
  end
end
