class Pay::RechargesController < ApplicationController
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Recharge.left_joins(:user, :channel, :agent).select("recharges.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
    query = query.where("user_id = ? or username = ?", "#{params[:search]}", "#{params[:search]}") if params[:search].present?
    # query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
    query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
    query = query.where("first_recharge = ?", "#{params[:first_recharge]}") if params[:first_recharge].present?
    query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
    query = query.where(status: params[:status].to_i) if params[:status].present?


    @pagy, @recharges = pagy(query, limit: size, page: current)
  end
end
