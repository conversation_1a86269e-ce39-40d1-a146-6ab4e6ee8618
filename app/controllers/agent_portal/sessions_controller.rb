class AgentPortal::Sessions<PERSON>ontroller < AgentPortal::ApplicationController
  allow_unauthenticated_access
  rate_limit to: 10, within: 3.minutes, only: :create, with: -> { redirect_to new_agent_portal_session_path, alert: "Try again later." }

  layout "blank_agent"

  # GET /agent_portal/sessions/new
  def new
    # Check if the user is already logged in, if so, clear the session
    if Current.user
      terminate_session
    end
  end

  # POST /agent_portal/sessions
  def create
    agent = Agent.find_by(name: params[:email_address].to_s.strip)

    if agent && agent.authenticate(params[:password].to_s.strip)
      unless agent.actived?
        redirect_to new_agent_portal_session_path, alert: "您的帐号已被锁定，请联系管理员。"
        return
      end

      start_new_session_for agent
      SystemLog.log(
        actor: agent,
        loggable: agent,
        operate: :agent_login,
        action: "代理商登录",
        snapshot: { name: agent.name }
      )

      redirect_to agent_portal_root_path
    else
      redirect_to new_agent_portal_session_path, alert: "帐号或密码不正确，请重新输入。"
    end
  end

  # DELETE /agent_portal/sessions
  def destroy
    if Current.agent
      SystemLog.log(
        actor: Current.agent,
        loggable: Current.agent,
        operate: :logout,
        action: "代理商登出",
        snapshot: { name: Current.agent.name }
      )
    end
    terminate_session
    redirect_to new_agent_portal_session_path
  end
end
