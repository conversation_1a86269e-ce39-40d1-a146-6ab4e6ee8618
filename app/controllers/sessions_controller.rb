class SessionsController < ApplicationController
  allow_unauthenticated_access only: %i[ new create ]
  rate_limit to: 10, within: 3.minutes, only: :create, with: -> { redirect_to new_session_url, alert: "Try again later." }

  layout "blank"

  # GET /sessions/new
  def new
  end

  # POST /sessions
  def create
    if params[:email_address]&.include?("@")
      admin = Admin.find_by(email_address: params[:email_address].to_s.strip)
    else
      admin = Admin.find_by(name: params[:email_address].to_s.strip)
    end

    if admin && admin.authenticate(params[:password])
      if Rails.env.production? && !admin.verify_totp(params[:otp])
        redirect_to new_session_path, alert: "验证码错误，请重新输入。"
        return
      end
      unless admin.actived?
        redirect_to new_session_path, alert: "您的帐号已被锁定，请联系管理员。"
        return
      end

      start_new_session_for admin
      SystemLog.log(
        actor: admin,
        loggable: admin,
        operate: :admin_login,
        action: "管理员登录",
        snapshot: { email: admin.email_address, name: admin.name }
      )

      redirect_url = after_authentication_url
      if redirect_url.include?(new_session_url)
        redirect_to root_url
      else
        redirect_to redirect_url
      end
    else
      redirect_to new_session_path, alert: "帐号或密码不正确，请重新输入。"
    end
  end

  # DELETE /sessions
  def destroy
    if Current.admin
      SystemLog.log(
        actor: Current.admin,
        loggable: Current.admin,
        operate: :admin_logout,
        action: "管理员登出",
        snapshot: { email: Current.admin.email_address, name: Current.admin.name }
      )
    end
    terminate_session
    redirect_to new_session_path
  end
end
