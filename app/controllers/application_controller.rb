class ApplicationController < ActionController::Base
  include Authentication
  around_action :set_time_zone, if: -> { authenticated? }
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  # allow_browser versions: :modern
  include ActionController::MimeResponds
  include Pagy::Backend
  before_action :set_locale

  private

  def authenticate_admin!
    token = extract_token_from_header
    return render_unauthorized unless token

    payload = Admin.decode_token(token)
    return render_unauthorized unless payload

    @current_admin = Admin.find_by(id: payload["id"])
    return render_unauthorized unless @current_admin

    # 检查用户状态
    return render_forbidden("账号已被锁定") if @current_admin.locked?
    render_forbidden("账号已被删除") if @current_admin.deleted?
  end

  def current_admin
    @current_admin
  end

  def extract_token_from_header
    auth_header = request.headers["Authorization"]
    return nil unless auth_header

    # 支持 Bearer token 和直接 token
    if auth_header.start_with?("Bearer ")
      auth_header.split(" ").last
    else
      auth_header
    end
  end

  def render_unauthorized
    render json: {
      code: "8888",
      msg: "未授权访问"
    }
  end

  def render_forbidden(message)
    render json: {
      code: "4030",
      msg: message
    }, status: :forbidden
  end

  def set_locale
    I18n.locale = params[:locale] || I18n.default_locale
  end

  def set_time_zone(&block)
    timezone = if Current.admin
      Current.admin.timezone
    elsif Current.agent
      Current.agent.timezone
    elsif Current.channel
      Current.channel.timezone
    else
      "UTC"
    end
    Time.use_zone(timezone, &block)
  end
end
