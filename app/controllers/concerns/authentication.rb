module Authentication
  extend ActiveSupport::Concern

  included do
    before_action :require_authentication
    helper_method :authenticated?
  end

  class_methods do
    def allow_unauthenticated_access(**options)
      skip_before_action :require_authentication, **options
    end
  end

  private
    def authenticated?
      resume_session
    end

    def require_authentication
      resume_session || request_authentication
    end

    def resume_session
      return Current.session if Current.session

      login_session = find_session_by_cookie
      if login_session
        Current.session = login_session
        Current.request = request

        # 根据 session 记录设置当前用户
        if login_session.admin_id
          Current.admin = login_session.admin
        elsif login_session.agent_id
          Current.agent = login_session.agent
        elsif login_session.channel_id
          Current.channel = login_session.channel
        end



        # 验证用户类型与访问路径是否匹配
        unless validate_user_type_access
          redirect_to @access_denied_redirect, alert: @access_denied_message
          return
        end

        # 记录自动登录事件, 只有距离上一次10分钟内没有登录记录时才记录
        if login_session.user.sessions.where("updated_at > ?", 10.minutes.ago).none?
          SystemLog.log(
            actor: login_session.user,
            loggable: login_session.user,
            operate: "#{login_session.user.class.name.downcase}_login".to_sym,
            action: "#{login_session.user.class.name}登录",
            snapshot: {
              email: login_session.user.try(:email_address),
              name: login_session.user.try(:name)
            }
          )
        end
        login_session.update!(updated_at: Time.current) unless Rails.env.test?
        return login_session
      end
      nil
    end

    def validate_user_type_access
      case controller_path
      when /^agent_portal/
        unless Current.agent
          terminate_session
          @access_denied_message = "无权访问代理后台"
          @access_denied_redirect = new_agent_portal_session_path
          return false
        end
      when /^channel_portal/
        unless Current.channel
          terminate_session
          @access_denied_message = "无权访问渠道后台"
          @access_denied_redirect = new_channel_portal_session_path
          return false
        end
      else
        unless Current.admin
          terminate_session
          @access_denied_message = "无权访问管理后台"
          @access_denied_redirect = new_session_path
          return false
        end
      end
      true
    end

    def find_session_by_cookie
      session_id = cookies.signed[:session_id]
      Session.find_by(id: session_id) if session_id
    end

    def request_authentication
      session[:return_to_after_authenticating] = request.url
      case controller_path
      when /^agent_portal/
        redirect_to new_agent_portal_session_path
        nil
      when /^channel_portal/
        redirect_to new_channel_portal_session_path
        nil
      else
        redirect_to new_session_path
        nil
      end
    end

    def after_authentication_url
      session.delete(:return_to_after_authenticating) || case controller_path
                                                         when /^agent_portal/
        agent_portal_root_path
                                                         when /^channel_portal/
        channel_portal_root_path
                                                         else
        dashboard_url
                                                         end
    end

    def start_new_session_for(user)
      user.update_columns(
        last_login_at: Time.current,
        last_login_ip: request.remote_ip,
        last_login_user_agent: request.user_agent,
        login_count: user.login_count + 1
      )

      login_session = case user
      when Admin
        user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip)
      when Agent
        user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip)
      when Channel
        user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip)
      end

      Current.session = login_session

      # 设置当前用户
      case user
      when Admin
        Current.admin = user
      when Agent
        Current.agent = user
      when Channel
        Current.channel = user
      end

      cookies.signed.permanent[:session_id] = { value: login_session.id, httponly: true, same_site: :lax }
    end

    def terminate_session
      Current.session&.destroy
      cookies.delete(:session_id)
    end
end
