class Game::GamePlatformsController < ApplicationController
  before_action :set_game_platform, only: [ :show, :edit, :update, :destroy ]

  # Get /game/game_platforms
  # 游戏管理 / 游戏平台
  def index
    search_params = params[:search].to_s.strip
    if search_params.present?
      game_platform_scope = GamePlatform.where("name LIKE ?", "%#{search_params}%")
    else
      game_platform_scope = GamePlatform.all
    end
    @game_platforms = game_platform_scope
  end

  # GET /game/game_platforms/new
  def new
    @game_platform = GamePlatform.new
  end

  def show
  end

  # GET /game/game_platforms/:id/edit
  def edit
  end

  # POST /game/game_platforms
  def create
    @game_platform = GamePlatform.new(game_platform_params)
    @game_platform.created_by = Current.session.admin&.name

    if @game_platform.save
      redirect_to game_game_platforms_path, notice: "游戏平台创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /game/game_platforms/:id
  def update
    if @game_platform.update(game_platform_params)
      redirect_to game_game_platforms_path, notice: "游戏平台更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /game/game_platforms/:id
  def destroy
    @game_platform.destroy
    redirect_to game_game_platforms_path, notice: "游戏平台删除成功"
  end

  private

  def set_game_platform
    @game_platform = GamePlatform.find(params[:id])
  end

  def game_platform_params
    params.require(:game_platform).permit(:name, :cover_url, :cover_url_selected, :order, :status)
  end
end
