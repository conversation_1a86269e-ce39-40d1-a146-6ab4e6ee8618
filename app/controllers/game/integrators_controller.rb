class Game::IntegratorsController < ApplicationController
  before_action :set_integrator, only: [ :show, :edit, :update, :destroy ]

  # Get /game/integrators
  # 游戏管理 / 集成商
  def index
    search_params = params[:search].to_s.strip
    if search_params.present?
      integrator_scope = Integrator.where("name LIKE ?", "%#{search_params}%")
    else
      integrator_scope = Integrator.all
    end
    @integrators = integrator_scope
  end

  # GET /game/integrators/:id
  def show
  end

  # GET /game/integrators/new
  def new
    @integrator = Integrator.new
  end

  # GET /game/integrators/:id/edit
  def edit
  end

  # POST /game/integrators
  def create
    @integrator = Integrator.new(integrator_params)
    @integrator.created_by = Current.session.admin&.name

    if @integrator.save
      redirect_to game_integrators_path, notice: "集成商创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /game/integrators/:id
  def update
    if @integrator.update(integrator_params)
      redirect_to game_integrators_path, notice: "集成商更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /game/integrators/:id
  def destroy
    @integrator.destroy
    redirect_to game_integrators_path, notice: "集成商删除成功"
  end

  private

  def set_integrator
    @integrator = Integrator.find(params[:id])
  end

  def integrator_params
    params.require(:integrator).permit(:name, :code, :order, :status)
  end
end
