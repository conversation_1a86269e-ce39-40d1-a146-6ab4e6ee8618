class Game::GamesController < ApplicationController
  before_action :set_game, only: [ :show, :edit, :update, :destroy ]

  # Get /game/games
  # 游戏管理 / 子游戏
  def index
    size = (params[:size].presence || 50).to_i.clamp(20, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # Only show no deleted games
    game_scope = Game.where("status != ?", Game.statuses[:deleted]).order(suggested_at: :desc, order: :desc)

    # 按游戏名称搜索
    game_scope = game_scope.where("name LIKE ?", "%#{params[:search].to_s.strip}%") if params[:search].present?

    # 按游戏平台筛选
    game_scope = game_scope.where(game_platform_id: params[:game_platform_id]) if params[:game_platform_id].present?

    # 按集成商筛选
    game_scope = game_scope.where(integrator_id: params[:integrator_id]) if params[:integrator_id].present?

    # 按游戏类型筛选
    game_scope = game_scope.where(game_type_id: params[:game_type_id]) if params[:game_type_id].present?

    # 按状态筛选
    game_scope = game_scope.where(status: params[:status]) if params[:status].present?

    @pagy, @games = pagy(game_scope, limit: size, page: current)
  end

  # GET /game/games/:id
  def show
  end

  # GET /game/games/new
  def new
    @game = Game.new(game_type: GameType.where(name: "slot").first)
  end

  # GET /game/games/:id/edit
  def edit
  end

  # POST /game/games
  def create
    @game = Game.new(game_params)
    @game.created_by = Current.session.admin&.name

    if @game.save
      SystemLog.log(actor: Current.admin, loggable: @game, operate: :create_game, snapshot: @game.attributes)
      redirect_to game_games_path, notice: "游戏创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /game/games/:id
  def update
    if @game.update(game_params)
      SystemLog.log(actor: Current.admin, loggable: @game, operate: :update_game, snapshot: @game.attributes)
      redirect_to game_games_path, notice: "游戏更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /game/games/:id
  def destroy
    if @game.soft_delete
      SystemLog.log(actor: Current.admin, loggable: @game, operate: :destroy_game, snapshot: @game.attributes)
    end
    redirect_to game_games_path, notice: "游戏删除成功"
  end

  private

  def set_game
    @game = Game.find(params[:id])
  end

  def game_params
    params.require(:game).permit(:name, :cover_url_0, :cover_url_1, :cover_url_2, :cover_url_3, :cover_url_4,
      :game_platform_id, :integrator_id, :game_type_id, :screen_direction, :status,
      :integrator_identity, :integrator_game_id, :order, :demo, :hot, :suggested, :suggested_at)
  end
end
