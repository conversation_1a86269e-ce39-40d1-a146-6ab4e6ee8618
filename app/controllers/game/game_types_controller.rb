class Game::GameTypesController < ApplicationController
  before_action :set_game_type, only: [ :show, :edit, :update, :destroy ]

  # Get /game/game_types
  # 游戏管理 / 游戏类型
  def index
    @game_types = GameType.all
  end

  # GET /game/game_types/new
  def new
    @game_type = GameType.new
  end

  # GET /game/game_types/:id/edit
  def edit
  end

  # POST /game/game_types
  def create
    @game_type = GameType.new(game_type_params)
    @game_type.created_by = Current.session.admin&.name

    if @game_type.save
      redirect_to game_game_types_path, notice: "游戏类型创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /game/game_types/:id
  def update
    if @game_type.update(game_type_params)
      redirect_to game_game_types_path, notice: "游戏类型更新成功"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /game/game_types/:id
  def destroy
    @game_type.destroy
    redirect_to game_game_types_path, notice: "游戏类型删除成功"
  end

  private

  def set_game_type
    @game_type = GameType.find(params[:id])
  end

  def game_type_params
    params.require(:game_type).permit(:name, :order, :status)
  end
end
