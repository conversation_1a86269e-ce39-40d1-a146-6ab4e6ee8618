class DashboardController < ApplicationController
  # GET /bashboard
  def index
    @users_count = User.count

    # 读取版本信息
    @git_revision = read_revision_file
    @deploy_time = read_revision_time_file

    # 数据统计 todo
  end

  # 时段报表
  def period_report
  end

  private

  def calculate_storage_size
    storage_path = Rails.root.join("storage")
    total_size = 0

    Dir.glob(File.join(storage_path, "**", "*")) do |file|
      total_size += File.size(file) if File.file?(file)
    end

    # Convert to human readable format
    ActiveSupport::NumberHelper.number_to_human_size(total_size)
  end

  def read_revision_file
    revision_file = Rails.root.join("REVISION")
    if File.exist?(revision_file)
      revision = File.read(revision_file).strip
      revision[0, 7] # 取前7个字符
    else
      "未知"
    end
  end

  def read_revision_time_file
    time_file = Rails.root.join("REVISION_TIME")
    if File.exist?(time_file)
      timestamp = File.read(time_file).strip.to_i
      Time.at(timestamp).strftime("%Y-%m-%d %H:%M")
    else
      "未知"
    end
  end
end
