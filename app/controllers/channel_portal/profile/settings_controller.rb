module ChannelPortal
  module Profile
    class SettingsController < ChannelPortal::ApplicationController
      def show
        @channel = Current.channel
      end

      def update
        @channel = Current.channel
        if @channel.update(channel_params)
          redirect_to channel_portal_profile_setting_path, notice: "个人设置已更新"
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def channel_params
        params.require(:channel).permit(:nickname, :timezone)
      end
    end
  end
end
