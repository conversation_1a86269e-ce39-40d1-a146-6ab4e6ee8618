class RegistrationsController < ApplicationController
  allow_unauthenticated_access only: [ :new, :create ]
  layout "blank"

  def new
    @user = User.new
    @channel_id = params[:channel_id]
  end

  def create
    @user = User.new(user_params)
    # @user.status = :actived if @user.status.blank?
    @user.nickname = "用户#{SecureRandom.hex(2)}" if @user.nickname.blank?
    @user.name = "user_#{SecureRandom.hex(16)}"
    @channel_id = params[:channel_id]

    if @user.save
      # 创建注册追踪记录
      @user.create_register_track!(
        channel_id: @channel_id.to_i,
        ua: request.user_agent || "",
        ip: request.remote_ip,
        device_id: params[:device_id].to_s,
        ad_id: params[:ad_id].to_s,
        ad_click_id: params[:ad_click_id].to_s,
        device_type: params[:device_type].to_s
      )

      # 记录系统日志
      SystemLog.log(
        actor: @user,
        loggable: @user,
        operate: :user_register,
        action: "用户注册"
      )

      # 自动登录
      session[:user_id] = @user.id
      redirect_to root_path, notice: "测试用户注册成功！"
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:mobile, :password, :password_confirmation)
  end
end
