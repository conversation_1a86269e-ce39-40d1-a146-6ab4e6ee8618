class Api::V1::AuthController < ApplicationController
  # skip_before_action :authenticate_admin!
  # POST /api/v1/auth/login
  # params: { userName: "admin", password: "password", totp: "123456" }
  # Response:
  # {
  #  "data": {
  #       "token": "xxxx",
  #       "refreshToken": "xxxx"
  #   },
  #   "code": "0000",
  #   "msg": "请求成功"
  # }
  def login
    @admin = Admin.find_by(name: params[:userName])
    if @admin && @admin.authenticate(params[:password])
      if @admin.status == "locked"
        render json: { data: nil, code: "1002", msg: "账户已锁定" }
      elsif @admin.status == "deleted"
        render json: { data: nil, code: "1003", msg: "账户已删除" }
      else
        if Rails.env.development?
          @admin.update_columns(last_login_at: Time.current, last_login_ip: request.remote_ip,
            last_login_user_agent: request.user_agent)
          render json: { data: { token: @admin.token, refresh_token: @admin.refresh_token }, code: "0000", msg: "请求成功" }
        else

          if params[:totp].blank?
            render json: { data: nil, code: "1004", msg: "请输入 TOTP 验证码" }
          elsif !@admin.verify_totp(params[:totp])
            render json: { data: nil, code: "1005", msg: "TOTP 验证码错误" }
          else
            @admin.update_columns(last_login_at: Time.current, last_login_ip: request.remote_ip,
              last_login_user_agent: request.user_agent)
            render json: { data: { token: @admin.token, refresh_token: @admin.refresh_token }, code: "0000", msg: "请求成功" }
          end

        end

      end
    else
      render json: {
        data: nil,
        code: "1001",
        msg: "用户名或密码错误"
      }, status: :ok
    end
  end

  # GET /api/v1/auth/user_info
  # Response:
  # {
  #   "data": {
  #     "userId": "1",
  #     "userName": "admin",
  #     "roles": ["admin"],
  #     "buttons": ["add", "edit", "delete"]
  #   },
  #   "code": "0000",
  #   "msg": "请求成功"
  # }
  def user_info
    token = extract_token_from_header
    decoded_token = Admin.decode_token(token)
    user_id = decoded_token&.dig("id")
    if user_id.blank?
      render json: { data: nil, code: "8888", msg: "登录信息未找到" }, status: :ok
    else
      @admin = Admin.find_by(id: user_id)
      if @admin.blank?
        render json: { data: nil, code: "8889", msg: "登录信息已过期" }, status: :ok
      else
        render json: {
          data: {
            userId: @admin.id,
            userName: @admin.name,
            roles: @admin.roles_codes,
            buttons: @admin.accessible_buttons
          },
          code: "0000",
          msg: "请求成功"
        }, status: :ok
      end
    end
  end
end
