class Api::V1::User::UsersController < ApplicationController
  before_action :set_user, only: [ :show, :update, :destroy ]

  # GET /api/v1/user/users
  def index
    @users = User.all
    if params[:include_deleted].present?
      @users = @users.with_deleted
    end
    render json: {
      data: {
        current: params[:current].to_i || 1,
        size: params[:size].to_i || 10,
        total: @users.count,
        records: @users.map { |user| format_user(user) }
      },
      code: "0000",
      msg: "请求成功"
    }
  end

  # GET /api/v1/user/users/:id
  def show
    render json: {
      data: format_user(@user),
      code: "0000",
      msg: "请求成功"
    }
  end

  def update
    if @user.update(user_params)
      render json: {
        data: format_user(@user),
        code: "0000",
        msg: "请求成功"
      }
    else
      render json: {
        data: @user.errors,
        code: "0001",
        msg: "请求失败"
      }
    end
  end

  def destroy
    if @user.destroy
      render json: {
        code: "0000",
        msg: "请求成功"
      }
    else
      render json: {
        data: @user.errors,
        code: "0001",
        msg: "请求失败"
      }
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def format_user(user)
    {
      id: user.id,
      name: user.name,
      nickname: user.nickname,
      mobile: user.mobile,
      status: user.status_before_type_cast,
      created_at: user.created_at&.strftime("%Y-%m-%d %H:%M:%S"),
      updated_at: user.updated_at&.strftime("%Y-%m-%d %H:%M:%S"),
      deleted_at: user.deleted_at&.strftime("%Y-%m-%d %H:%M:%S")
    }
  end
end
