module Api
  module V1
    class WhiteIpsController < ApplicationController
      before_action :set_white_ip, only: [ :show, :update, :destroy ]

      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)

        # add search params
        search_params = params[:search]
        if search_params.present?
          white_ip_scope = WhiteIp.where("ip LIKE ?", "%#{search_params}%")
        else
          white_ip_scope = WhiteIp.all
        end
        @pagy, @white_ips = pagy(white_ip_scope, page: current, limit: size)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: {
            current: @pagy.page,
            total: @pagy.count,
            size: @pagy.limit,
            pages: @pagy.pages,
            records: @white_ips.map { |white_ip| format_white_ip(white_ip) }
          }
        }
      end

      def show
        render json: {
          code: "0000",
          msg: "请求成功",
          data: format_white_ip(@white_ip)
        }
      end

      def create
        @white_ip = WhiteIp.new(white_ip_params)

        if @white_ip.save
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_white_ip(@white_ip)
          }, status: :created
        else
          render json: {
            code: "0001",
            msg: @white_ip.errors.full_messages.join(", ")
          }
        end
      end

      def update
        if @white_ip.update(white_ip_params)
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_white_ip(@white_ip)
          }
        else
          render json: {
            code: "0001",
            msg: @white_ip.errors.full_messages.join(", ")
          }
        end
      end

      def destroy
        @white_ip.destroy
        render json: {
          code: "0000",
          msg: "请求成功",
          data: nil
        }
      end

      private

      def set_white_ip
        @white_ip = WhiteIp.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: {
          code: "4040",
          msg: "White IP not found"
        }, status: :not_found
      end

      def white_ip_params
        params.require(:white_ip).permit(:ip)
      end

      def format_white_ip(white_ip)
        {
          id: white_ip.id,
          ip: white_ip.ip,
          remark: white_ip.remark,
          created_by: white_ip.created_by,
          created_at: white_ip.created_at&.strftime("%Y-%m-%d %H:%M:%S")
        }
      end
    end
  end
end
