module Api
  module V1
    class MenusController < ApplicationController
      # GET /api/v1/menus
      def index
        all_menus = Menu.order(:order).all
        menu_tree = build_menu_tree(all_menus)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: {
            records: menu_tree,
            total: all_menus.length,
            size: 10,
            current: 1
          }
        }
      end

      # GET /api/v1/menus/tree
      def tree
        all_menus = Menu.order(:order).all
        menu_tree = build_simple_menu_tree(all_menus)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: menu_tree
        }
      end

      def update
        menu = Menu.find(params[:id])

        # 转换前端参数到数据库字段
        menu_params = {
          name: params[:menu_name],
          route_name: params[:route_name],
          path: params[:route_path],
          component: params[:component],
          icon: params[:icon],
          menu_type: params[:menu_type] == "1" ? "directory" : "menu",
          order: params[:order],
          status: params[:status],
          hidden: params[:hide_in_menu],
          parent_id: params[:parent_id] == 0 ? nil : params[:parent_id]
        }

        if menu.update(menu_params)
          render json: {
            code: "0000",
            msg: "更新成功",
            data: {
              id: menu.id,
              parent_id: menu.parent_id || 0,
              menu_type: menu.menu_type == "directory" ? "1" : "2",
              menu_name: menu.name,
              route_name: menu.route_name,
              route_path: menu.path,
              component: menu.component,
              icon: menu.icon,
              icon_type: "1",
              order: menu.order,
              status: menu.status_before_type_cast,
              status_name: menu.status,
              hide_in_menu: menu.hidden,
              created_by: "admin",
              created_at: menu.created_at.strftime("%Y-%m-%d %H:%M:%S"),
              updated_at: menu.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            }
          }
        else
          render json: {
            code: "0001",
            msg: menu.errors.full_messages.join(", "),
            data: nil
          }, status: :unprocessable_entity
        end
      end

      def destroy
        menu = Menu.find(params[:id])
        menu.destroy

        render json: {
          code: "0000",
          msg: "删除成功",
          data: nil
        }
      end

      def batch_destroy
        Menu.where(id: params[:ids]).destroy_all

        render json: {
          code: "0000",
          msg: "批量删除成功",
          data: nil
        }
      end

      private

      # 完整字段结构，供 /api/v1/menus 使用
      def build_menu_tree(menus)
        menu_map = {}
        root_menus = []

        menus.each do |menu|
          menu_node = {
            id: menu.id,
            parent_id: menu.parent_id || 0,
            menu_type: menu.menu_type == "directory" ? "1" : "2",
            menu_name: menu.title,
            route_name: menu.route_name,
            route_path: menu.path,
            component: menu.component,
            icon: menu.icon,
            icon_type: "1",
            order: menu.order,
            status: menu.status_before_type_cast,
            status_name: menu.status,
            hide_in_menu: menu.hidden,
            created_by: "admin",
            created_at: menu.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at: menu.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            children: []
          }
          menu_map[menu.id] = menu_node
          root_menus << menu_node if menu.parent_id.nil?
        end

        menus.each do |menu|
          if menu.parent_id.present?
            parent_node = menu_map[menu.parent_id]
            parent_node[:children] << menu_map[menu.id] if parent_node
          end
        end

        sort_menu_tree(root_menus)
        root_menus
      end

      # 精简字段结构，供 /api/v1/menus/tree 使用
      def build_simple_menu_tree(menus)
        menu_map = {}
        root_menus = []

        menus.each do |menu|
          menu_node = {
            id: menu.id,
            label: menu.name,
            pId: menu.parent_id || 0
          }
          menu_map[menu.id] = menu_node
          root_menus << menu_node if menu.parent_id.nil?
        end

        menus.each do |menu|
          if menu.parent_id.present?
            parent_node = menu_map[menu.parent_id]
            parent_node[:children] ||= []
            parent_node[:children] << menu_map[menu.id] if parent_node
          end
        end

        sort_menu_tree(root_menus)
        remove_empty_children(root_menus)
        root_menus
      end

      def sort_menu_tree(menus)
        # 对当前层级的菜单按 order 排序
        menus.sort_by! { |menu| menu[:order] }

        # 递归对子菜单排序
        menus.each do |menu|
          sort_menu_tree(menu[:children]) if menu[:children]&.any?
        end
      end

      # 递归去除没有 children 的节点的 children 字段
      def remove_empty_children(nodes)
        nodes.each do |node|
          if node[:children].is_a?(Array)
            if node[:children].empty?
              node.delete(:children)
            else
              remove_empty_children(node[:children])
            end
          end
        end
      end
    end
  end
end
