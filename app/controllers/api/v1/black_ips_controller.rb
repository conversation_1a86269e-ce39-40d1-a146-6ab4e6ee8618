module Api
  module V1
    class BlackIpsController < ApplicationController
      before_action :set_black_ip, only: [ :show, :update, :destroy ]

      # GET /api/v1/black_ips
      # 获取黑名单IP列表
      # 参数:
      # - size: 每页大小, 默认20, 最小2, 最大200
      # - current: 当前页码, 默认1
      # - search: 搜索关键字, 模糊匹配ip
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)

        # add search params
        search_params = params[:search]
        if search_params.present?
          black_ip_scope = BlackIp.where("ip LIKE ?", "%#{search_params}%")
        else
          black_ip_scope = BlackIp.all
        end
        @pagy, @black_ips = pagy(black_ip_scope, page: current, limit: size)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: {
            current: @pagy.page,
            total: @pagy.count,
            size: @pagy.limit,
            pages: @pagy.pages,
            records: @black_ips.map { |black_ip| format_black_ip(black_ip) }
          }
        }
      end

      def show
        render json: {
          code: "0000",
          msg: "success",
          data: @black_ip
        }
      end

      def create
        @black_ip = BlackIp.new(black_ip_params)
        @black_ip.created_by = current_admin&.name
        if @black_ip.save
          render json: {
            code: "0000",
            msg: "success",
            data: @black_ip
          }, status: :created
        else
          render json: {
            code: "0001",
            msg: @black_ip.errors.full_messages.join(", ")
          }
        end
      end

      def update
        if @black_ip.update(black_ip_params)
          render json: {
            code: "0000",
            msg: "请求成功",
            data: @black_ip
          }
        else
          render json: {
            code: "0001",
            msg: @black_ip.errors.full_messages.join(", ")
          }
        end
      end

      # DELETE /api/v1/black_ips/:id
      def destroy
        @black_ip.destroy
        render json: {
          code: "0000",
          data: nil,
          msg: "请求成功"
        }
      end

      private

      def set_black_ip
        @black_ip = BlackIp.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: {
          code: "4040",
          msg: "Black IP not found"
        }, status: :not_found
      end

      def black_ip_params
        params.require(:black_ip).permit(:ip, :remark)
      end

      def format_black_ip(black_ip)
        {
          id: black_ip.id,
          ip: black_ip.ip,
          remark: black_ip.remark,
          created_by: black_ip.created_by,
          created_at: black_ip.created_at&.strftime("%Y-%m-%d %H:%M:%S")
        }
      end
    end
  end
end
