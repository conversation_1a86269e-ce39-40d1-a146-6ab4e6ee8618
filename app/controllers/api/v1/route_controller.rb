class Api::V1::RouteController < ApplicationController
  # skip_before_action :authenticate_admin!, only: [ :constant_routes ]

  # GET /api/v1/route/user_routes
  # Response:
  # {
  #   "data": {
  #     "routes": [],
  #     "home": "string"
  #   },
  #   "code": "0000",
  #   "msg": "请求成功"
  # }
  def user_routes
    Rails.logger.debug "Current admin: #{current_admin.inspect}"
    Rails.logger.debug "Current admin roles: #{current_admin.roles.pluck(:code).join(', ')}"

    # 获取用户所有角色关联的菜单ID
    accessible_menu_ids = Menu.joins(:roles)
                            .where(roles: { id: current_admin.roles.pluck(:id) })
                            .where(status: "active")
                            .distinct
                            .pluck(:id)

    # 获取所有可访问菜单的父菜单ID
    parent_menu_ids = Menu.where(id: accessible_menu_ids)
                         .pluck(:parent_id)
                         .compact
                         .uniq

    # 获取所有可访问菜单及其父菜单
    accessible_menus = Menu.where(id: accessible_menu_ids + parent_menu_ids)
                          .includes(:children)
                          .order(:order)

    Rails.logger.debug "Accessible menus: #{accessible_menus.map(&:inspect).join(', ')}"

    routes = build_routes_from_menus(accessible_menus)
    Rails.logger.debug "Built routes: #{routes.inspect}"

    response = {
      data: {
        routes: routes,
        home: "home"
      },
      code: "0000",
      msg: "请求成功"
    }

    Rails.logger.debug "Response: #{response.inspect}"

    render json: response
  end

  # GET /api/v1/route/constant_routes
  # Response:
  # [
  #   {
  #     "name": "root",
  #     "path": "/",
  #     "redirect": "/home",
  #     "meta": {
  #       "title": "首页",
  #       "constant": true,
  #       "hideInMenu": true
  #     }
  #   },
  #   {
  #     "name": "login",
  #     "path": "/login",
  #     "component": "layout.blank$view.login",
  #     "meta": {
  #       "title": "登录",
  #       "icon": "mdi:login",
  #       "constant": true,
  #       "hideInMenu": true
  #     }
  #   },
  #   {
  #     "name": "403",
  #     "path": "/403",
  #     "component": "layout.blank$view.403",
  #     "meta": {
  #       "title": "403",
  #       "icon": "mdi:alert-circle",
  #       "constant": true,
  #       "hideInMenu": true
  #     }
  #   },
  #   {
  #     "name": "404",
  #     "path": "/404",
  #     "component": "layout.blank$view.404",
  #     "meta": {
  #       "title": "404",
  #       "icon": "mdi:alert-circle",
  #       "constant": true,
  #       "hideInMenu": true
  #     }
  #   },
  #   {
  #     "name": "500",
  #     "path": "/500",
  #     "component": "layout.blank$view.500",
  #     "meta": {
  #       "title": "500",
  #       "icon": "mdi:alert-circle",
  #       "constant": true,
  #       "hideInMenu": true
  #     }
  #   }
  # ]
  def constant_routes
    routes_array = [
      {
        name: "root",
        path: "/",
        redirect: "/home",
        meta: {
          title: "首页",
          constant: true,
          hide_in_menu: true
        }
      },
      {
        name: "login",
        path: "/login",
        component: "layout.blank$view.login",
        meta: {
          title: "登录",
          icon: "mdi:login",
          constant: true,
          hide_in_menu: true
        }
      },
      {
        name: "403",
        path: "/403",
        component: "layout.blank$view.403",
        meta: {
          title: "403",
          icon: "mdi:alert-circle",
          constant: true,
          hide_in_menu: true
        }
      },
      {
        name: "404",
        path: "/404",
        component: "layout.blank$view.404",
        meta: {
          title: "404",
          icon: "mdi:alert-circle",
          constant: true,
          hide_in_menu: true
        }
      },
      {
        name: "500",
        path: "/500",
        component: "layout.blank$view.500",
        meta: {
          title: "500",
          icon: "mdi:alert-circle",
          constant: true,
          hide_in_menu: true
        }
      }
    ]

    Rails.logger.debug "Constant routes: #{routes_array.inspect}"

    render json: routes_array
  end

  # GET /api/v1/route/route_exist
  # Response:
  # {
  #   "data": {
  #     "exist": true
  #   },
  #   "code": "0000",
  #   "msg": "请求成功"
  # }
  def route_exist
    path = params[:path]
    Rails.logger.debug "Checking route existence for path: #{path}"

    # 获取用户所有角色关联的菜单
    accessible_menus = Menu.joins(:roles)
                          .where(roles: { id: current_admin.roles.pluck(:id) })
                          .where(status: "active")
                          .distinct

    exist = accessible_menus.any? { |menu| menu.path == path }

    Rails.logger.debug "Route exists: #{exist}"

    render json: {
      data: {
        exist: exist
      },
      code: "0000",
      msg: "请求成功"
    }
  end

  private

  def build_routes_from_menus(menus)
    # 只处理顶级菜单
    menus.select { |menu| menu.parent_id.nil? }.map do |menu|
      route = {
        name: menu.route_name,
        path: menu.path,
        component: menu.component,
        meta: {
          title: menu.title,
          icon: menu.icon,
          order: menu.order,
          i18nKey: "route.#{menu.route_name}"
        }
      }

      if menu.children.any?
        # 只包含用户有权限访问的子菜单
        accessible_children = menu.children.select { |child| menus.include?(child) }
        if accessible_children.any?
          route[:children] = accessible_children.map do |child|
            {
              name: child.route_name,
              path: child.path,
              component: child.component,
              meta: {
                title: child.title,
                icon: child.icon,
                order: child.order,
                i18nKey: "route.#{child.route_name}"
              }
            }
          end
        end
      else
        # 对于没有子菜单的顶级菜单，使用 layout$view 格式
        route[:component] = menu.component
      end

      route
    end
  end
end
