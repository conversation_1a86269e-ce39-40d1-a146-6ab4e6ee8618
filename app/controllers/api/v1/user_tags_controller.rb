module Api
  module V1
    class UserTagsController < ApplicationController
      before_action :set_user

      def index
        @tags = @user.tags.active
        render json: @tags
      end

      def create
        @tag = Tag.find(params[:tag_id])
        @user.tags << @tag unless @user.tags.include?(@tag)
        render json: @user.tags, status: :created
      rescue ActiveRecord::RecordNotFound
        render json: { error: "Tag not found" }, status: :not_found
      end

      def destroy
        @tag = @user.tags.find(params[:id])
        @user.tags.delete(@tag)
        head :no_content
      rescue ActiveRecord::RecordNotFound
        render json: { error: "Tag not found" }, status: :not_found
      end

      private

      def set_user
        @user = User.find(params[:user_id])
      end
    end
  end
end
