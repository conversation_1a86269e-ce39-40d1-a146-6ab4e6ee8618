module Api
  module V1
    class WithdrawsController < ApplicationController
      before_action :set_withdraw, only: [ :show, :update ]

      # GET /api/v1/withdraws
      # params:
      #   size: 每页条数
      #   current: 当前页码
      #   user_id:  用户ID
      #   username: 用户名
      #   order_no: 平台订单号
      #   risk: 是否风控
      #   status: 状态，0=待支付 1=完成 2=拒绝 3=没收
      #   start_time ~ end_time 查询时间范围
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)
        current = 1 if current < 1

        # Build search query
        query = Withdraw.left_joins(:user, :channel, :agent).select("withdraws.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
        query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
        query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
        query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
        query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
        query = query.where(status: params[:status].to_i) if params[:status].present?

        @pagy, @withdraws = pagy(query, limit: size, page: current)

        render json: {
          code: "0000",
          msg: "success",
          data: {
            records: @withdraws,
            total: @pagy.count,
            size: size,
            current: current
          }
        }
      end

      # GET /api/v1/withdraws/1
      def show
        render json: {
          code: "0000",
          msg: "请求成功",
          data: @withdraw
        }
      end

      # PATCH/PUT /api/v1/withdraws/1
      def update
        # 开启事务
        begin
          if @withdraw.status!=0
            render json: { code: "0001", msg: "已经处理过了", data: nil }
            return
          end
          ActiveRecord::Base.transaction do
            @withdraw.update(withdraw_params)
            status = withdraw_params[:status].to_i
            if status==2
              # 退回余额
              wallet = Wallet.find_by(user_id: @withdraw.user_id)
              wallet.balance = wallet.balance + @withdraw.amount
              wallet.save!
            end
            save_log(status)
          end
        rescue => e
          render json: { code: "0001", msg: "修改失败,#{e.message}", data: nil }
          return
        end
        render json: { code: "0000", msg: "修改成功", data: nil }
      end

      private

      def set_withdraw
        @withdraw = Withdraw.find(params[:id])
      end

      def withdraw_params
        params.require(:withdraw).permit(
          :status,
          :risk,
          :remark
        )
      end

      # 记录操作日志
      def save_log(status)
        @log = SystemLog.new(
            admin_id: current_admin&.id,
            admin_name: current_admin&.name,
            operate_type: 2,
            ip: request.remote_ip,
            user_id: @withdraw.user_id,
            ordre_no: @withdraw.order_no
          )
          status = withdraw_params[:status].to_i
          case status
          when 1
            # 提款成功
            @log.operate = 5
            @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现成功"
          when 2
            # 拒绝
            @log.operate = 6
            @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现拒绝"
          when 3
            # 没收
            @log.operate = 7
            @log.operate_desc = "用户ID #{@withdraw[:user_id]},订单号#{@withdraw[:order_no]},提现没收"
          else
            @log.operate_desc = "未知状态操作"
          end
          @log.save if @log.operate.present?
        end
    end
  end
end
