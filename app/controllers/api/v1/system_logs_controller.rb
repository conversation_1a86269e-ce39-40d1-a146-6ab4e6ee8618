module Api
  module V1
    class SystemLogsController < ApplicationController
      # GET /api/v1/system_logs
      # param
      # current 当前页
      # size 每页大小
      # admin_name 管理员
      # start_time ~ end_time 查询时间范围
      def index
        current = (params[:current].presence || 1).to_i
        size = (params[:size].presence || 10).to_i

        # Build search query
        query = SystemLog.order(id: :desc)
        query = query.where("admin_name = ?", "#{params[:admin_name]}") if params[:admin_name].present?
        query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
        query = query.where("operate = ?", "#{params[:operate]}") if params[:operate].present?
        query = query.where("operate_type = ?", "#{params[:operate_type]}") if params[:operate_type].present?
        query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
        query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
        @pagy, @logs = pagy(query, limit: size, page: current)

        render json: {
          code: "0000",
          msg: "success",
          data: {
            records: @logs,
            total: @pagy.count,
            size: size,
            current: current
          }
        }
      end
    end
  end
end
