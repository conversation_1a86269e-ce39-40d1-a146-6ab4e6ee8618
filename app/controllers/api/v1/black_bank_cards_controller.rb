module Api
  module V1
    class BlackBankCardsController < ApplicationController
      before_action :set_black_bank_card, only: [ :show, :update, :destroy ]

      # GET /api/v1/black_bank_cards
      # 获取黑名单银行卡列表
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)

        # add search params
        search_params = params[:search]
        if search_params.present?
          black_bank_cards_scope = BlackBankCard.where("card_number LIKE ?", "%#{search_params}%")
        else
          black_bank_cards_scope = BlackBankCard.all
        end

        @pagy, @black_bank_cards = pagy(black_bank_cards_scope, page: current, limit: size)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: {
            current: @pagy.page,
            total: @pagy.count,
            size: @pagy.limit,
            pages: @pagy.pages,
            records: @black_bank_cards.map { |card| format_black_bank_card(card) }
          }
        }
      end

      # GET /api/v1/black_bank_cards/:id
      # 获取黑名单银行卡详情
      def show
        render json: {
          code: "0000",
          msg: "请求成功",
          data: format_black_bank_card(@black_bank_card)
        }
      end

      # POST /api/v1/black_bank_cards
      # 创建黑名单银行卡
      def create
        @black_bank_card = BlackBankCard.new(black_bank_card_params)
        @black_bank_card.created_by = current_admin&.name

        if @black_bank_card.save
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_black_bank_card(@black_bank_card)
          }, status: :created
        else
          render json: {
            code: "0001",
            msg: @black_bank_card.errors.full_messages.join(", ")
          }
        end
      end

      # PUT /api/v1/black_bank_cards/:id
      # 更新黑名单银行卡
      def update
        if @black_bank_card.update(black_bank_card_params)
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_black_bank_card(@black_bank_card)
          }
        else
          render json: {
            code: "0001",
            msg: @black_bank_card.errors.full_messages.join(", ")
          }
        end
      end

      # DELETE /api/v1/black_bank_cards/:id
      # 删除黑名单银行卡
      def destroy
        @black_bank_card.destroy
        render json: {
          code: "0000",
          msg: "请求成功",
          data: nil
        }
      end

      private

      def set_black_bank_card
        @black_bank_card = BlackBankCard.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: {
          code: "4040",
          msg: "Black bank card not found"
        }, status: :not_found
      end

      def black_bank_card_params
        params.require(:black_bank_card).permit(:card_number, :remark, :status)
      end

      def format_black_bank_card(card)
        {
          id: card.id,
          card_number: card.card_number,
          remark: card.remark,
          status: card.status,
          created_by: card.created_by,
          created_at: card.created_at&.strftime("%Y-%m-%d %H:%M:%S")
        }
      end
    end
  end
end
