module Api
  module V1
    class AgentsController < ApplicationController
      before_action :set_agent, only: [ :show, :update, :destroy ]

      # GET /api/v1/agents
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)

        # add search params
        search_params = params[:search]
        if search_params.present?
          agent_scope = Agent.where("name LIKE ?", "%#{search_params}%")
        else
          agent_scope = Agent.all
        end
        @pagy, @agents = pagy(agent_scope, limit: size, page: current)
        render json: {
          code: "0000",
          msg: "请求成功",
          data: {
            current: @pagy.page,
            total: @pagy.count,
            size: @pagy.limit,
            pages: @pagy.pages,
            records: @agents.map { |agent| format_agent(agent) }
          }
        }
      end

      # GET /api/v1/agents/1
      def show
        render json: @agent
      end

      # POST /api/v1/agents
      def create
        @agent = Agent.new(agent_params)
        @agent.password ||= SecureRandom.hex(12)
        @agent.totp_secret = ROTP::Base32.random_base32
        @agent.created_by = current_admin&.name
        @agent.timezone ||= "UTC"

        if @agent.save
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_agent(@agent)
          }, status: :created
        else
          render json: {
            code: "0001",
            msg: @agent.errors.full_messages.join(", "),
            data: nil
          }
        end
      end

      # PATCH/PUT /api/v1/agents/1
      def update
        if @agent.update(agent_params)
          render json: {
            code: "0000",
            msg: "请求成功",
            data: format_agent(@agent)
          }
        else
          render json: {
            code: "0001",
            msg: @agent.errors.full_messages.join(", "),
            data: nil
          }
        end
      end

      # DELETE /api/v1/agents/1
      def destroy
        @agent.destroy
        render json: {
          code: "0000",
          msg: "请求成功",
          data: nil
        }
      end

      private

      def set_agent
        @agent = Agent.find(params[:id])
      end

      def agent_params
        params.require(:agent).permit(
          :name,
          :nickname,
          :password,
          :email_address,
          :mobile,
          :avatar,
          :remark,
          :status,
          :timezone
        )
      end

      def format_agent(agent)
        {
          id: agent.id,
          name: agent.name,
          nickname: agent.nickname,
          email_address: agent.email_address,
          mobile: agent.mobile,
          remark: agent.remark,
          status: agent.status_before_type_cast,
          status_name: agent.status,
          created_at: agent.created_at&.strftime("%Y-%m-%d %H:%M:%S"),
          created_by: agent.created_by,
          timezone: agent.timezone,
          avatar: agent.avatar,
          last_login_at: agent.last_login_at&.strftime("%Y-%m-%d %H:%M:%S"),
          last_login_ip: agent.last_login_ip,
          last_login_user_agent: agent.last_login_user_agent,
          login_count: agent.login_count,
          totp_secret: agent.totp_secret
        }
      end
    end
  end
end
