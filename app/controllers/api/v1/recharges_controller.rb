module Api
  module V1
    class RechargesController < ApplicationController
      before_action :set_recharge, only: [ :show ]

      # GET /api/v1/recharges
      # params:
      #   size: 每页条数
      #   current: 当前页码
      #   user_id:  用户ID
      #   username: 用户名
      #   order_no: 平台订单号
      #   first_recharge: 是否首充
      #   status: 状态，0=待支付 1=支付完成 2=支付失败
      #   start_time ~ end_time 查询时间范围
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)
        current = 1 if current < 1

        # Build search query
        query = Recharge.left_joins(:user, :channel, :agent).select("recharges.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
        query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
        query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
        query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
        query = query.where("first_recharge = ?", "#{params[:first_recharge]}") if params[:first_recharge].present?
        query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
        query = query.where(status: params[:status].to_i) if params[:status].present?

        @pagy, @recharges = pagy(query, limit: size, page: current)

        render json: {
          code: "0000",
          msg: "success",
          data: {
            records: @recharges,
            total: @pagy.count,
            size: size,
            current: current
          }
        }
      end

      # GET /api/v1/recharges/1
      def show
        render json: {
          code: "0000",
          msg: "请求成功",
          data: @recharge
        }
      end

      private

      def set_recharge
        @recharge = Recharge.find(params[:id])
      end
    end
  end
end
