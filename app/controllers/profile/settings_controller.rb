module Profile
  class SettingsController < ApplicationController
    def show
      @admin = Current.user
    end

    def update
      @admin = Current.user
      if @admin.update(admin_params)
        redirect_to profile_setting_path, notice: "个人设置已更新"
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def admin_params
      params.require(:admin).permit(:nickname)
    end
  end
end
