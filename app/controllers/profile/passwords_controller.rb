module Profile
  class PasswordsController < ApplicationController
    def show
      @admin = Current.admin
    end

    def update
      @admin = Current.admin

      if @admin.authenticate(admin_params[:old_password])
        if @admin.update(admin_params.except(:old_password))
          redirect_to profile_password_path, notice: "密码已更新"
        else
          render :show, status: :unprocessable_entity
        end
      else
        @admin.errors.add(:old_password, "错误")
        render :show, status: :unprocessable_entity
      end
    end

    private

    def admin_params
      params.require(:admin).permit(:old_password, :password, :password_confirmation)
    end
  end
end
