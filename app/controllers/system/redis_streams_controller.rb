class System::RedisStreamsController < ApplicationController
  # GET /system/redis_streams
  def index
    @stream_key = RedisStreamProcessor::STREAM_KEY
    @group_name = RedisStreamProcessor::GROUP_NAME
    @consumer_name = RedisStreamProcessor::CONSUMER_NAME

    Rails.cache.redis.with do |redis_client|
      @stream_info = get_stream_info(redis_client)
      @group_info = get_group_info(redis_client)
      @pending_info = get_pending_info(redis_client)
      @consumers_info = get_consumers_info(redis_client)

      @read_count = @group_info.first&.dig("entries-read") || 0
      @unread_count = @stream_info["length"] - @read_count

      # how to get time in redis with time cmd
      @redis_time = redis_client.time.first
    end
  end

  # 清空队列
  # DEL /system/redis_streams/clear_stream
  def clear_stream
    @stream_key = RedisStreamProcessor::STREAM_KEY
    @group_name = RedisStreamProcessor::GROUP_NAME
    @consumer_name = RedisStreamProcessor::CONSUMER_NAME

    Rails.cache.redis.with do |redis_client|
      redis_client.del(@stream_key)
      redis_client.xgroup(:create, RedisStreamProcessor::STREAM_KEY, RedisStreamProcessor::GROUP_NAME, "0", mkstream: true)
    end
    respond_to do |format|
      format.html { redirect_to system_redis_streams_path, notice: "队列已清空" }
    end
  end

  private

  def get_stream_info(redis_client)
      message = redis_client.xinfo(:stream, @stream_key)
      message
  rescue Redis::CommandError => e
    # 当 stream 不存在时返回默认值
    {
      "length" => 0,
      "last-generated-id" => "0-0",
      "entries-read" => 0,
      "groups" => 0,
      "first-entry" => nil,
      "last-entry" => nil
    }
  end

  def get_group_info(redis_client)
      group_info = redis_client.xinfo(:groups, @stream_key)
      group_info
  rescue Redis::CommandError => e
    # 当 stream 不存在时返回空数组
    []
  end

  def get_pending_info(redis_client)
    begin
      # xpending 命令返回格式：{"size" => count, "min_entry_id" => min_id, "max_entry_id" => max_id, "consumers" => {...}}
      result = redis_client.xpending(@stream_key, @group_name)
      {
        "count" => result["size"],
        "min" => result["min_entry_id"],
        "max" => result["max_entry_id"]
      }
    rescue Redis::CommandError => e
      Rails.logger.error "Failed to get pending info: #{e.message}"
      # 当 stream 或 group 不存在时返回默认值
      {
        "count" => 0,
        "min" => "0-0",
        "max" => "0-0"
      }
    end
  end

  def get_consumers_info(redis_client)
    begin
      consumers = redis_client.xinfo(:consumers, @stream_key, @group_name)

      # 获取每个消费者的待处理消息数
      consumers.each do |consumer|
        # 获取该消费者的所有待处理消息数
        pending = redis_client.xpending(@stream_key, @group_name, "-", "+", 1000, consumer["name"])
        # xpending 返回的是一个数组，数组长度就是待处理消息数
        consumer["pending_count"] = pending.size
      end

      consumers
    rescue Redis::CommandError => e
      Rails.logger.error "Failed to get consumers info: #{e.message}"
      # 当 stream 或 group 不存在时返回空数组
      []
    end
  end
end
