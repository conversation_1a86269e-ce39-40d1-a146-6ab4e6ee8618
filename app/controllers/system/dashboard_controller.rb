class System::DashboardController < ApplicationController
  # GET /system/system_info
  def system_info
    @app_time = Time.current
    @app_timezone = Time.zone.name

    # Get server itself timezone info
    @server_timezone_info = `timedatectl show`.strip.split("\n")

    @uptime = `uptime`.strip
    @memory_info = `free -h`.strip.split("\n")
    @disk_info = `df -h`.strip.split("\n")
    @cpu_info = `top -bn1 | head -n 5`.strip.split("\n")
  end
end
