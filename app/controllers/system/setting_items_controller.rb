class System::SettingItemsController < ApplicationController
  before_action :set_setting_item, only: [ :edit, :update ]

  # GET /system/setting_items
  def index
    @sections = Setting.select(:section).distinct.pluck(:section)
    @section = params[:section] || @sections.first
    @setting_items = Setting.where(section: @section).order(sort: :asc)

    # For redis cache inspect check purpose
    @section_settings = Setting.get_section(@section)
  end

  # GET /system/setting_items/:id/edit
  def edit
    @sections = Setting.select(:section).distinct.pluck(:section)
    @section = params[:section] || @sections.first
  end

  # PATCH/PUT /system/setting_items/:id
  def update
    old_status = @setting_item.status
    if @setting_item.update(setting_item_params)
      Setting.notify_section_refreshed(@setting_item.section)
      Setting.notify_all_refreshed

      # Record system log based on the operation type
      if setting_item_params[:status].present? && setting_item_params[:status] != old_status
        operate = setting_item_params[:status] == "enabled" ? :enable_setting : :disable_setting
        SystemLog.log(
          actor: Current.admin,
          loggable: @setting_item,
          operate: operate,
          action: "Setting #{@setting_item.section_and_key} #{operate.to_s.split('_').first}ed",
          snapshot: setting_item_params.to_h
        )
      else
        SystemLog.log(
          actor: Current.admin,
          loggable: @setting_item,
          operate: :update_setting,
          action: "Setting #{@setting_item.section_and_key} updated",
          snapshot: setting_item_params.to_h
        )
      end

      redirect_to system_setting_items_path(section: @setting_item.section), notice: "设置 <#{@setting_item.section_and_key}> 更新成功"
    else
      @sections = Setting.select(:section).distinct.pluck(:section)
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_setting_item
    @setting_item = Setting.find(params[:id])
  end

  def setting_item_params
    params.require(:setting).permit(:label, :value, :value_type, :options, :description, :status, :sort)
  end
end
