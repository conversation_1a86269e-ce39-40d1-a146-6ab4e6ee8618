class BackManage::SystemLogsController < ApplicationController
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    system_log_scope = SystemLog.includes(:actor, :loggable).order(created_at: :desc)

    # 搜索条件
    if params[:search].present?
      system_log_scope = system_log_scope.where("action LIKE ?", "%#{params[:search]}%")
    end

    if params[:operate].present?
      system_log_scope = system_log_scope.where(operate: params[:operate])
    end

    if params[:actor_type].present?
      system_log_scope = system_log_scope.where(actor_type: params[:actor_type])
    end

    if params[:loggable_type].present?
      system_log_scope = system_log_scope.where(loggable_type: params[:loggable_type])
    end

    if params[:start_date].present?
      system_log_scope = system_log_scope.where("created_at >= ?", params[:start_date].to_date.beginning_of_day)
    end

    if params[:end_date].present?
      system_log_scope = system_log_scope.where("created_at <= ?", params[:end_date].to_date.end_of_day)
    end

    @pagy, @system_logs = pagy(system_log_scope, limit: size, page: current)
  end
end
