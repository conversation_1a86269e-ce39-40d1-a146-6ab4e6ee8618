class BackManage::AdminsController < ApplicationController
  before_action :set_admin, only: [ :show, :edit, :update, :destroy, :reset_password ]

  # GET /api/v1/admins
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    if search_params.present?
      admin_scope = Admin.where("name LIKE ? OR nickname LIKE ?", "%#{search_params}%", "%#{search_params}%")
    else
      admin_scope = Admin.all
    end

    @pagy, @admins = pagy(admin_scope, limit: size, page: current)
    if params[:include_deleted].present?
      @admins = @admins.with_deleted
    end
  end

  # GET /admins/:id
  def show
  end

  def edit
  end

  def new
    @admin = Admin.new
  end

  # POST /admins
  def create
    # Extract role_ids before creating admin
    role_ids = params[:admin][:role_ids]
    admin_creation_params = admin_params.except(:role_ids)

    @admin = Admin.new(admin_creation_params)
    @admin.email_address ||= "u#{Time.current.to_i}_#{rand(1000)}@example.com"
    @admin.password = "zx4dX12sue3456"

    if @admin.save
      if role_ids.present?
        roles = Role.where(id: role_ids)
        roles.each { |role| @admin.add_role(role) }
      end
      SystemLog.log(
        actor: Current.admin,
        loggable: @admin,
        operate: :create,
        action: "创建管理员",
        snapshot: {
          name: @admin.name,
          email: @admin.email_address,
          roles: @admin.role_names
        }
      )
      redirect_to edit_admin_path(@admin), notice: "管理员帐号添加成功"
    else
      flash.now[:alert] = "管理员帐号创建失败"
      render :new
    end
  end

  # PUT /admins/:id
  def update
    # admin_params need except role_ids
    admin_params_without_role_ids = admin_params.except(:role_ids)
    old_roles = @admin.role_names

    if @admin.update(admin_params_without_role_ids)
      # Always handle role updates
      new_role_ids = params[:admin][:role_ids] || []
      new_role_ids = new_role_ids.map(&:to_i).sort
      current_role_ids = @admin.role_ids.sort

      # Only update if roles have changed
      if new_role_ids != current_role_ids
        # Remove roles that are no longer needed
        @admin.roles.each do |role|
          @admin.remove_role(role) unless new_role_ids.include?(role.id)
        end
        # Add new roles
        roles = Role.where(id: new_role_ids)
        roles.each do |role|
          @admin.add_role(role) unless @admin.has_role?(role.id)
        end
      end

      SystemLog.log(
        actor: Current.admin,
        loggable: @admin,
        operate: :update,
        action: "更新管理员",
        snapshot: {
          name: @admin.name,
          email: @admin.email_address,
          old_roles: old_roles,
          new_roles: @admin.role_names,
          changes: @admin.previous_changes
        }
      )
      redirect_to edit_admin_path(@admin), notice: "管理员帐号更新成功"
    else
      redirect_to edit_admin_path(@admin), alert: "管理员帐号更新失败"
    end
  end

  # POST /admins/:id/reset_password
  def reset_password
    password = params[:password].presence || SecureRandom.hex(8)

    if password.length < 8
      flash.now[:alert] = "密码长度不能少于8位"
      return render turbo_stream: turbo_stream.update(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    end

    begin
      @admin.reset_password(password)
    rescue ActiveRecord::RecordInvalid => e
      flash.now[:alert] = "密码重置失败: #{e.message}"
      render turbo_stream: turbo_stream.replace(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    else
      SystemLog.log(
        actor: Current.admin,
        loggable: @admin,
        operate: :reset_admin_password,
        action: "重置管理员密码",
        snapshot: {
          name: @admin.name,
          email: @admin.email_address
        }
      )
      render turbo_stream: turbo_stream.replace(
        "modal",
        partial: "back_manage/admins/password_modal",
        locals: { password: password }
      )
    end
  end

  # DELETE /admins/:id
  def destroy
    if @admin.soft_delete
      SystemLog.log(
        actor: Current.admin,
        loggable: @admin,
        operate: :delete,
        action: "删除管理员",
        snapshot: {
          name: @admin.name,
          email: @admin.email_address,
          roles: @admin.role_names
        }
      )
      redirect_to admins_path, notice: "管理员帐号删除成功"
    else
      redirect_to admins_path, alert: "管理员帐号删除失败"
    end
  end

  private

  def set_admin
    @admin = Admin.find(params[:id])
  end

  def admin_params
    params.require(:admin).permit(:name, :email_address, :nickname, :password, :mobile, :department, :status, :avatar, :remark, role_ids: [])
  end
end
