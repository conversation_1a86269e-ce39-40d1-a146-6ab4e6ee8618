class BackManage::RolesController < ApplicationController
  before_action :set_role, only: [ :edit, :update, :destroy ]
  # GET /roles
  # params:
  #   size: 每页条数
  #   current: 当前页码
  #   role_name: 角色名称
  #   role_code: 角色编码
  #   status: 状态
  def index
    size = (params[:size].presence || 20).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # Build search query
    query = Role.order(id: :asc)
    query = query.where("name LIKE ?", "%#{params[:role_name]}%") if params[:role_name].present?
    query = query.where("code LIKE ?", "%#{params[:role_code]}%") if params[:role_code].present?
    query = query.where(status: params[:status]) if params[:status].present?

    @pagy, @roles = pagy(query, limit: size, page: current)
  end

  def new
    @role = Role.new
  end

  def edit
  end

  def all
    @roles = Role.where(status: "active")

    render json: {
      code: "0000",
      msg: "success",
      data: @roles.map { |role|
        {
          id: role.id,
          role_name: role.name,
          role_code: role.code
        }
      }
    }
  end

  # POST /roles
  def create
    @role = Role.new(role_params)
    if @role.save
      SystemLog.log(
        actor: Current.admin,
        loggable: @role,
        operate: :create,
        action: "创建角色",
        snapshot: {
          name: @role.name,
          code: @role.code,
          description: @role.description,
          status: @role.status
        }
      )
      redirect_to back_manage_roles_path, notice: "角色创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PUT /roles/:id
  # params:
  #   role_name: 角色名称
  #   role_code: 角色编码
  #   role_desc: 角色描述
  #   menu_ids: 菜单ID (array), optional
  def update
    role_params_without_menu_ids = role_params.except(:menu_ids)
    old_menus = @role.menu_ids

    if @role.update(role_params_without_menu_ids)
      new_menu_ids = params[:role][:menu_ids] || []
      new_menu_ids = new_menu_ids.map(&:to_i).sort
      current_menu_ids = @role.menu_ids.sort
      if new_menu_ids != current_menu_ids
        @role.menus.each do |menu|
          @role.remove_menu(menu) unless new_menu_ids.include?(menu.id)
        end
        menus = Menu.where(id: new_menu_ids)
        menus.each do |menu|
          @role.add_menu(menu) unless @role.has_menu?(menu.id)
        end
      end

      SystemLog.log(
        actor: Current.admin,
        loggable: @role,
        operate: :update,
        action: "更新角色",
        snapshot: {
          name: @role.name,
          code: @role.code,
          description: @role.description,
          status: @role.status,
          old_menus: old_menus,
          new_menus: @role.menu_ids,
          changes: @role.previous_changes
        }
      )
      redirect_to edit_role_path(@role), notice: "角色更新成功"
    else
      redirect_to edit_role_path(@role), alert: "角色更新失败"
    end
  end

  # GET /roles/:id/menus
  def get_menus
    role = Role.find(params[:id])
    menu_ids = role.menus.pluck(:id)

    render json: {
      code: "0000",
      msg: "success",
      data: menu_ids
    }
  end

  # PUT /roles/:id/menus
  def update_menus
    role = Role.find(params[:id])
    menu_ids = params[:menu_ids]

    # 清除现有菜单关联
    role.menus.clear

    # 添加新的菜单关联
    if menu_ids.present?
      menus = Menu.where(id: menu_ids)
      role.menus << menus
    end
  end

  # DELETE /roles/:id
  def destroy
    @role = Role.find(params[:id])
    if @role.destroy
      SystemLog.log(
        actor: Current.admin,
        loggable: @role,
        operate: :destroy_role,
        action: "删除角色",
        snapshot: {
          name: @role.name,
          code: @role.code,
          description: @role.description,
          status: @role.status,
          menus: @role.menu_ids
        }
      )
      redirect_to back_manage_roles_path, notice: "角色删除成功"
    else
      redirect_to back_manage_roles_path, alert: "角色删除失败"
    end
  end

  private

  def role_params
    params.require(:role).permit(:name, :code, :description, :status, menu_ids: [])
  end

  def set_role
    @role = Role.find(params[:id])
  end
end
