class User::UsersController < ApplicationController
  before_action :set_user, only: [ :edit, :update, :destroy, :show ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = User.left_joins(:wallet, :wallet_ext, :register_track).select("wallets.*,wallet_exts.*,register_tracks.*,users.*").where("status < ?", 2).order(id: :desc)
    query = query.where("users.id = ? or users.name = ?", "#{params[:search]}", "#{params[:search]}") if params[:search].present?
    query = query.where("register_tracks.agent_id = ?", "#{params[:agent_id]}") if params[:agent_id].present?
    query = query.where("register_tracks.channel_id = ?", "#{params[:channel_id]}") if params[:channel_id].present?
    query = query.where("register_tracks.parent_id = ?", "#{params[:parent_id]}") if params[:parent_id].present?
    @pagy, @users = pagy(query, limit: size, page: current)
  end

  def new
    @user = User.new
  end

  # POST /payment_type
  def create
    @user = User.new(user_params)
    if @user.save
      redirect_to user_users_path, notice: "创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  # PATCH/PUT
  def update
    if @user.update(user_params)
      redirect_to user_users_path, notice: "修改成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
     params={
      status: 2,
      deleted_at: Time.current
      }
     if @user.update(params)
      redirect_to user_users_path, notice: "删除成功"
     else
      redirect_to user_users_path, alert: "删除失败"
     end
  end

  def show
    op = params[:op].presence
    if op
      case op
      when "profile"
        @user = User.includes(:register_track).find(params[:id])
        puts @user.inspect
        nil
      when "team"
        size = (params[:size].presence || 10).to_i.clamp(2, 2000)
        current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

        # add search params by name
        search_params = params[:search]
        query = User.left_joins(:wallet, :register_track).select("wallets.*,register_tracks.*,users.*").where("status < ?", 2).order(id: :desc)
        query = query.where("register_tracks.parent_id = ?", "#{@user.id}")
        @pagy, @users = pagy(query, limit: size, page: current)
      when "transaction"

      when "recharge"
        size = (params[:size].presence || 10).to_i.clamp(2, 2000)
        current = (params[:page].presence || 1).to_i.clamp(1, 1000000)
        query = Recharge.order(id: :desc)
        query = query.where("user_id = ? ", "#{@user.id}")

        @pagy, @recharges = pagy(query, limit: size, page: current)
      when "withdraw"
        size = (params[:size].presence || 10).to_i.clamp(2, 2000)
        current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

        # add search params by name
        search_params = params[:search]
        query = Withdraw.order(id: :desc)
        query = query.where("user_id = ? ", "#{@user.id}")

        @pagy, @withdraws = pagy(query, limit: size, page: current)
      when "game"
        size = (params[:size].presence || 10).to_i.clamp(2, 2000)
        current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

        start_time = if params[:start_time].present?
          Time.parse(params[:start_time]).in_time_zone("UTC")
        else
          Time.current.beginning_of_day
        end

        end_time = if params[:end_time].present?
          Time.parse(params[:end_time]).in_time_zone("UTC")
        else
          Time.current.end_of_day
        end

        # 👇 尝试从参数解析分页游标
        last_evaluated_key = nil
        if params[:lek_user_id].present? && params[:lek_time].present?
          lek_time = params[:lek_time].to_i
          if lek_time.between?(start_time.to_i, end_time.to_i)
            last_evaluated_key = {
              "tenantIdUserId" => params[:lek_user_id],
              "actionTimeEpoch" => lek_time
            }
          else
            puts "⚠️ lek_time 不在查询范围内，忽略游标"
          end
        end
        puts "start_time: #{start_time.inspect} #{start_time.class}"
        puts "end_time: #{end_time.inspect} #{end_time.class}"
        puts "last_evaluated_key: #{last_evaluated_key.inspect} #{last_evaluated_key.class}"
        result = DynamodbLogger.query_by_user(
          user_id: @user.id,
          tenant_id: "tenant_1",
          start_time: start_time,
          end_time: end_time,
          limit: size,
          last_evaluated_key: last_evaluated_key
        )

        @game_records = result[:items]
        @consumed_capacity = result[:consumed_capacity]
        @form_start_time = start_time.strftime("%Y-%m-%dT%H:%M")
        @form_end_time = end_time.strftime("%Y-%m-%dT%H:%M")

        if result[:last_evaluated_key].present?
          @next_page_params = {
            lek_user_id: result[:last_evaluated_key]["tenantIdUserId"],
            lek_time: result[:last_evaluated_key]["actionTimeEpoch"],
            start_time: start_time.strftime("%Y-%m-%dT%H:%M"),
            end_time: end_time.strftime("%Y-%m-%dT%H:%M"),
            size: size
          }
        end
      end
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:status, :remark)
  end
end
