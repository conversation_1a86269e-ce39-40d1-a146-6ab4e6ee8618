class User::TagsController < ApplicationController
  before_action :set_tag, only: [ :edit, :update, :destroy ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Tag.order(id: :desc)
    query = query.where("name = ?", "#{params[:search]}") if params[:search].present?
    @pagy, @tags = pagy(query, limit: size, page: current)
  end

  def new
    @tag = Tag.new
  end

  # POST /payment_type
  def create
    @tag = Tag.new(tag_params)
    @tag.created_by = current_admin&.name
    if @tag.save
      redirect_to user_tags_path, notice: "创建成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  # PATCH/PUT
  def update
    if @tag.update(tag_params)
      redirect_to user_tags_path, notice: "修改成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
     if @tag.destroy
      redirect_to user_tags_path, notice: "删除成功"
     else
      redirect_to user_tags_path, alert: "删除失败"
     end
  end

  private

  def set_tag
    @tag = Tag.find(params[:id])
  end

  def tag_params
    params.require(:tag).permit(:name, :status, :remark, :description)
  end
end
