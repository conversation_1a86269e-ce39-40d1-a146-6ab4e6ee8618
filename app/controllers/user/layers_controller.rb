class User::LayersController < ApplicationController
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    day = params[:day].to_s.strip

    date = day.present? ? Date.parse(day) : Date.current
    start_time = date.beginning_of_day
    # end_time = date.end_of_day
    end_time = (date + 1.day).beginning_of_day

    # add search params by name
    search_params = params[:search]
    case search_params
    when "reg_30m_unpay"
      # 注册30分钟内未支付
      # sql = <<-SQL
      #   SELECT u.*
      #   FROM users u
      #   WHERE u.created_at >= ?
      #     AND u.created_at < ?
      #     AND NOT EXISTS (
      #       SELECT 1
      #       FROM recharges r
      #       WHERE r.user_id = u.id AND r.status=1 AND r.first_recharge=1
      #         AND r.created_at BETWEEN u.created_at AND u.created_at + INTERVAL 30 MINUTE
      #     )
      # SQL
      # result = ActiveRecord::Base.connection.exec_query(sql, "SQL", [ [ nil, start_time ], [ nil, end_time ] ])
      # query = result.to_a
      query = User.select("users.*")
            .where("users.created_at >= ? AND users.created_at < ?", start_time, end_time)
            .where.not(
              "EXISTS (
                SELECT 1 FROM recharges r
                WHERE r.user_id = users.id AND r.status = 1 AND r.first_recharge = 1
                AND r.created_at BETWEEN users.created_at AND users.created_at + INTERVAL 30 MINUTE
              )"
            )

    when "reg_2h_unpay"
       # 注册2小时内未支付
       query = User.select("users.*")
            .where("users.created_at >= ? AND users.created_at < ?", start_time, end_time)
            .where.not(
              "EXISTS (
                SELECT 1 FROM recharges r
                WHERE r.user_id = users.id AND r.status = 1 AND r.first_recharge = 1
                AND r.created_at BETWEEN users.created_at AND users.created_at + INTERVAL 2 HOUR
              )"
            )
    when "reg_curday_unpay"
      # 注册当天内未支付
      query = User.select("users.*")
            .where("users.created_at >= ? AND users.created_at < ?", start_time, end_time)
            .where.not(
              "EXISTS (
                SELECT 1 FROM recharges r WHERE r.user_id = u.id AND r.status=1 AND r.first_recharge=1 AND r.created_at < ?
              )"
            )
    when "first_3d_unlogin"
      # 首充3天未登录的
      query = User.joins(:recharges).order(id: :desc)
      query = query.where("recharges.first_recharge = 1 AND users.created_at BETWEEN ? AND ?", start_time, end_time).where("users.last_login_at IS NULL OR users.last_login_at < created_at + INTERVAL 3 DAY")
    when "first_7d_unlogin"
      query = User.order(id: :desc)
      query = query.where("recharges.first_recharge = 1 AND users.created_at BETWEEN ? AND ?", start_time, end_time).where("users.last_login_at IS NULL OR users.last_login_at < created_at + INTERVAL 7 DAY")
    when "first_15d_unlogin"
      query = User.order(id: :desc)
      query = query.where("recharges.first_recharge = 1 AND users.created_at BETWEEN ? AND ?", start_time, end_time).where("users.last_login_at IS NULL OR users.last_login_at < created_at + INTERVAL 15 DAY")
    when "first_30d_unlogin"
      query = User.order(id: :desc)
      query = query.where("recharges.first_recharge = 1 AND users.created_at BETWEEN ? AND ?", start_time, end_time).where("users.last_login_at IS NULL OR users.last_login_at < created_at + INTERVAL 30 DAY")
    # when "first_curday_win"
    # when "first_curday_loss"
    # when "pay_15d_unlogin"
    else
      query = User.order(id: :desc)
    end
    @pagy, @users = pagy(query, limit: size, page: current)
  end
end
