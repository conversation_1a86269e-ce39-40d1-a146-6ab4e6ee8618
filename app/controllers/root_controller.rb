class RootController < ApplicationController
  allow_unauthenticated_access

  def index
    # 首先尝试恢复会话
    resume_session

    # 根据当前用户类型重定向到相应的后台首页
    if Current.admin
      redirect_to dashboard_path
    elsif Current.agent
      redirect_to agent_portal_root_path
    elsif Current.channel
      redirect_to channel_portal_root_path
    else
      # 未登录用户重定向到主后台登录页面
      redirect_to new_session_path
    end
  end
end
