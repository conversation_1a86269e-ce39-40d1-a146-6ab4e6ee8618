class PasswordsController < ApplicationController
  allow_unauthenticated_access only: %i[ new create edit update ]
  before_action :set_user_by_token, only: %i[ edit update ]
  before_action :require_authentication, only: %i[ change update_password ]
  layout "blank"

  # GET /passwords/new
  def new
  end

  # POST /passwords
  def create
    if user = User.find_by(email_address: params[:email_address])
      PasswordsMailer.reset(user).deliver_later
    end

    redirect_to new_session_path, notice: "Password reset instructions sent (if user with that email address exists)."
  end

  # GET /passwords/:token/edit
  def edit
  end

  # PATCH /passwords/:token
  # PUT /passwords/:token
  def update
    if @user.update(params.permit(:password, :password_confirmation))
      redirect_to new_session_path, notice: "Password has been reset."
    else
      redirect_to edit_password_path(params[:token]), alert: "Passwords did not match."
    end
  end

  # GET /passwords/change
  def change
    @user = Current.admin
  end

  # PATCH /passwords/change
  def update_password
    @user = Current.admin

    password_params = params.require(:user).permit(:current_password, :password, :password_confirmation)

    unless @user.authenticate(password_params[:current_password])
      @user.errors.add(:current_password, "当前密码不正确")
      return render :change, status: :unprocessable_entity
    end

    if password_params[:password] != password_params[:password_confirmation]
      @user.errors.add(:password_confirmation, "新密码和确认密码不匹配")
      return render :change, status: :unprocessable_entity
    end

    if @user.update(password_params.except(:current_password))
      redirect_to change_passwords_path, notice: "密码修改成功"
    else
      render :change, status: :unprocessable_entity
    end
  end

  private
    def set_user_by_token
      @user = User.find_by_password_reset_token!(params[:token])
    rescue ActiveSupport::MessageVerifier::InvalidSignature
      redirect_to new_password_path, alert: "Password reset link is invalid or has expired."
    end
end
