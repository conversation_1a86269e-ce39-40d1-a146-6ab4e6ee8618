import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "previewContainer"]

  connect() {
    console.log("File upload controller connected")
    this.maxFiles = 10
    this.acceptedTypes = ['image/png', 'image/jpeg', 'image/jpg']
    this.selectedFiles = new Set()
  }

  highlight(event) {
    event.preventDefault()
    event.stopPropagation()
    event.currentTarget.classList.add("bg-gray-100")
  }

  unhighlight(event) {
    event.preventDefault()
    event.stopPropagation()
    event.currentTarget.classList.remove("bg-gray-100")
  }

  handleDrop(event) {
    event.preventDefault()
    event.stopPropagation()
    event.currentTarget.classList.remove("bg-gray-100")

    const files = event.dataTransfer.files
    this.processFiles(files)
  }

  handleFiles(event) {
    const files = event.target.files
    this.processFiles(files)
  }

  processFiles(files) {
    const validFiles = Array.from(files).filter(file => 
      this.acceptedTypes.includes(file.type)
    )

    if (validFiles.length === 0) {
      alert('请上传有效的图片文件（PNG、JPG、JPEG）')
      return
    }

    // 检查总文件数
    if (this.selectedFiles.size + validFiles.length > this.maxFiles) {
      alert(`最多只能上传 ${this.maxFiles} 张图片`)
      return
    }

    // 添加新文件到集合
    validFiles.forEach(file => {
      this.selectedFiles.add(file)
    })

    // 显示上传进度
    validFiles.forEach((file, index) => {
      this.showUploadProgress(file, index)
    })

    // 更新文件输入
    this.updateFileInput()
  }

  updateFileInput() {
    const dataTransfer = new DataTransfer()
    this.selectedFiles.forEach(file => dataTransfer.items.add(file))
    this.inputTarget.files = dataTransfer.files
  }

  clearInput() {
    // 清空文件输入和集合
    this.selectedFiles.clear()
    this.inputTarget.value = ''
    // 移除所有带有"新图片"标记的预览
    const newPreviews = this.previewContainerTarget.querySelectorAll('.bg-yellow-500')
    newPreviews.forEach(preview => {
      preview.closest('.relative').remove()
    })
  }

  showUploadProgress(file, index) {
    const reader = new FileReader()
    const previewId = `preview-${Date.now()}-${index}`

    // 创建预览容器
    const previewContainer = document.createElement('div')
    previewContainer.id = previewId
    previewContainer.className = 'relative group'
    previewContainer.dataset.fileName = file.name
    
    // 创建进度条
    const progressBar = document.createElement('div')
    progressBar.className = 'absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center'
    progressBar.innerHTML = `
      <div class="w-3/4 bg-gray-200 rounded-full h-2.5">
        <div class="bg-indigo-600 h-2.5 rounded-full upload-progress" style="width: 0%"></div>
      </div>
    `

    // 创建新图片标记
    const newIndicator = document.createElement('div')
    newIndicator.className = 'absolute top-1 left-1 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full'
    newIndicator.textContent = '新图片'

    previewContainer.appendChild(progressBar)
    previewContainer.appendChild(newIndicator)
    this.previewContainerTarget.appendChild(previewContainer)

    reader.onload = (e) => {
      // 创建图片预览
      const img = document.createElement('img')
      img.src = e.target.result
      img.className = 'rounded-lg shadow-md w-full h-48 object-cover cursor-pointer'
      img.onclick = (e) => this.showLightbox(e)
      previewContainer.insertBefore(img, progressBar)

      // 模拟上传进度
      let progress = 0
      const interval = setInterval(() => {
        progress += 10
        const progressElement = previewContainer.querySelector('.upload-progress')
        if (progressElement) {
          progressElement.style.width = `${progress}%`
        }
        
        if (progress >= 100) {
          clearInterval(interval)
          setTimeout(() => {
            progressBar.remove()
          }, 500)
        }
      }, 100)
    }

    reader.readAsDataURL(file)
  }

  showLightbox(event) {
    event.stopPropagation()
    const img = event.target
    const src = img.src

    // 创建 lightbox 容器
    const lightbox = document.createElement('div')
    lightbox.className = 'fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center'
    lightbox.onclick = () => this.hideLightbox()

    // 创建图片容器
    const imgContainer = document.createElement('div')
    imgContainer.className = 'relative max-w-4xl max-h-[90vh]'
    imgContainer.onclick = (e) => e.stopPropagation()

    // 创建大图
    const largeImg = document.createElement('img')
    largeImg.src = src
    largeImg.className = 'max-w-full max-h-[90vh] object-contain'

    // 创建关闭按钮
    const closeButton = document.createElement('button')
    closeButton.className = 'absolute -top-4 -right-4 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100'
    closeButton.innerHTML = `
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    `
    closeButton.onclick = () => this.hideLightbox()

    imgContainer.appendChild(largeImg)
    imgContainer.appendChild(closeButton)
    lightbox.appendChild(imgContainer)

    // 添加到 body
    document.body.appendChild(lightbox)
    // 禁止滚动
    document.body.style.overflow = 'hidden'
  }

  hideLightbox() {
    const lightbox = document.querySelector('.fixed.inset-0.bg-black')
    if (lightbox) {
      lightbox.remove()
      // 恢复滚动
      document.body.style.overflow = ''
    }
  }
} 