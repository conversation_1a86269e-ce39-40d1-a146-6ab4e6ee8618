import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["provider", "jsonConfig", "formConfig", "configField"]

  connect() {
    this.toggleConfigFields();
  }

  toggleConfigFields() {
    const provider = this.providerTarget.value;
    const formConfig = this.formConfigTarget;
    const jsonConfig = this.jsonConfigTarget;
    
    // 隐藏所有字段
    this.configFieldTargets.forEach(field => {
      field.classList.add("hidden");
    });
    
    // 显示当前选择的支付方式的字段
    this.configFieldTargets.forEach(field => {
      if (field.dataset.provider === provider) {
        field.classList.remove("hidden");
      }
    });

    // 根据provider值判断是否显示JSON文本输入
    if (provider === "") {
      formConfig.classList.add("hidden");
      jsonConfig.classList.remove("hidden");
    } else {
      formConfig.classList.remove("hidden");
      jsonConfig.classList.add("hidden");
    }
  }

  providerChanged() {
    this.toggleConfigFields();
  }
} 