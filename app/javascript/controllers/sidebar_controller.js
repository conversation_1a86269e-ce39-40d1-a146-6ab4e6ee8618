import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["toggle", "sidebar"]
  static values = {
    contentSelector: String
  }

  connect() {
    console.log("Sidebar controller connected")
    // 从 localStorage 恢复状态，如果没有保存的状态则默认折叠
    const isCollapsed = localStorage.getItem('sidebarCollapsed') !== 'false'
    if (isCollapsed) {
      this.collapse()
    }
  }

  toggle() {
    const isCollapsed = this.sidebarTarget.classList.contains('collapsed')
    if (isCollapsed) {
      this.expand()
    } else {
      this.collapse()
    }
    // 保存状态到 localStorage
    localStorage.setItem('sidebarCollapsed', !isCollapsed)
  }

  collapse() {
    this.sidebarTarget.classList.add('collapsed')
    const content = document.querySelector(this.contentSelectorValue)
    if (content) {
      content.classList.remove('ml-64')
      content.classList.add('ml-16')
    }
  }

  expand() {
    this.sidebarTarget.classList.remove('collapsed')
    const content = document.querySelector(this.contentSelectorValue)
    if (content) {
      content.classList.add('ml-64')
      content.classList.remove('ml-16')
    }
  }
} 