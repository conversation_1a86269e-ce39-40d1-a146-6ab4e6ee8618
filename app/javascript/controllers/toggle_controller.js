// app/javascript/controllers/toggle_controller.js
import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="toggle"
export default class extends Controller {
  // 定义需要被切换显示/隐藏的目标元素
  // 在 HTML 中使用 data-toggle-target="element" 来标记这个元素
  static targets = [ "element" ]

  // 可选：定义要切换的 CSS 类名，默认为 'hidden'
  // 可以在 HTML 中通过 data-toggle-class-value="your-class" 覆盖
  static values = { class: { type: String, default: "hidden" } }

  connect() {
    // 可选：确保初始 aria-expanded 状态与目标元素的可见性一致
    // 这要求触发器按钮（带有 data-action 的那个）有 aria-controls 属性
    // 并且目标元素（elementTarget）有对应的 id
    const trigger = this.findTrigger();
    if (trigger && trigger.hasAttribute('aria-controls')) {
      const isHidden = this.elementTarget.classList.contains(this.classValue);
      trigger.setAttribute('aria-expanded', !isHidden);
    }
  }

  // 切换目标元素的 CSS 类 (默认为 'hidden')
  toggle(event) {
    this.elementTarget.classList.toggle(this.classValue);

    // 更新触发器按钮的 aria-expanded 属性 (如果存在)
    // event.currentTarget 是触发此操作的元素 (通常是按钮)
    const trigger = event.currentTarget;
    if (trigger && trigger.hasAttribute('aria-controls')) {
      const isExpanded = !this.elementTarget.classList.contains(this.classValue);
      trigger.setAttribute('aria-expanded', isExpanded);
    }

    // 可选：如果需要切换触发器自身的图标（例如 汉堡菜单 <-> 关闭图标）
    // 可以添加额外的 target 或逻辑来处理
    // this.toggleIcon(trigger, isExpanded);
  }

  // 辅助方法：尝试找到此控制器的触发器元素
  // (基于 data-action 指向本控制器的 toggle 方法)
  // 注意：一个控制器实例可能被多个元素触发，这里简化处理，
  // 查找第一个匹配的触发器，主要用于 connect 时设置初始状态。
  // 在 toggle 方法中，使用 event.currentTarget 更准确。
  findTrigger() {
    const controllerName = this.identifier; // e.g., "toggle"
    return this.scope.findElement(`[data-action*='->${controllerName}#toggle']`);
  }

  // 可选：切换图标的示例逻辑
  // toggleIcon(trigger, isExpanded) {
  //   const openIcon = trigger.querySelector('.open-icon'); // 需要给图标添加 class
  //   const closeIcon = trigger.querySelector('.close-icon'); // 需要给图标添加 class
  //   if (openIcon && closeIcon) {
  //     openIcon.classList.toggle('hidden', isExpanded);
  //     closeIcon.classList.toggle('hidden', !isExpanded);
  //   }
  // }
}