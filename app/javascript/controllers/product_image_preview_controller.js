import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["modal", "image"]

  connect() {
    this.modalTarget.classList.add("hidden")
    
    // ESC 键关闭
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape") this.hide()
    })

    // 添加全局事件监听器
    document.addEventListener("click", (e) => {
      const imageContainer = e.target.closest("[data-product-preview-image]")
      if (imageContainer) {
        e.preventDefault()
        this.show({
          currentTarget: imageContainer
        })
      }
    })
  }

  show(event) {
    const imageUrl = event.currentTarget.dataset.imageUrl
    this.imageTarget.src = imageUrl
    this.modalTarget.classList.remove("hidden")
    document.body.style.overflow = "hidden"
  }

  hide() {
    this.modalTarget.classList.add("hidden")
    document.body.style.overflow = ""
  }

  // 阻止事件冒泡，防止点击内容区域时关闭模态框
  stopPropagation(event) {
    event.stopPropagation()
  }
} 