import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["source", "trigger"]
  static values = {
    successDuration: { type: Number, default: 2000 },
    successText: { type: String, default: "已复制" }
  }


  async copy(event) {
    event.preventDefault()
    if (!this.hasSourceTarget) {
      this.sourceTarget = this.element
    }
    const text = this.sourceTarget.textContent.trim()
    try {
      // 尝试现代 Clipboard API
      await navigator.clipboard.writeText(text)
      this.showSuccess()
    } catch (err) {
      if (this.copyViaExecCommand(text)) {
        this.showSuccess()
      } else {
        this.showSuccess("❌ 复制失败，请手动选择文本复制")
      }
    }
  }

  copyViaExecCommand(text) {
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'  // 避免滚动跳转
    document.body.appendChild(textarea)
    textarea.select()
    
    try {
      return document.execCommand('copy')
    } catch (err) {
      console.error("Failed to copy text with execCommand: ", err)
      return false
    } finally {
      document.body.removeChild(textarea)
    }
  }

  showSuccess(message = null) {
    const button = this.triggerTarget
    const originalText = button.innerHTML
    button.innerHTML = `
      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
      ${message || this.successTextValue}
    `
    setTimeout(() => {
      button.innerHTML = originalText
    }, this.successDurationValue)
  }
} 