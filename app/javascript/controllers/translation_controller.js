import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "result", "button"]

  connect() {
    this.resultTarget.classList.add("hidden")
  }

  async translate() {
    const text = this.inputTarget.value

    if (!text) {
      alert("请输入需要翻译的文本")
      return
    }

    this.buttonTarget.disabled = true
    this.buttonTarget.textContent = "翻译中..."
    this.resultTarget.classList.add("hidden")

    try {
      const response = await fetch("/api/tools/transform", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer sk-or-v1"
        },
        body: JSON.stringify({
          text: text,
        })
      })

      const data = await response.json()
      
      if (data.error) {
        throw new Error(data.error)
      }

      if (this.resultTarget.tagName.toLowerCase() === "textarea" || this.resultTarget.tagName.toLowerCase() === "input") {
        this.resultTarget.value = data.translated_text
      } else {
        this.resultTarget.textContent = data.translated_text
      }
      
      this.resultTarget.classList.remove("hidden")
    } catch (error) {
      alert("翻译失败：" + error.message)
    } finally {
      this.buttonTarget.disabled = false
      this.buttonTarget.textContent = "翻译"
    }
  }
} 