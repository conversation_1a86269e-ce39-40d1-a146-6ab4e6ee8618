import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="removals"
export default class extends Controller {
  static values = { delay: { type: Number, default: 5000 } }

  connect() {
    this.timeout = setTimeout(() => {
      this.remove()
    }, this.delayValue) // 使用 data-removals-delay-value 或默认值

  }

  disconnect() {
    clearTimeout(this.timeout)
  }

  // 移除元素的方法
  remove() {
    // 添加一个 CSS 类来触发淡出动画
    this.element.classList.add('fade-out');

    // 监听过渡效果结束事件，在动画完成后再移除元素
    // { once: true } 确保事件监听器只执行一次
    this.element.addEventListener('transitionend', () => {
      this.element.remove();
      // console.log("Element removed after transition.");
    }, { once: true });

    // 作为备用方案，如果 transitionend 事件由于某种原因没有触发
    // (例如，元素没有 transition 属性或 display: none)，
    // 在动画持续时间后强制移除。时间应略长于 CSS 过渡时间。
    setTimeout(() => {
      if (this.element) { // 检查元素是否还存在
          this.element.remove();
          // console.log("Element force removed via fallback timeout.");
      }
    }, 600); // 稍长于 0.5s 的 CSS transition
  }
}