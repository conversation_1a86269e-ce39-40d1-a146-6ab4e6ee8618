import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["slide", "indicator"]

  connect() {
    this.currentIndex = 0
    this.totalSlides = this.slideTargets.length
    this.updateThumbnails()
    this.updateIndicators()
    
    // 如果有多张图片，启动自动轮播
    if (this.totalSlides > 1) {
      this.startAutoSlide()
    }
  }

  disconnect() {
    // 在控制器断开连接时清除定时器
    this.stopAutoSlide()
  }

  startAutoSlide() {
    // 设置10秒自动轮播
    this.autoSlideInterval = setInterval(() => {
      this.next()
    }, 10000)
  }

  stopAutoSlide() {
    // 清除定时器
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval)
    }
  }

  prev() {
    // 用户手动切换时重置自动轮播计时器
    this.resetAutoSlideTimer()
    this.currentIndex = (this.currentIndex - 1 + this.totalSlides) % this.totalSlides
    this.updateSlides()
  }

  next() {
    // 用户手动切换时重置自动轮播计时器
    this.resetAutoSlideTimer()
    this.currentIndex = (this.currentIndex + 1) % this.totalSlides
    this.updateSlides()
  }

  goTo(event) {
    // 用户手动切换时重置自动轮播计时器
    this.resetAutoSlideTimer()
    this.currentIndex = parseInt(event.currentTarget.dataset.index)
    this.updateSlides()
  }

  resetAutoSlideTimer() {
    // 在用户手动切换后重置自动轮播计时器
    if (this.totalSlides > 1) {
      this.stopAutoSlide()
      this.startAutoSlide()
    }
  }

  updateSlides() {
    this.slideTargets.forEach((slide, index) => {
      slide.style.opacity = index === this.currentIndex ? 1 : 0
    })
    this.updateThumbnails()
    this.updateIndicators()
  }

  updateThumbnails() {
    document.querySelectorAll('[data-index]').forEach(thumbnail => {
      const index = parseInt(thumbnail.dataset.index)
      
      // 移除所有缩略图上的高亮样式
      thumbnail.classList.remove('border-emerald-500', 'border-emerald-600', 'border-2', 'ring', 'ring-emerald-400', 'ring-opacity-50', 'scale-105')
      
      // 为当前选中的缩略图添加高亮效果
      if (index === this.currentIndex) {
        thumbnail.classList.add('border-emerald-600', 'border-2', 'ring', 'ring-emerald-400', 'ring-opacity-50', 'scale-105')
        // 确保选中的缩略图滚动到视图中
        thumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' })
      } else {
        // 其他缩略图保持默认边框
        thumbnail.classList.add('border-transparent')
      }
    })
  }

  updateIndicators() {
    if (this.hasIndicatorTarget) {
      this.indicatorTargets.forEach((indicator, index) => {
        // 更新指示器的宽度和颜色
        indicator.style.width = index === this.currentIndex ? '24px' : '8px'
        indicator.style.backgroundColor = index === this.currentIndex ? '#059669' : 'rgba(255,255,255,0.7)'
      })
    }
  }

  preloadNext() {
    const nextIndex = (this.currentIndex + 1) % this.totalSlides
    const nextImage = new Image()
    nextImage.src = this.slideTargets[nextIndex].src
  }
}