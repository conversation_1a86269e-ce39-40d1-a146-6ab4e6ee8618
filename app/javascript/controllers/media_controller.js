import { Controller } from "@hotwired/stimulus"
import { destroy } from "@rails/request.js"

export default class extends Controller {
  connect() {
    console.log("Media controller connected")
  }

  toggleAll(event) {
    const checked = event.target.checked
    const checkboxes = document.querySelectorAll('.media-checkbox')
    
    checkboxes.forEach(checkbox => {
      checkbox.checked = checked
    })

    this.toggleDeleteButton()
  }

  toggleDeleteButton() {
    const checkboxes = document.querySelectorAll('.media-checkbox:checked')
    const batchDeleteBtn = document.getElementById('batch-delete-btn')
    
    if (checkboxes.length > 0) {
      batchDeleteBtn.classList.remove('hidden')
    } else {
      batchDeleteBtn.classList.add('hidden')
    }
  }

  async confirmBatchDelete(event) {
    event.preventDefault()
    
    const checkedCount = document.querySelectorAll('.media-checkbox:checked').length
    
    if (checkedCount === 0) {
      alert('请先选择要删除的文件')
      return
    }
    
    if (confirm(`确定要删除选中的 ${checkedCount} 个文件吗？此操作不可恢复。`)) {
      try {
        // 收集选中的ID
        const checkboxes = document.querySelectorAll('.media-checkbox:checked')
        const selectedIds = Array.from(checkboxes).map(checkbox => checkbox.value)
        
        // 使用FormData
        const formData = new FormData()
        selectedIds.forEach(id => {
          formData.append('medium_ids[]', id)
        })
        
        // 使用request.js发送批量删除请求
        const response = await destroy('/admin/media/batch_destroy', {
          body: formData
        })
        
        if (response.ok) {
          // 删除成功，刷新页面
          window.location.reload()
        } else {
          alert(`批量删除失败: ${response.statusCode}`)
        }
      } catch (error) {
        console.error("批量删除出错:", error)
        alert("批量删除过程中发生错误")
      }
    }
  }
  
  async deleteSingle(event) {
    event.preventDefault()
    
    const id = event.currentTarget.dataset.id
    
    if (confirm('确定删除此文件吗？此操作不可恢复。')) {
      try {
        const response = await destroy(`/admin/media/${id}`)
        window.location.reload()
      } catch (error) {
        console.error("删除出错:", error)
        alert("删除过程中发生错误")
      }
    }
  }
} 