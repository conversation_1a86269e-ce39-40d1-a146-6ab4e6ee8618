import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["content"]
  static values = {
    closeOnBackgroundClick: { type: Boolean, default: true }
  }

  connect() {
    // 添加键盘事件监听，按 ESC 键关闭模态框
    document.addEventListener("keydown", this.handleKeydown.bind(this))
  }

  disconnect() {
    document.removeEventListener("keydown", this.handleKeydown.bind(this))
  }

  open() {
    this.element.classList.remove("hidden")
    document.body.classList.add("overflow-hidden")
  }

  close(event) {
    if (this.closeOnBackgroundClickValue && (event.target === this.element || event.target === this.element.firstElementChild)) {
      event.preventDefault()
      this.element.remove()
    }
  }

  // 点击模态框背景关闭
  closeBackground(event) {
    if (event.target === this.element) {
      this.close()
    }
  }

  // 处理 ESC 键关闭
  handleKeydown(event) {
    if (event.key === "Escape") {
      event.preventDefault()
      this.element.remove()
    }
  }
} 