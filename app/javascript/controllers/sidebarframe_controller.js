import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  // 当 Turbo Frame 加载完成后触发侧边栏显示
  connect() {
    console.log("Sidebar connected")
    // 初始状态设为隐藏
    this.element.classList.add("initializing")
    // 使用requestAnimationFrame确保DOM先渲染一次
    requestAnimationFrame(() => {
      // 然后移除初始化类，触发显示动画
      setTimeout(() => {
        this.element.classList.remove("initializing")
      }, 50)
    })
  }

  // 关闭侧边栏
  close() {
    console.log("close sidebarframe")
    
    // 添加removing类触发关闭动画
    this.element.classList.add("removing")
    
    // 等待动画完成后再移除元素
    setTimeout(() => {
      this.element.remove()
    }, 250) // 与CSS过渡时间匹配
  }
}