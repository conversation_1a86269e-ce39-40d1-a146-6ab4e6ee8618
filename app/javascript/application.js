// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails"
import "@rails/request.js"
import Alpine from 'alpinejs'
import "controllers"
import "channels"


window.Alpine = Alpine 
Alpine.start()

import { Turbo } from "@hotwired/turbo-rails"
const { StreamActions } = Turbo

StreamActions.scroll_to_bottom = function () {
    const target = this.getAttribute("data-target")
    const element = document.getElementById(target)
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
    }
}
