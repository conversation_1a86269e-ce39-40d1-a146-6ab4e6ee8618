class Admin < ApplicationRecord
  include TotpAble
  include JwtAble
  include LoginAble

  has_many :admin_roles, dependent: :destroy
  has_many :roles, through: :admin_roles

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  # validates :email_address, presence: true, uniqueness: true

  enum :department, { game: "game", finance: "finance", operation: "operation", hr: "hr", marketing: "marketing",
    tech: "tech", legal: "legal", admin: "admin" }

  enum :status, { actived: 0, locked: 1, deleted: 2 }, default: :actived
  def status_text
    I18n.t("common.status.#{status}")
  end

  # Default scope to exclude deleted records
  default_scope { where(deleted_at: nil) }

  # Scope to include deleted records
  scope :with_deleted, -> { unscope(where: :deleted_at) }

  def soft_delete
    update(status: :deleted, deleted_at: Time.current)
  end

  def restore
    update(status: :active, deleted_at: nil)
  end

  def deleted?
    deleted_at.present?
  end

  def has_role?(role_code)
    roles.exists?(code: role_code)
  end

  def add_role(role)
    roles << role unless has_role?(role.code)
  end

  def remove_role(role)
    roles.delete(role)
  end

  def role_names
    roles.pluck(:name)
  end

  def roles_codes
    roles.pluck(:code)
  end

  def accessible_menus
    roles.flat_map(&:menus).uniq
  end

  def accessible_buttons
    roles.flat_map(&:buttons).uniq
  end

  def can_operate_button?(button_identifier)
    button = Button.find_by(identifier: button_identifier)
    return false unless button
    accessible_buttons.include?(button)
  end
end
