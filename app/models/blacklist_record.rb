class BlacklistRecord < ApplicationRecord
  belongs_to :user

  validates :user_id, presence: true
  validates :ban_type, presence: true
  validates :reason, presence: true
  validates :actived, inclusion: { in: [ true, false ] }

  # 如果设置了expires_at，则必须是未来的时间
  validate :expires_at_must_be_in_future, if: -> { expires_at.present? }

  enum :ban_type, { personal: 0, influencer: 1, team: 2 }

  private

  def expires_at_must_be_in_future
    if expires_at <= Time.current
      errors.add(:expires_at, "必须是未来的时间")
    end
  end
end
