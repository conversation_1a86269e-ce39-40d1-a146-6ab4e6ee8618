class PendingMessageProcessor
  STREAM_KEY = RedisStreamProcessor::STREAM_KEY
  GROUP_NAME = RedisStreamProcessor::GROUP_NAME
  PENDING_TIMEOUT = 60 # 1分钟超时

  def self.process_pending_messages(stream_key: STREAM_KEY, group_name: GROUP_NAME)
    Rails.cache.redis.with do |redis_client|
      puts "Redis 连接信息:"
      puts "Redis 客户端: #{redis_client.inspect}"

      # 获取所有pending的消息
      puts "执行命令: XPENDING #{stream_key} #{group_name} - + 100"
      begin
        pending_messages = redis_client.xpending(
          stream_key,
          group_name,
          "-", # 最小ID
          "+", # 最大ID
          100  # 返回数量
        )
      rescue Redis::CommandError => e
        if e.message.include?("NOGROUP") || e.message.include?("No such key")
          puts "Stream 或 Consumer Group 不存在: #{e.message}"
          return true
        else
          raise e
        end
      end

      if pending_messages.empty?
        puts "没有待处理消息"
        return
      end

      puts "发现 #{pending_messages.size} 条待处理消息"

      pending_messages.each do |message|
        puts "message: #{message.inspect}"
        entry_id = message["entry_id"]
        consumer = message["consumer"]
        idle_time = message["elapsed"]
        delivery_count = message["count"]
        # puts "超时时间: #{idle_time / 1000}s"

        # 如果消息空闲时间超过1分钟
        if idle_time > PENDING_TIMEOUT * 1000 # Redis返回的是毫秒
          puts "处理超时消息: Entry ID: #{entry_id}, 空闲时间: #{idle_time / 1000.0}s"

          # 重新认领消息
          begin
            # 获取消息详情
            message_details = redis_client.xrange(stream_key, entry_id, entry_id).first
            if message_details
              puts "message_details: #{message_details.inspect}"
              # 重新处理消息
              process_message(entry_id, message_details[1])
              # 确认消息
              redis_client.xack(stream_key, group_name, entry_id)
              puts "消息 #{entry_id} 已重新处理并确认"
            end
          rescue => e
            puts "处理消息 #{entry_id} 时出错: #{e.message}"
          end
        else
          puts "消息 #{entry_id} 未超时"
        end
      end
      true
    end
  end

  private

  def self.process_message(entry_id, fields)
    # 这里添加具体的消息处理逻辑
    puts "处理消息: Entry ID: #{entry_id}, Fields: #{fields.inspect}"
    sleep 3 # 模拟处理时间
  end
end
