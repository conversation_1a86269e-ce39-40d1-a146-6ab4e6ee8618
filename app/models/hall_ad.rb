class HallAd < Ad
  # 广告位置 enum
  AD_POSITIONS = %w[ hall_slider hall_big_kinggang hall_mini_kinggang hall_top_nav
    sidebar_big_kinggang sidebar_mini_kinggang hall_bottom ].index_by(&:itself)
  enum :ad_position, AD_POSITIONS, default: :hall_slider
  def ad_position_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.ad_positions.#{self.ad_position}")
  end

  # 跳转类型 enum
  REDIRECT_TYPES = %w[ no_redirect internal external popup ].index_by(&:itself)
  enum :redirect_type, REDIRECT_TYPES, default: :no_redirect
  def redirect_type_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.redirect_types.#{self.redirect_type}")
  end
end
