class GameType < ApplicationRecord
  include Cacheable
  has_many :games, dependent: :restrict_with_exception

  # 验证
  validates :name, presence: true, uniqueness: true
  validates :order, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :status, presence: true

  # 状态枚举
  enum :status, { offlined: 0, onlined: 1 }, default: :onlined
  def status_text
    I18n.t("common.status.#{status}")
  end

  # 默认排序
  default_scope { order(order: :desc, id: :asc) }

  def self.cached_all
    Rails.cache.fetch(self.cache_key, raw: true) do
      onlined.to_json
    end
  end
end
