class Payment < ApplicationRecord
  include Cacheable

  belongs_to :payment_type

  validates :name, presence: true
  validates :payment_way, presence: true
  validates :payment_type_id, presence: true

  validates :recharge_method, presence: true, inclusion: { in: -> { payment_solutions.keys } }
  validates :recharge_fee_rate, presence: true
  validates :recharge_enabled, inclusion: { in: [ true, false, 0, 1 ] }
  validates :recharge_weight, presence: true

  validates :withdraw_fee_rate, presence: true
  validates :min_recharge, presence: true
  validates :max_recharge, presence: true
  validates :min_withdraw, presence: true
  validates :max_withdraw, presence: true
  validates :withdraw_weight, presence: true
  validates :withdraw_enabled, inclusion: { in: [ true, false, 0, 1 ] }

  serialize :extras, coder: JSON, type: Hash

  enum :payment_way, %w[qrcode webpage external].index_by(&:itself), default: :qrcode

  def self.payment_solutions
    Rails.cache.fetch("payment_solutions") do
      JSON.parse(Rails.root.join("config", "pay.json").read)
    end
  end

  def self.cached_all
    Rails.cache.fetch(self.cache_key, raw: true) do
      unscoped.all.to_json
    end
  end
end
