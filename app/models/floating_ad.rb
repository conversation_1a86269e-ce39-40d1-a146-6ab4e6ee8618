class FloatingAd < Ad
  # 跳转类型 enum
  REDIRECT_TYPES = %w[ no_redirect internal external popup ].index_by(&:itself)
  enum :redirect_type, REDIRECT_TYPES, default: :no_redirect
  def redirect_type_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.redirect_types.#{self.redirect_type}")
  end

  # 展示位置
  AD_POSITIONS = %w[ left right ].index_by(&:itself)
  enum :ad_position, AD_POSITIONS, default: :bottom_right
  def ad_position_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.ad_positions.#{self.ad_position}")
  end
end
