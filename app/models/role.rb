class Role < ApplicationRecord
  has_many :admin_roles, dependent: :destroy
  has_many :admins, through: :admin_roles

  has_many :role_menus, dependent: :destroy
  has_many :menus, through: :role_menus

  has_many :role_buttons, dependent: :destroy
  has_many :buttons, through: :role_buttons

  validates :name, presence: true, uniqueness: true
  validates :code, presence: true, uniqueness: true

  enum :status, [ :actived, :inactived ], default: :actived
  def status_text
    I18n.t("common.status.#{status}")
  end

  def add_menu(menu)
    menus << menu unless menus.include?(menu)
  end

  def remove_menu(menu)
    menus.delete(menu)
  end

  def add_button(button)
    buttons << button unless buttons.include?(button)
  end

  def remove_button(button)
    buttons.delete(button)
  end

  def has_menu?(menu)
    menus.include?(menu)
  end

  def has_button?(button)
    buttons.include?(button)
  end

  def menu_ids
    menus.pluck(:id)
  end

  def button_ids
    buttons.pluck(:id)
  end
end
