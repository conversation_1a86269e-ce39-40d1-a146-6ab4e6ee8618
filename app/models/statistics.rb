class Statistics
  # 总用户数
  def self.total_users
    Rails.cache.fetch("statistics_total_users", expires_in: 1.hour) do
      User.actived.count
    end
  end

  # 今日新增用户数
  def self.today_new_users
    Rails.cache.fetch("statistics_today_new_users", expires_in: 1.hour) do
      User.actived.where(created_at: Time.current.beginning_of_day..Time.current.end_of_day).count
    end
  end

  # 总充值用户数
  def self.total_recharge_users
    Rails.cache.fetch("statistics_total_recharge_users", expires_in: 1.hour) do
      User.actived.joins(:user_order_summary).where.not(user_order_summaries: { total_recharge_count: 0 }).count
    end
  end

  # 付费率, format 0.00%
  def self.recharge_rate
    Rails.cache.fetch("statistics_recharge_rate", expires_in: 1.hour) do
      (total_recharge_users / total_users.to_f * 100).round(2).to_s + "%"
    end
  end

  # 总余额
  def self.total_balance
    Rails.cache.fetch("statistics_total_balance", expires_in: 1.hour) do
      # sum from user relation wallet
      User.actived.joins(:wallet).sum(:balance)
    end
  end

  # 总代理数
  def self.total_agents
    Rails.cache.fetch("statistics_total_agents", expires_in: 1.hour) do
      Agent.actived.count
    end
  end

  # 总渠道数
  def self.total_channels
    Rails.cache.fetch("statistics_total_channels", expires_in: 1.hour) do
      Channel.actived.count
    end
  end

  # 今日新增渠道数
  def self.today_new_channels
    Rails.cache.fetch("statistics_today_new_channels", expires_in: 1.hour) do
      Channel.actived.where(created_at: Time.current.beginning_of_day..Time.current.end_of_day).count
    end
  end

  # 总游戏数
  def self.total_games
    Rails.cache.fetch("statistics_total_games", expires_in: 1.hour) do
      Game.onlined.count
    end
  end
end
