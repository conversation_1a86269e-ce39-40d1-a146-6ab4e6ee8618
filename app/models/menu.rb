class Menu < ApplicationRecord
  belongs_to :parent, class_name: "<PERSON><PERSON>", optional: true
  has_many :children, class_name: "<PERSON><PERSON>", foreign_key: "parent_id", dependent: :destroy

  has_many :role_menus, dependent: :destroy
  has_many :roles, through: :role_menus

  validates :name, presence: true, uniqueness: true
  validates :path, presence: true
  validates :route_name, presence: true
  validates :menu_type, presence: true, inclusion: { in: %w[menu directory] }

  enum :status, [ :active, :inactive ], default: :active

  # 虚拟属性
  attr_accessor :no_cache

  # 初始化时设置默认值
  after_initialize :set_defaults

  def component
    if menu_type == "directory"
      "layout.#{layout}"
    else
      if layout.present?
        "layout.#{layout}$#{read_attribute(:component)}"
      else
        read_attribute(:component)
      end
    end
  end

  # 用于构建路由数据的方法
  def to_route
    {
      name: name.downcase,
      path: path,
      component: component,
      meta: {
        title: title,
        icon: icon,
        order: order
      }
    }
  end

  # 用于调试的方法
  def inspect
    "#<Menu id: #{id}, name: #{name}, path: #{path}, component: #{component}, title: #{title}, icon: #{icon}, order: #{order}>"
  end

  private

  def set_defaults
    self.no_cache ||= false
    self.menu_type ||= "menu"
    self.hidden ||= false
  end
end
