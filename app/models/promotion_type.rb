class PromotionType < ApplicationRecord
  include Cacheable
  has_many :channels, dependent: :restrict_with_exception

  # 验证
  validates :name, presence: true, uniqueness: true
  validates :code, presence: true, uniqueness: true
  validates :sort, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :status, presence: true

  # 状态枚举
  enum :status, { disabled: 0, enabled: 1 }, default: :enabled
  def status_text
    I18n.t("common.status.#{status}")
  end

  # 默认排序
  default_scope { order(sort: :desc, id: :asc) }

  def self.cached_all
    Rails.cache.fetch(self.cache_key, raw: true) do
      enabled.to_json
    end
  end
end
