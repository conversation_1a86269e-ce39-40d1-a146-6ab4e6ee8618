module JwtAble
  extend ActiveSupport::Concern

  class_methods do
    def decode_token(token)
      token = token.to_s.strip
      return nil if token.blank?
      begin
        decoded_token = JWT.decode(token, Rails.application.credentials.secret_key_base, true, { algorithm: "HS256" })
        if decoded_token.is_a?(Array)
          decoded_token.first
        else
          puts "Invalid token received: #{token}"
          nil
        end
      rescue JWT::DecodeError => e
        puts "JWT::DecodeError: #{e.message}"
        nil
      end
    end
  end

  def token
    JWT.encode(
      {
        id: id,
        name: name,
        department: department,
        roles: roles_codes,
        exp: 24.hours.from_now.to_i
      },
      Rails.application.credentials.secret_key_base,
      "HS256"
    )
  end

  def refresh_token
    JWT.encode(
      {
        id: id,
        exp: 7.days.from_now.to_i
      },
      Rails.application.credentials.secret_key_base,
      "HS256"
    )
  end
end
