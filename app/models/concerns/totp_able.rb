module TotpAble
  extend ActiveSupport::Concern

  included do
    before_validation :generate_totp_secret, on: :create
  end

  # 生成 TOTP 密钥
  def generate_totp_secret
    self.totp_secret ||= ROTP::Base32.random_base32
  end

  # 获取 TOTP 实例
  def totp
    ROTP::TOTP.new(totp_secret, issuer: ENV.fetch("TOTP_ISSUER", "catalyst-admin-backend"))
  end

  # 验证 TOTP 代码
  def verify_totp(code)
    totp.verify(code, drift_behind: 30, drift_ahead: 30) # 允许 30 秒的时钟漂移
  end
end
