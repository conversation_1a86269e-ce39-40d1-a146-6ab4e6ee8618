module LoginAble
  extend ActiveSupport::Concern
  # 此模块主要是用于为 admin / agent / channel 提供登录相关的功能
  # 注意：引用此模块，必须有的表字段：name / nickname / password_digest
  # 包括密码格式的验证 / 重置密码 等功能

  included do
    has_secure_password
    validates :name, presence: true, uniqueness: true, format: { with: ALLOW_LOGIN_FORMAT_REGEXP, allow_blank: true }, length: { in: 2..24, allow_blank: true }
    validates :nickname, length: { in: 2..24, allow_blank: true }
    validates :password, presence: true, length: { minimum: 8, allow_blank: true }, if: -> { new_record? || password.present? }

    has_many :sessions, dependent: :destroy
  end

  # 重置密码
  def reset_password(password)
    if password.present?
      self.password = password
      save!
    end
  end
end
