# 说明：
# 这个 Concern 提供了缓存和通知功能，主要用于需要缓存的 Model
#
# 使用方法:
# 1. 在需要缓存的 Model 中引入此 Concern:
#    include Cacheable
#
# 2. 在 Model 中实现 cached_all 类方法，返回需要缓存的 JSON 字符串:
#    def self.cached_all
#      Rails.cache.fetch(cache_key, raw: true) do
#        all.to_json
#      end
#    end
#
# 功能:
# - 当 Model 发生 create/update/destroy 操作时，会自动:
#   1. 清除该 Model 的缓存
#   2. 重新生成缓存
#   3. 通过 Redis pub/sub 发布更新通知
#
# 缓存 key 格式:
# - "<model_name>:all" (例如: "user:all")
#
# Redis 订阅 channel:
# - 与缓存 key 相同
module Cacheable
  extend ActiveSupport::Concern

  included do
    after_commit :update_cache_and_notify, on: [ :create, :update, :destroy ]
    class_attribute :use_base_class_cache, default: true
  end

  class_methods do
    def cache_key
      if use_base_class_cache
        base_class = self.base_class
        "#{base_class.name.underscore}:all"
      else
        "#{name.underscore}:all"
      end
    end

    def use_base_class_cache!
      self.use_base_class_cache = true
    end
  end

  private

  def update_cache_and_notify
    Rails.cache.delete(self.class.cache_key)
    json_string = self.class.cached_all # 重新生成缓存
    Rails.cache.redis.with do |redis_client|
      redis_client.publish(self.class.cache_key, json_string)
    end
  end
end
