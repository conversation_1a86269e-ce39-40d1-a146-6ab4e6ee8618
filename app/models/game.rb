class Game < ApplicationRecord
  mount_uploader :cover_url_0, CoverUploader
  mount_uploader :cover_url_1, CoverUploader
  mount_uploader :cover_url_2, CoverUploader
  mount_uploader :cover_url_3, CoverUploader
  mount_uploader :cover_url_4, CoverUploader

  belongs_to :game_platform
  belongs_to :integrator
  belongs_to :game_type

  validates :name, presence: true
  validates :game_platform, presence: true
  validates :integrator, presence: true
  validates :game_type, presence: true
  validates :screen_direction, presence: true
  validates :status, presence: true

  before_save :compute_suggested_at

  # 状态枚举
  enum :status, { offlined: 0, onlined: 1, deleted: 2 }, default: :onlined
  def status_text
    I18n.t("common.status.#{status}")
  end

  # 游戏方向枚举
  enum :screen_direction, { auto: 0, portrait: 1, landscape: 2 }, default: :auto
  def screen_direction_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.screen_directions.#{self.screen_direction}")
  end

  def compute_suggested_at
    self.suggested_at = self.suggested? ? Time.current : nil
  end

  def soft_delete
    self.status = :deleted
    self.save
  end

  # fetch remote game cover from cdn
  def fetch_remote_cover(file_name)
    cdn_host = "https://dudvdg1kabrjm.cloudfront.net"
    cdn_url = "#{cdn_host}/#{file_name}"

    begin
      # use ruby built-in http client to fetch remote file
      response = Net::HTTP.get(URI(cdn_url))

      # create a temporary file to store the downloaded image
      temp_file = Tempfile.new([ "cover", File.extname(file_name) ])
      temp_file.binmode
      temp_file.write(response)
      temp_file.rewind

      self.cover_url_0 = temp_file

      temp_file.close
      temp_file.unlink
    rescue StandardError => e
      Rails.logger.error "Failed to fetch remote cover: #{e.message}"
      puts "Failed to fetch remote cover: #{e.message}: #{cdn_url}"
      false
    end
  end
end
