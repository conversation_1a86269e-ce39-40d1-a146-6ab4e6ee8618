class PaymentType < ApplicationRecord
  include Cacheable
  has_many :payment, dependent: :restrict_with_exception
  has_many :recharge, dependent: :restrict_with_exception
  has_many :withdraw, dependent: :restrict_with_exception

  validates :name, presence: true
  validates :status, inclusion: { in: [ true, false ] }
  validates :weight, presence: true

  def status_text
    if status
      "启用"
    else
      "禁用"
    end
  end

  def self.cached_all
    Rails.cache.fetch(self.cache_key, raw: true) do
      where(status: true).order(weight: :desc).to_json
    end
  end
end
