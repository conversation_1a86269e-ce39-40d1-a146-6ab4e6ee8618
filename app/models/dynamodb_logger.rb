class DynamodbLogger
  TABLE_NAME = "UserBetLogs_v1"

  def self.encode_sort_key(user_id, action_time_epoch)
    user_id_str = format("%010d", user_id)
    time_str    = format("%010d", action_time_epoch.to_i)
    (user_id_str + time_str).to_i
  end

  def self.decode_sort_key(encoded_key)
    key_str = encoded_key.to_s.rjust(20, "0") # 补齐20位
    user_id = key_str[0, 10].to_i
    time_epoch = key_str[10, 10].to_i
    [ user_id, time_epoch ]
  end

  def self.batch_log_bet(bets)
    return { success: true, processed: [] } if bets.empty?

    results = { success: true, processed: [], failed: [] }
    batch_items = bets.map do |bet|
      ttl = (Time.current + 180.days).to_i
      day = Time.current.strftime("%Y-%m-%d")
      tenant_id = bet[:tenant_id] || "tenant_1"
      tenant_id_day = "#{tenant_id}_#{day}"
      user_id_action_time = encode_sort_key(bet[:user_id], bet[:action_time].to_i)
      tenant_id_user_id = "#{tenant_id}_#{bet[:user_id]}"

      {
        put_request: {
          item: {
            "tenantIdDay" => tenant_id_day,
            "userIdActionTime" => user_id_action_time,
            "tenantIdUserId" => tenant_id_user_id,
            "userId" => bet[:user_id],
            "bet" => bet[:bet],
            "real_win" => bet[:real_win],
            "win" => bet[:win],
            "game_number" => bet[:game_number],
            "round_id" => bet[:round_id],
            "before_money" => bet[:before_money],
            "after_money" => bet[:after_money],
            "actionTimeEpoch" => bet[:action_time].to_i,
            "ttl" => ttl
          }
        }
      }
    end

    # DynamoDB 批量写入限制为25条
    batch_items.each_slice(25).with_index do |batch, index|
      begin
        response = DYNAMO_DB_CLIENT.batch_write_item({
          request_items: {
            TABLE_NAME => batch
          }
        })

        puts "DynamodbLogger batch_write_item response: #{response.inspect}"

        # 检查是否有未处理的项
        if response[:unprocessed_items].present? && !response[:unprocessed_items][TABLE_NAME].empty?
          puts "----."
          failed_items = response[:unprocessed_items][TABLE_NAME]
          start_index = index * 25
          failed_items.each do |item|
            original_index = start_index + batch.index(item)
            results[:failed] << bets[original_index]
          end
          results[:success] = false
        end

        # 记录成功处理的项
        start_index = index * 25
        batch.each_with_index do |_, batch_index|
          original_index = start_index + batch_index
          results[:processed] << bets[original_index]
        end
      rescue => e
        puts "----. got error. #{e.message}"
        Rails.logger.error "DynamoDB batch write error: #{e.message}"
        results[:success] = false
        # 记录这个批次的所有项为失败
        start_index = index * 25
        batch.each_with_index do |_, batch_index|
          original_index = start_index + batch_index
          results[:failed] << bets[original_index]
        end
      end
    end

    if results[:success]
      puts "✅ 批量写入成功，共 #{results[:processed].size} 条记录"
    else
      puts "⚠️ 批量写入部分失败，成功：#{results[:processed].size} 条，失败：#{results[:failed].size} 条"
    end

    results
  end

  def self.log_bet(user_id:, bet:, real_win:, win:, game_number:, round_id:, before_money:, after_money:, action_time:, tenant_id: "tenant_1")
    ttl = (Time.current + 180.days).to_i
    day = Time.current.strftime("%Y-%m-%d")

    tenant_id_day = "#{tenant_id}_#{day}"

    user_id_action_time = "#{user_id}_#{action_time.to_i}"
    tenant_id_user_id = "#{tenant_id}_#{user_id}"

    item = {
      "tenantIdDay" => tenant_id_day,
      "userIdActionTime" => user_id_action_time,
      "tenantIdUserId" => tenant_id_user_id,
      "userId" => user_id,
      "bet" => bet,
      "real_win" => real_win,
      "win" => win,
      "game_number" => game_number,
      "round_id" => round_id,
      "before_money" => before_money,
      "after_money" => after_money,
      "actionTimeEpoch" => action_time.to_i,
      "ttl" => ttl
    }

    result = DYNAMO_DB_CLIENT.put_item({
      table_name: TABLE_NAME,
      item: item
    })

    puts "✅ 写入成功"
    result
  end

  def self.query_by_user(user_id:, tenant_id:, start_time: nil, end_time: nil, limit: 100, last_evaluated_key: nil)
    tenant_id_user_id = "#{tenant_id}_#{user_id}"

    expression_values = {
      ":uid" => tenant_id_user_id
    }
    key_expr = "tenantIdUserId = :uid"

    if start_time && end_time
      key_expr += " AND actionTimeEpoch BETWEEN :start AND :end"
      expression_values[":start"] = start_time.to_i
      expression_values[":end"] = end_time.to_i
    end

    query_params = {
      table_name: TABLE_NAME,
      index_name: "UserIdIndex",
      key_condition_expression: key_expr,
      expression_attribute_values: expression_values,
      limit: limit,
      scan_index_forward: false # 按时间倒序返回
    }

    # 👇 添加分页游标（起始位置）
    query_params[:exclusive_start_key] = last_evaluated_key if last_evaluated_key

    puts "Debug: last_evaluated_key: #{last_evaluated_key.inspect}"
    puts "Debug: query_params: #{query_params.inspect}"
    result = DYNAMO_DB_CLIENT.query(query_params)

    puts "DynamoDB returned last_evaluated_key: #{result[:last_evaluated_key].inspect}"

    {
      items: result[:items],
      last_evaluated_key: result[:last_evaluated_key]
    }
  end

  # 查询某天内的日志
  def self.query_user_by_day(user_id:, tenant_id:, day:, limit: 100)
    # day 应该是 Date/Time/String 类型
    day_obj = day.is_a?(String) ? Date.parse(day) : day.to_date
    start_time = Time.new(day_obj.year, day_obj.month, day_obj.day, 0, 0, 0)
    end_time   = start_time + 1.day - 1

    query_by_user(
      user_id: user_id,
      tenant_id: tenant_id,
      start_time: start_time,
      end_time: end_time,
      limit: limit
    )
  end
end
