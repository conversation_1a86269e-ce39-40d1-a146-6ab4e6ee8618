class PopupAd < Ad
  # 跳转类型 enum
  REDIRECT_TYPES = %w[ no_redirect internal external popup ].index_by(&:itself)
  enum :redirect_type, REDIRECT_TYPES, default: :no_redirect
  def redirect_type_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.redirect_types.#{self.redirect_type}")
  end

  # 可见类型
  VISIBILITY_TYPES = %w[ all_users un_paid paid ].index_by(&:itself)
  enum :visibility, VISIBILITY_TYPES, default: :all_users
  def visibility_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.visibilities.#{self.visibility}")
  end

  # 展示时机
  DISPLAY_OCCASION_TYPES = %w[ daily_first_login every_refresh ].index_by(&:itself)
  enum :display_occasion, DISPLAY_OCCASION_TYPES, default: :daily_first_login
  def display_occasion_text
    I18n.t("activerecord.attributes.#{self.class.name.underscore}.display_occasions.#{self.display_occasion}")
  end
end
