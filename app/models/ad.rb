class Ad < ApplicationRecord
  include Cacheable

  mount_uploader :media, AdMediaUploader
  validates :name, presence: true

  # 状态枚举
  enum :status, { offlined: 0, onlined: 1 }, default: :onlined
  def status_text
    I18n.t("common.status.#{status}")
  end

  def self.cached_all
    Rails.cache.fetch(self.cache_key, raw: true) do
      self.onlined.to_json(methods: [ :type, :media_url ], except: [ :media ])
    end
  end

  def media_url
    media.url if media.present?
  end
end
