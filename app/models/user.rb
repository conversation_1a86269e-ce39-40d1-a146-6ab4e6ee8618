class User < ApplicationRecord
  has_secure_password
  has_many :bank_cards, dependent: :destroy
  has_one :wallet # 用户钱包
  has_one :wallet_ext # 用户钱包扩展
  has_one :register_track, dependent: :destroy # 用户注册跟踪
  has_one :user_order_summary, dependent: :destroy # 用户订单统计
  has_many :deposits, -> { order(id: :desc) }, dependent: :destroy
  enum :status, [ :actived, :locked, :deleted ], default: :actived

  # validates :name, presence: true, uniqueness: true
  validates :mobile, presence: true, uniqueness: true
  validates :password_digest, presence: true
  validates :login_count, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :nickname, length: { maximum: 50 }
  validates :timezone, presence: true, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }

  after_create :create_associated_records

  def self.layers = %w[ reg_30m_unpay reg_2h_unpay reg_curday_unpay
  first_nextday_unlogin first_3d_unlogin first_7d_unlogin first_15d_unlogin first_30d_unlogin
  first_curday_win first_curday_loss pay_15d_unlogin].index_by(&:itself)
  # enum :layer_type, layers, default: :reg_30m_unpay

  # def layers_text
  #   I18n.t("activerecord.attributes.#{self.class.name.underscore}.layers.#{self.layers}")
  # end

  def short_id
    $sqids.encode([ id ])
  end

  def self.from_short_id(short_id)
    id = $sqids.decode(short_id).first
    find(id)
  end

  def update_last_login_info(ip:, user_agent:)
    update(
      last_login_ip: ip,
      last_login_user_agent: user_agent,
      last_login_at: Time.current,
      login_count: login_count + 1
    )
  end

   def status_text
    I18n.t("common.status.#{status}")
  end

  private

  def create_associated_records
    create_wallet!
    create_wallet_ext!
    create_register_track!(
      agent_id: 0,
      channel_id: 0,
      parent_id: 0,
      super_id: 0,
      super_parent_id: 0,
      ua: "",
      device_id: "",
      ip: "",
      ad_id: "",
      ad_click_id: "",
      device_type: "0"
    )
    create_user_order_summary!
  end
end
