class BlackBankCard < ApplicationRecord
  # 只验证长度
  validates :card_number, presence: true, length: { in: 10..32 }, uniqueness: true

  # validate :valid_card_number_format

  private

  # def valid_card_number_format
  #   return if card_number.blank?

  #   # Basic validation for bank card number (16-19 digits)
  #   unless card_number.match?(/^\d{16,19}$/)
  #     errors.add(:card_number, "不是有效的银行卡号")
  #   end
  # end
end
