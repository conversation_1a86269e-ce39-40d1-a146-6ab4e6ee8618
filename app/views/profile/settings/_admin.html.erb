<%= form_with(model: admin, url: profile_setting_path, method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>
  <% if admin.errors.any? %>
    <div class="bg-red-50 p-4 rounded-md mb-6">
      <div class="text-red-700">
        <h3 class="text-sm font-medium">请修正以下错误：</h3>
        <ul class="mt-2 list-disc list-inside">
          <% admin.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <div>
    <%= f.label :name, "帐号", class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1 text-sm text-gray-900"><%= admin.name %></div>
  </div>

  <div>
    <%= f.label :roles, "角色", class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1 flex flex-wrap gap-2">
      <% admin.roles.each do |role| %>
        <div class="badge badge-primary"><%= role.name %></div>
      <% end %>
    </div>
  </div>

  <div>
    <%= f.label :department, "部门", class: "block text-sm font-medium text-gray-700" %>
    <div class="mt-1 text-sm text-gray-900"><%= I18n.t("activerecord.attributes.admin.departments.#{admin.department}", default: admin.department&.humanize) %></div>
  </div>

  <div>
    <%= f.label :nickname, "昵称", class: "block text-sm font-medium text-gray-700" %>
    <%= f.text_field :nickname, class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
  </div>

  <div class="flex justify-between items-center">
    <div class="text-xs text-gray-500">
      帐号添加时间: <%= admin.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
    </div>
    <%= f.submit "保存设置", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
  </div>
<% end %>