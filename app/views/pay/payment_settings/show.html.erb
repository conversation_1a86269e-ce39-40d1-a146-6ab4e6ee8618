<%= render "tab" %>

<div class="max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
  
  <%= form_with(url: pay_payment_setting_path(section: params[:section]), method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <% @settings.each do |setting| %>
        <div class="space-y-2 <%= setting.status == "disabled" ? "bg-red-200" : "" %>">
          <%= f.label "settings[#{setting.key}]", setting.label, class: "block text-sm font-medium text-gray-700" %>
          
          <% case setting.value_type %>
          <% when "boolean" %>
            <div class="flex items-center space-x-4">
              <%= f.radio_button "settings[#{setting.key}]", "1", checked: setting.typed_value, class: "h-4 w-4 text-primary-600" %>
              <%= f.label "settings[#{setting.key}]_1", "开启", class: "text-sm text-gray-700" %>
              <%= f.radio_button "settings[#{setting.key}]", "0", checked: !setting.typed_value, class: "h-4 w-4 text-primary-600" %>
              <%= f.label "settings[#{setting.key}]_0", "关闭", class: "text-sm text-gray-700" %>
            </div>
          
          <% when "integer", "float" %>
            <%= f.number_field "settings[#{setting.key}]", 
                value: setting.typed_value,
                step: setting.value_type == "float" ? "0.01" : "1",
                class: "input" %>
          
          <% when "array" %>
            <%= f.text_field "settings[#{setting.key}]", 
                value: setting.typed_value.join(","),
                class: "input",
                placeholder: "多个值用逗号分隔" %>
          
          <% when "json" %>
            <%= f.text_area "settings[#{setting.key}]", 
                value: setting.typed_value.to_json,
                class: "input font-mono",
                rows: 3 %>
          
          <% else %>
            <%= f.text_field "settings[#{setting.key}]", 
                value: setting.typed_value,
                class: "input" %>
          <% end %>

          <% if setting.description.present? %>
            <p class="text-sm text-gray-500"><%= setting.description %></p>
          <% end %>

          <% if setting.status == "disabled" %>
            <p class="text-xs text-red-500">该设置已禁用，请与相关技术联系</p>
          <% end %>
        </div>
      <% end %>
    </div>

    <div class="flex justify-end">
      <%= f.submit "保存设置", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>