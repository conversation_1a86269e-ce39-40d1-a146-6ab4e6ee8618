<div class="flex items-center mb-6">
  <%= link_to pay_payments_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-xl"><%= @payment.new_record? ? "添加支付通道" : "编辑支付通道" %></h1>
</div>

<%= form_with(model: [:pay, @payment], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= form.hidden_field :recharge_method %>
  <%= render "shared/error_messages", object: @payment %>

  <div class="grid grid-cols-2 gap-6">
    <div class="space-y-4">
        <div>
          <%= form.label :name, "名称 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :name, class: "input"%>
        </div>
        <div>
            <%= form.label :payment_way, "充值方式", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :payment_way, Payment.payment_ways.keys, { include_blank: "请选支付方式" }, class: "input" %>
          </div>
       
        <div>
          <%= form.label :recharge_fee_rate, "充值费率 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :recharge_fee_rate, class: "input" %>
        </div>
        <div>
          <%= form.label :min_recharge, "最小充值 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :min_recharge, class: "input" %>
        </div>
        <div>
          <%= form.label :max_recharge, "最大充值 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :max_recharge, class: "input" %>
        </div>
        <div>
          <%= form.label :recharge_weight, "充值权重 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :recharge_weight, class: "input" %>
        </div>
        <div>
          <%= form.label :recharge_enabled, "充值开关 *", class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :recharge_enabled, [["开启", true], ["关闭", false]], {}, class: "input" %>
        </div>

         <div>
            <%= form.label :recharge_request_url, "代收请求地址 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :recharge_request_url, class: "input" %>
          </div>
           <div>
            <%= form.label :ip_whitelist, "IP白名单", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :ip_whitelist, class: "input" %>
          </div>
      </div>
      <div class="space-y-4">
           <div>
          <%= form.label :payment_type_id, "支付类型", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :payment_type_id,
                  PaymentType.where(status: true).map { |pt| [pt.name, pt.id] },
                  { include_blank: "请选择支付类型" },
                  class: "input" %>
          </div>
          
          <div>
            <%= form.label :withdraw_fee_rate, "代付费率 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :withdraw_fee_rate, class: "input" %>
          </div>
          <div>
            <%= form.label :min_withdraw, "最小代付 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :min_withdraw, class: "input" %>
          </div>
          <div>
            <%= form.label :max_withdraw, "最大代付 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :max_withdraw, class: "input" %>
          </div>
          <div>
            <%= form.label :withdraw_weight, "代付权重 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :withdraw_weight, class: "input" %>
          </div>
          <div>
            <%= form.label :withdraw_enabled, "提现开关 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :withdraw_enabled, [["开启", true], ["关闭", false]], {}, class: "input" %>
          </div>

          <div>
            <%= form.label :withdraw_request_url, "代付请求地址 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :withdraw_request_url, class: "input" %>
          </div>
        </div>
  </div>
  <div class="space-y-4">
    <div class="mb-4">
      <h2 class="text-lg font-semibold">支付参数</h2>
    </div>
    <% if Payment.payment_solutions[@payment.recharge_method]&.dig("requestParameters").present? %>
      <% Payment.payment_solutions[@payment.recharge_method]["requestParameters"].each do |field| %>
        <div class="grid grid-cols-12 gap-2 items-center mb-4">
          <label class="col-span-2 text-right text-gray-700"><%= field["explain"] %></label>
          <%= form.fields_for :extras do |extras_form| %>
            <%= extras_form.text_field field["key"], 
                value: @payment.extras&.dig(field["key"]), 
                class: "col-span-10 input border border-gray-300 rounded px-2 py-1",
                placeholder: field["explain"] %>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", pay_payments_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
  </div>
<% end %>
