<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">支付通道</h1>


    <div class="dropdown dropdown-end">
      <div tabindex="0" role="button" class="btn btn-sm">添加支付通道</div>
      <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-1 w-32 p-2 shadow-sm">
        <% Payment.payment_solutions.keys.each do |solution| %>
          <li><%= link_to solution, new_pay_payment_path(payment_solution: solution), data: { turbo: false } %></li>
        <% end %>
      </ul>
    </div>

  </div>

  <div class="bg-white shadow rounded-lg p-4">
    <%= form_tag pay_payments_path, method: :get, class: "flex flex-col sm:flex-row gap-4 items-end" do %>
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
        <%= text_field_tag :search, params[:search], class: "input input-bordered w-full sm:w-64", placeholder: "输入支付名称搜索" %>
      </div>
      <div class="flex gap-2 w-full sm:w-auto">
        <%= submit_tag "搜索", class: "btn btn-primary flex-1 py-1 sm:flex-none" %>
        <%= link_to "重置", pay_payments_path, class: "btn btn-outline flex-1 py-1 sm:flex-none" %>
      </div>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">支付名称</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">支付费率</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最小充值</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最大充值</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">充值权重</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">充值方式</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">充值开关</th>

          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代付费率</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最小代付</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最大代付</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代付权重</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代付开关</th>

          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @payments.each do |vo| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.recharge_fee_rate %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.min_recharge %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.max_recharge %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.recharge_weight %></div>
            </td>
             <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.payment_way %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <% if vo.recharge_enabled? %>
                <span class="badge badge-soft badge-success">已启用</span>
              <% else %>
                <span class="badge badge-soft badge-error">已禁用</span>
              <% end %>
            </td>
             <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.withdraw_fee_rate %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.min_withdraw %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.max_withdraw %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= vo.withdraw_weight %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
            <% if vo.withdraw_enabled? %>  
              <span class="badge badge-soft badge-success">已启用</span>
            <% else %>
              <span class="badge badge-soft badge-error">已禁用</span>
            <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <%= link_to "编辑", edit_pay_payment_path(vo), class: "btn btn-outline", data: { turbo: false } %>
                <%= button_to "删除", pay_payment_path(vo), method: :delete, class: "btn btn-outline btn-error", form: { data: { turbo_confirm: "你确定要删除 ##{vo.id}:#{vo.name} 吗？" } } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <%== pagy_nav(@pagy) %>
  </div>
</div>