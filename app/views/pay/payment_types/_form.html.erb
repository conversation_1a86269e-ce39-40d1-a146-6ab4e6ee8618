<div class="flex items-center mb-6">
  <%= link_to_back pay_payment_types_path %>
  <h1 class="text-xl"><%= @payment_type.new_record? ? "添加支付类型" : "编辑支付类型" %></h1>
</div>

<%= form_with(model: [:pay, @payment_type], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @payment_type %>

  <div class="space-y-4">
    <div>
      <%= form.label :name, "名称 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :name, class: "input"%>
    </div>

    <div>
      <%= form.label :weight, "权重 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :weight, class: "input" %>
    </div>
    <div>
      <%= form.label :status, "状态 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.select :status, [ [ "启用", true ], [ "禁用", false ] ], {}, class: "input" %>
    </div>

    <div>
     
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", pay_payment_types_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>