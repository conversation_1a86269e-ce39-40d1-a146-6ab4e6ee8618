<div class="flex items-center mb-6">
  <%= link_to withdraws_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-2xl font-bold"><%= @withdraw.new_record? ? "添加提现订单" : "编辑提现订单" %></h1>
</div>

<%= form_with(model: [:pay, @withdraw], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @withdraw %>

  <div class="space-y-4">
    <div>
      <%= form.label :user_id, "用户ID *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :user_id, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :amount, "提现金额 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :amount, class: "input", readonly: !@withdraw.new_record?%>
    </div>
    <div>
      <%= form.label :fee, "费率 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :fee, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :real_amount, "实际金额 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :real_amount, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :order_no, "提现订单号 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :order_no, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :status, "提现状态 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :status, class: "input" %>
    </div>
    <div>
      <%= form.label :remark, "备注 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :remark, class: "input" %>
    </div>
    <div>
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", withdraws_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>