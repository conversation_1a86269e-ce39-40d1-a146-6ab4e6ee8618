<div class="px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">Redis Stream 统计信息</h1>

  <div class="mb-4 flex justify-between items-center">
    <span class="text-sm text-gray-500">Redis服务器时间戳: <%= @redis_time %></span>
    <%= button_to "清空队列", clear_stream_system_redis_streams_path, method: :delete, data: { turbo_confirm: "确定要清空队列吗？" }, class: "btn btn-error" %>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Stream 信息 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Stream 信息</h2>
        <div class="overflow-x-auto">
          <table class="table">
            <tbody>
              <tr>
                <td>Stream Key</td>
                <td><%= @stream_key %></td>
              </tr>
              <tr>
                <td>消息数量</td>
                <td><%= @stream_info["length"] %></td>
              </tr>
              <tr>
                <td>总数量</td>
                <td><%= @stream_info["entries-added"] %></td>
              </tr>
              <tr>
                <td>最后生成ID</td>
                <td><%= @stream_info["last-generated-id"] %></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Consumer Group 信息 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Consumer Group 信息</h2>
        <div class="overflow-x-auto">
          <table class="table">
            <tbody>
              <tr>
                <td>Group Name</td>
                <td><%= @group_name %></td>
              </tr>
              <tr>
                <td>Consumer Name</td>
                <td><%= @consumer_name %></td>
              </tr>
              <tr>
                <td>消费者数量</td>
                <td><%= @group_info.first&.dig("consumers") || 0 %></td>
              </tr>
              <tr>
                <td>已读消息数</td>
                <td><%= @group_info.first&.dig("entries-read") || 0 %></td>
              </tr>
              <tr>
                <td>未读消息数</td>
                <td><%= @unread_count %></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pending 消息信息 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Pending 消息信息</h2>
        <div class="overflow-x-auto">
          <table class="table">
            <tbody>
              <tr>
                <td>待处理消息数</td>
                <td><%= @pending_info["count"] %></td>
              </tr>
              <tr>
                <td>最小ID</td>
                <td><%= @pending_info["min"] %></td>
              </tr>
              <tr>
                <td>最大ID</td>
                <td><%= @pending_info["max"] %></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Consumers 信息 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Consumers 信息</h2>
        <div class="overflow-x-auto">
          <table class="table">
            <thead>
              <tr>
                <th>Consumer Name</th>
                <th>待处理消息数</th>
                <th>空闲时间</th>
              </tr>
            </thead>
            <tbody>
              <% if @consumers_info.any? %>
                <% @consumers_info.each do |consumer| %>
                  <tr>
                    <td><%= consumer["name"] %></td>
                    <td><%= consumer["pending_count"] %></td>
                    <td><%= "#{(consumer["idle"].to_f / 1000).round(0)} 秒" %></td>
                  </tr>
                <% end %>
              <% else %>
                <tr>
                  <td colspan="4" class="text-center">暂无消费者信息</td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div> 