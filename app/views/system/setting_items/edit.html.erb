<div role="tablist" class="tabs tabs-border">
  <% @sections.each do |section| %>
  <%= link_to section, system_setting_items_path(section: section), role: "tab", class: "tab #{params[:section] == section ? 'tab-active' : ''}" %>
  <% end %>
</div>


<div class="max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
  <h2 class="text-xl mb-6">编辑配置项</h2>

  <%= form_with(model: @setting_item, url: system_setting_item_path(@setting_item), method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>
    <% if @setting_item.errors.any? %>
      <div class="bg-red-50 p-4 rounded-md mb-6">
        <div class="text-red-700">
          <h3 class="text-sm font-medium">请修正以下错误：</h3>
          <ul class="mt-2 list-disc list-inside">
            <% @setting_item.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <%= f.label :key, "键", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_field :key, class: "input", disabled: true %>
      </div>

      <div>
        <%= f.label :section, "分组", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_field :section, class: "input", disabled: true %>
      </div>

      <div>
        <%= f.label :label, "标签", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_field :label, class: "input" %>
      </div>

      <div>
        <%= f.label :value_type, "值类型", class: "block text-sm font-medium text-gray-700" %>
        <%= f.select :value_type, Setting::VALUE_TYPES.map { |type| [type, type] }, {}, class: "input" %>
      </div>

      <div>
        <%= f.label :value, "值", class: "block text-sm font-medium text-gray-700" %>
        <% case @setting_item.value_type %>
        <% when "boolean" %>
          <div class="flex items-center space-x-4">
            <%= f.radio_button :value, "1", checked: @setting_item.typed_value, class: "h-4 w-4 text-primary-600" %>
            <%= f.label :value_1, "开启", class: "text-sm text-gray-700" %>
            <%= f.radio_button :value, "0", checked: !@setting_item.typed_value, class: "h-4 w-4 text-primary-600" %>
            <%= f.label :value_0, "关闭", class: "text-sm text-gray-700" %>
          </div>
        <% when "integer", "float" %>
          <%= f.number_field :value, 
              value: @setting_item.typed_value,
              step: @setting_item.value_type == "float" ? "0.01" : "1",
              class: "input" %>
        <% when "array" %>
          <%= f.text_field :value, 
              value: @setting_item.typed_value.join(","),
              class: "input",
              placeholder: "多个值用逗号分隔" %>
        <% when "json" %>
          <%= f.text_area :value, 
              value: @setting_item.typed_value.to_json,
              class: "input font-mono",
              rows: 3 %>
        <% else %>
          <%= f.text_field :value, class: "input" %>
        <% end %>
      </div>

      <div>
        <%= f.label :options, "选项", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_area :options, class: "input", rows: 3 %>
      </div>

      <div>
        <%= f.label :description, "描述", class: "block text-sm font-medium text-gray-700" %>
        <%= f.text_area :description, class: "input", rows: 3 %>
      </div>

      <div>
        <%= f.label :status, "状态", class: "block text-sm font-medium text-gray-700" %>
        <%= f.select :status, [["启用", "enabled"], ["禁用", "disabled"]], {}, class: "input" %>
      </div>

      <div>
        <%= f.label :sort, "排序", class: "block text-sm font-medium text-gray-700" %>
        <%= f.number_field :sort, class: "input" %>
      </div>
    </div>

    <div class="flex justify-end space-x-4">
      <%= link_to "返回", system_setting_items_path(section: @setting_item.section), class: "btn btn-outline" %>
      <%= f.submit "保存", class: "btn btn-primary" %>
    </div>
  <% end %>
</div> 