<div role="tablist" class="tabs tabs-border">
  <% @sections.each do |section| %>
  <%= link_to section, system_setting_items_path(section: section), role: "tab", class: "tab #{params[:section] == section ? 'tab-active' : ''}" %>
  <% end %>
</div>

<div class="p-4 flex items-center">
  <div class="w-20 text-primary text-xs"><%= @setting_items.size %> keys.</div>
  <div class="p-2 text-wrap break-all bg-gray-200 text-xs">
    <%= @section_settings %>
  </div>
</div>

<table class="table table-zebra w-full">
  <thead>
    <tr>
      <th>ID</th>
      <th>Key</th>
      <th>标签</th>
      <th>描述</th>
      <th>值</th>
      <th>类型</th>
      <th>选项</th>
      <th>排序</th>
      <th>状态</th>
      <th>操作</th>
    </tr>
  </thead>
  
  <% @setting_items.each do |setting_item| %>
  <tr>
    <td><%= setting_item.id %></td>
    <td><%= setting_item.key %></td>
    <td><%= setting_item.label %></td>
    <td><%= setting_item.description %></td>
    <td><%= setting_item.value %></td>
    <td><%= setting_item.value_type %></td>
    <td><%= setting_item.options %></td>
    <td><%= setting_item.sort %></td>
    <td>
      <% if setting_item.enabled? %>
        <span class="badge badge-soft badge-success"><%= setting_item.status_text %></span>
      <% else %>
        <span class="badge badge-soft badge-error"><%= setting_item.status_text %></span>
      <% end %>
    </td>
    <td><%= link_to "编辑", edit_system_setting_item_path(setting_item, section: @section), class: "btn btn-sm btn-outline btn-primary" %></td>
  </tr>
  <% end %>
</table>