<div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 w-full max-w-md">
  <h2 class="text-center text-3xl font-extrabold text-gray-900 mb-8">
    注册新账号
  </h2>

  <%= form_with(model: @user, url: registrations_path(channel_id: @channel_id), class: "space-y-6") do |f| %>
    <% if @user.errors.any? %>
      <div class="alert alert-error">
        <ul>
          <% @user.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div>
      <%= f.label :mobile, "手机号", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= f.text_field :mobile, class: "input input-bordered w-full", required: true, placeholder: "请输入手机号" %>
      </div>
    </div>

    <div>
      <%= f.label :password, "密码", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= f.password_field :password, class: "input input-bordered w-full", required: true, placeholder: "请输入密码" %>
      </div>
    </div>

    <%= f.hidden_field :device_id, value: params[:device_id] %>
    <%= f.hidden_field :ad_id, value: params[:ad_id] %>
    <%= f.hidden_field :ad_click_id, value: params[:ad_click_id] %>
    <%= f.hidden_field :device_type, value: params[:device_type] %>

    <div>
      <%= f.submit "注册", class: "btn btn-primary w-full" %>
    </div>
  <% end %>

  <div class="mt-6">
    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-white text-gray-500">
          已有账号？
        </span>
      </div>
    </div>

    <div class="mt-6">
      <%= link_to "登录", new_session_path, class: "btn btn-outline w-full" %>
    </div>
  </div>
</div> 