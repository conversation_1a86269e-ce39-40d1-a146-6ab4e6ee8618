<nav class="bg-white border-b border-gray-200" data-controller="toggle">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <div class="flex">
        <div class="flex-shrink-0 flex items-center">
          <a href="<%= root_path %>" class="ml-2 text-xl font-bold text-gray-900">xxxx</a>
        </div>
        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
          <%# --- Desktop Menu --- %>
          <a href="<%= root_path %>" class="<%= nav_link_class(root_path, type: :desktop) %>">首页</a>
          <a href="#" class="<%= nav_link_class(service_path, type: :desktop) %>">服务</a>
        </div>
      </div>
      <div class="hidden sm:ml-6 sm:flex sm:items-center">
        <!-- 购物车图标 -->
        <div class="mr-4">
          <%= link_to cart_path, class: "relative text-gray-600 hover:text-emerald-600" do %>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
            </svg>
            <% if session[:cart_items].present? && session[:cart_items].any? %>
              <%= render 'shared/cart_icon' %>
            <% end %>
          <% end %>
        </div>
        
        <% if authenticated? %>
          <div class="flex items-center">
            <span class="text-gray-700 mr-4"><%= Current.admin.email_address %></span>
            <%= button_to session_path, method: :delete, class: "text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium" do %>
              退出登录
            <% end %>
          </div>
        <% else %>
          <%= link_to new_session_path, class: "text-gray-700 hover:text-gray-900 px-3 py-2 hover:underline rounded-md text-sm font-medium mr-4" do %>
            登录
          <% end %>
          <%= link_to "立即咨询", new_conversation_path, class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
        <% end %>
      </div>
      <div class="-mr-2 flex items-center sm:hidden">
        <!-- 移动端购物车图标 -->
        <div class="mr-4">
          <%= link_to cart_path, class: "relative text-gray-600 hover:text-emerald-600" do %>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
            </svg>
            <% if session[:cart_items].present? && session[:cart_items].any? %>
              <%= render 'shared/cart_icon' %>
            <% end %>
          <% end %>
        </div>
        
        <%# --- Mobile Menu Button --- %>
        <%# data-action="click->toggle#toggle" 触发 toggle 方法 %>
        <%# aria-controls="mobile-menu" 指向被控制的元素 ID %>
        <%# aria-expanded="false" 初始状态为折叠 %>
        <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
                aria-expanded="false"
                aria-controls="mobile-menu"
                data-action="click->toggle#toggle">
          <span class="sr-only">打开主菜单</span>
          <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <%# <svg class="hidden h-6 w-6" ...> Close Icon </svg> %>
        </button>
      </div>
    </div>
  </div>

  <%# --- Mobile Menu --- %>
  <%# id="mobile-menu" 对应按钮的 aria-controls %>
  <%# data-toggle-target="element" 标记为被控制器切换的目标 %>
  <%# class="... hidden" 初始状态为隐藏 %>
  <div class="sm:hidden hidden" id="mobile-menu" data-toggle-target="element">
    <div class="pt-2 pb-3 space-y-1">
      <a href="<%= root_path %>" class="<%= nav_link_class(root_path, type: :mobile) %>">首页</a>
      <a href="<%= service_path %>" class="<%= nav_link_class(service_path, type: :mobile) %>">服务</a>
      <a href="<%= blogs_path %>" class="<%= nav_link_class(blogs_path, type: :mobile, controller: 'blogs') %>">博客</a>
      <a href="<%= products_path %>" class="<%= nav_link_class(products_path, type: :mobile, controller: 'products') %>">商品</a>
      <a href="<%= contact_path %>" class="<%= nav_link_class(contact_path, type: :mobile) %>">联系我们</a>
    </div>
    <div class="pt-4 pb-3 border-t border-gray-200">
      <div class="flex items-center px-4">
        <% if authenticated? %>
          <div class="flex items-center">
            <span class="text-gray-700 mr-4"><%= Current.admin.email_address %></span>
            <%= button_to session_path, method: :delete, class: "text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium w-full" do %>
              退出登录
            <% end %>
          </div>
        <% else %>
          <%= link_to new_session_path, class: "text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium w-full" do %>
            登录
          <% end %>
          <%= link_to "立即咨询", new_conversation_path, class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium w-full" %>
        <% end %>
      </div>
    </div>
  </div>
</nav>