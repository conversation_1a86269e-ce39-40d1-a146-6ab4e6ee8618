<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl flex items-center gap-2">
      <%= link_to_back agent_portal_channels_path %>
      添加渠道
    </h1>
  </div>

  <div class="bg-base-100 shadow-xl rounded-lg p-6">
    <%= form_with(model: [@channel], url: agent_portal_channels_path, class: "space-y-6", data: { turbo: false }) do |f| %>
      <%= render "shared/error_messages", object: @channel %>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-control">
          <%= f.label :name, "渠道名称 *", class: "label" %>
          <%= f.text_field :name, class: "input input-bordered w-full", required: true %>
          <p class="label text-xs">
            渠道名称只能包含字母、数字、下划线、中划线、点, 长度为2-24个字符。
          </p>
        </div>

        <div class="form-control">
          <%= f.label :nickname, "昵称", class: "label" %>
          <%= f.text_field :nickname, class: "input input-bordered w-full" %>
          <p class="label text-xs">
            如果不填写，将使用渠道名称作为昵称
          </p>
        </div>

        <div class="form-control">
          <%= f.label :promotion_type_id, "推广类型 *", class: "label" %>
          <%= f.select :promotion_type_id,
                       options_from_collection_for_select(@promotion_types, :id, :name, @channel.promotion_type_id),
                       { prompt: "请选择推广类型" },
                       { class: "select select-bordered w-full", required: true } %>
        </div>

        <div class="form-control">
          <%= f.label :advertiser, "广告方", class: "label" %>
          <%= f.text_field :advertiser, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control">
          <%= f.label :pixel_id, "像素ID", class: "label" %>
          <%= f.text_field :pixel_id, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control">
          <%= f.label :pixel_token, "像素Token", class: "label" %>
          <%= f.text_field :pixel_token, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control md:col-span-2">
          <%= f.label :remark, "备注", class: "label" %>
          <%= f.text_area :remark, class: "textarea textarea-bordered w-full", rows: 3 %>
          <p class="label text-xs">
            备注信息最多50个字符
          </p>
        </div>
      </div>

      <div class="flex justify-end gap-4">
        <%= link_to "取消", agent_portal_channels_path, class: "btn btn-outline" %>
        <%= f.submit "保存", class: "btn btn-primary" %>
      </div>
    <% end %>
  </div>
</div>
