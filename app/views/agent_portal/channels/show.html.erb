<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl flex items-center gap-2">
      <%= link_to_back agent_portal_channels_path %>
      渠道详情
    </h1>
    <div class="flex gap-2">
      <%= link_to "编辑", edit_agent_portal_channel_path(@channel), class: "btn btn-primary btn-sm" %>
    </div>
  </div>

  <div class="bg-base-100 shadow-xl rounded-lg p-6">
    <div class="border-b border-base-300 pb-4 mb-6">
      <h2 class="text-lg font-semibold">基本信息</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <div class="text-sm font-medium text-base-content/60">渠道名称</div>
        <div class="text-base font-medium"><%= @channel.name %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">昵称</div>
        <div class="text-base"><%= @channel.nickname %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">推广类型</div>
        <div class="text-base"><%= @channel.promotion_type.name %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">状态</div>
        <div>
          <span class="badge <%= @channel.status == 'actived' ? 'badge-success' : 'badge-error' %>">
            <%= @channel.status == 'actived' ? '正常' : '禁用' %>
          </span>
        </div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">广告方</div>
        <div class="text-base"><%= @channel.advertiser || "未设置" %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">像素ID</div>
        <div class="text-base"><%= @channel.pixel_id || "未设置" %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">像素Token</div>
        <div class="text-base">
          <%= @channel.pixel_token ? "已设置" : "未设置" %>
        </div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">登录次数</div>
        <div class="text-base"><%= @channel.login_count %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">最后登录时间</div>
        <div class="text-base">
          <%= @channel.last_login_at&.strftime("%Y-%m-%d %H:%M:%S") || "从未登录" %>
        </div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">最后登录IP</div>
        <div class="text-base"><%= @channel.last_login_ip || "无" %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">创建时间</div>
        <div class="text-base"><%= @channel.created_at.strftime("%Y-%m-%d %H:%M:%S") %></div>
      </div>
      
      <div>
        <div class="text-sm font-medium text-base-content/60">更新时间</div>
        <div class="text-base"><%= @channel.updated_at.strftime("%Y-%m-%d %H:%M:%S") %></div>
      </div>
    </div>
    
    <% if @channel.remark.present? %>
      <div class="mt-6 pt-6 border-t border-base-300">
        <div class="text-sm font-medium text-base-content/60 mb-2">备注</div>
        <div class="text-base"><%= @channel.remark %></div>
      </div>
    <% end %>
  </div>
</div>
