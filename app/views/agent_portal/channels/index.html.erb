<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl">我的渠道</h1>
    <%= link_to "添加渠道", new_agent_portal_channel_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="bg-base-100 shadow-xl rounded-lg p-6">
    <% if @channels.any? %>
      <div class="overflow-x-auto">
        <table class="table w-full">
          <thead>
            <tr>
              <th>ID</th>
              <th>渠道名称</th>
              <th>昵称</th>
              <th>推广类型</th>
              <th>状态</th>
              <th>最后登录</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% @channels.each do |channel| %>
              <tr>
                <td><%= channel.id %></td>
                <td>
                  <%= link_to channel.name, agent_portal_channel_path(channel),
                      class: "link link-primary" %>
                </td>
                <td><%= channel.nickname %></td>
                <td><%= channel.promotion_type.name %></td>
                <td>
                  <span class="badge <%= channel.status == 'actived' ? 'badge-success' : 'badge-error' %>">
                    <%= channel.status == 'actived' ? '正常' : '禁用' %>
                  </span>
                </td>
                <td><%= channel.last_login_at&.strftime("%Y-%m-%d %H:%M") || "从未登录" %></td>
                <td><%= channel.created_at.strftime("%Y-%m-%d %H:%M") %></td>
                <td>
                  <div class="flex gap-2">
                    <%= link_to "查看", agent_portal_channel_path(channel), class: "btn btn-sm btn-info" %>
                    <%= link_to "编辑", edit_agent_portal_channel_path(channel), class: "btn btn-sm btn-warning" %>
                    <%= button_to "删除", agent_portal_channel_path(channel),
                        method: :delete,
                        class: "btn btn-sm btn-error btn-outline",
                        data: { turbo_confirm: "确定要删除渠道 #{channel.name} 吗？" } %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% else %>
      <div class="text-center py-12">
        <div class="text-base-content/60 text-lg mb-4">
          您还没有创建任何渠道
        </div>
        <%= link_to "创建第一个渠道", new_agent_portal_channel_path,
            class: "btn btn-primary" %>
      </div>
    <% end %>
  </div>
</div>
