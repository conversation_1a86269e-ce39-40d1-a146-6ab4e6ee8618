<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">系统日志</h1>
  </div>

  <div class="bg-white shadow rounded-lg p-4">
    <%= form_tag back_manage_system_logs_path, method: :get, class: "flex flex-col sm:flex-row gap-4 items-end" do %>
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
        <%= text_field_tag :search, params[:search], 
            class: "input input-bordered w-full sm:w-64", 
            placeholder: "搜索动作..." %>
      </div>

      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
        <%= select_tag :operate, 
            options_for_select(SystemLog.operates.map { |k, v| [k.titleize, k] }, params[:operate]),
            include_blank: "全部",
            class: "select select-bordered w-full sm:w-48" %>
      </div>

      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">操作者类型</label>
        <%= select_tag :actor_type,
            options_for_select([["管理员", "Admin"]], params[:actor_type]),
            include_blank: "全部",
            class: "select select-bordered w-full sm:w-48" %>
      </div>

      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">对象类型</label>
        <%= select_tag :loggable_type,
            options_for_select([["管理员", "Admin"]], params[:loggable_type]),
            include_blank: "全部",
            class: "select select-bordered w-full sm:w-48" %>
      </div>

      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
        <%= date_field_tag :start_date, params[:start_date], 
            class: "input input-bordered w-full sm:w-48" %>
      </div>

      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
        <%= date_field_tag :end_date, params[:end_date], 
            class: "input input-bordered w-full sm:w-48" %>
      </div>

      <div class="flex gap-2 w-full sm:w-auto">
        <%= submit_tag "搜索", class: "btn btn-primary flex-1 py-1 sm:flex-none" %>
        <%= link_to "重置", back_manage_system_logs_path, class: "btn btn-outline flex-1 py-1 sm:flex-none" %>
      </div>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作者</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作对象</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">动作</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">浏览器信息</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">添加时间</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @system_logs.each do |log| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= log.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="badge badge-<%= log.operate == 'login' ? 'success' : 
                                       log.operate == 'logout' ? 'warning' : 
                                       log.operate.include?('create') ? 'info' : 
                                       log.operate.include?('destroy') ? 'error' : 'neutral' %>">
                <%= log.operate.titleize %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <% if log.actor %>
                <div class="text-sm font-mono text-gray-900"><%= "#{log.actor_type}##{log.actor_id}" %></div>
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <% if log.loggable %>
                <div class="text-sm font-mono text-gray-900"><%= "#{log.loggable_type}##{log.loggable_id}" %></div>
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900"><%= log.action %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900"><%= log.ip %></div>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900 max-w-xs truncate" title="<%= log.user_agent %>">
                <%= log.user_agent %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= log.created_at.strftime("%Y-%m-%d %H:%M:%S") %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button type="button" 
                      class="btn btn-sm btn-info" 
                      onclick="document.getElementById('snapshotModal<%= log.id %>').showModal()">
                查看快照
              </button>
            </td>
          </tr>

          <dialog id="snapshotModal<%= log.id %>" class="modal">
            <div class="modal-box w-11/12 max-w-5xl">
              <h3 class="font-bold text-lg mb-4">快照详情</h3>
              <div class="bg-base-200 p-4 rounded-lg">
                <pre class="whitespace-pre-wrap break-words"><%= JSON.pretty_generate(log.snapshot) if log.snapshot.present? %></pre>
              </div>
              <div class="modal-action">
                <form method="dialog">
                  <button class="btn">关闭</button>
                </form>
              </div>
            </div>
            <form method="dialog" class="modal-backdrop">
              <button>关闭</button>
            </form>
          </dialog>
        <% end %>
      </tbody>
    </table>

    <div class="px-6 py-4">
      <%== pagy_nav(@pagy) %>
    </div>
  </div>
</div>
