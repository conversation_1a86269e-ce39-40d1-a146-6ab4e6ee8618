<div class="flex items-center mb-6">
  <%= link_to back_manage_admins_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-xl"><%= @admin.new_record? ? "添加管理员" : "编辑管理员" %></h1>
</div>

<%= form_with(model: [:back_manage, @admin], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @admin %>

  <div class="space-y-4">
    <div>
      <%= form.label :name, "管理员帐号 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :name, class: "input", readonly: !@admin.new_record? %>
    </div>

    <div>
      <%= form.label :nickname, "昵称", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :nickname, class: "input" %>
    </div>

    <div>
      <%= form.label :email_address, "邮箱", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :email_address, class: "input" %>
    </div>

    <div>
      <%= form.label :remark, "备注", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_area :remark, class: "input", rows: 2 %>
    </div>

    <div>
    <%= form.label :status, "状态 *", class: "block text-sm font-medium text-gray-700" %>
    <%= form.select :status, ["actived", "locked"].map { |status| [I18n.t("activerecord.attributes.admin.statuses.#{status}"), status] }, {}, class: "input" %>
  </div>

    <div>
      <%= form.label :role_ids, "角色", class: "block text-sm font-medium text-gray-700 mb-2" %>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <% Role.actived.all.each do |role| %>
          <div class="flex items-center">
            <%= check_box_tag "admin[role_ids][]", role.id, @admin.role_ids.include?(role.id), 
                id: "admin_role_ids_#{role.id}",
                class: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" %>
            <%= label_tag "admin_role_ids_#{role.id}", role.name, 
                class: "ml-2 block text-sm text-gray-700 cursor-pointer select-none" %>
          </div>
        <% end %>
      </div>
    </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", back_manage_admins_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>