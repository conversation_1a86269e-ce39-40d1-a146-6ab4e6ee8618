<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">管理员列表</h1>
    <%= link_to "添加管理员", new_back_manage_admin_path, class: "btn btn-primary" %>
  </div>

  <div class="bg-white shadow rounded-lg p-4">
    <%= form_tag back_manage_admins_path, method: :get, class: "flex flex-col sm:flex-row gap-4 items-end" do %>
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
        <%= text_field_tag :search, params[:search], class: "input input-bordered w-full sm:w-64", placeholder: "输入管理员名称搜索" %>
      </div>
      <div class="flex gap-2 w-full sm:w-auto">
        <%= submit_tag "搜索", class: "btn btn-primary flex-1 py-1 sm:flex-none" %>
        <%= link_to "重置", back_manage_admins_path, class: "btn btn-outline flex-1 py-1 sm:flex-none" %>
      </div>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">帐号</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">昵称</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">登录次数</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录时间</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">帐号状态</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @admins.each do |admin| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= admin.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= admin.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= admin.nickname %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= admin.login_count %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= format_time(admin.last_login_at) %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= admin.remark %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= status_badge(admin) %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <%= button_to "重置密码", reset_password_back_manage_admin_path(admin), method: :post, class: "btn btn-outline", form: { data: { turbo_confirm: "你确定要重置 ##{admin.id}:#{admin.name} 这个管理员帐号的密码吗？" } } %>
                <%= link_to "编辑", edit_back_manage_admin_path(admin), class: "btn btn-outline" %>
                <%= button_to "删除", back_manage_admin_path(admin), method: :delete, class: "btn btn-outline btn-error", form: { data: { turbo_confirm: "你确定要删除 ##{admin.id}:#{admin.name} 这个管理员帐号吗？" } } %>
              
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <%== pagy_nav(@pagy) %>
  </div>
</div>