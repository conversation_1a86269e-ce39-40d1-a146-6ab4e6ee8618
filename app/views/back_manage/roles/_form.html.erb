<div class="flex items-center mb-6">
  <%= link_to back_manage_roles_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-2xl font-bold"><%= @role.new_record? ? "添加角色" : "编辑角色" %></h1>
</div>

<%= form_with(model: [:back_manage, @role], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @role %>

  <div class="space-y-4">
    <div>
      <%= form.label :name, "角色名称 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :name, class: "input", readonly: !@role.new_record? %>
    </div>

    <div>
      <%= form.label :code, "角色编码 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :code, class: "input", readonly: !@role.new_record? %>
    </div>

    <div>
      <%= form.label :description, "角色描述", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_area :description, class: "input", rows: 2 %>
    </div>

    <div>
      <%= form.label :status, "状态 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.select :status, Role.statuses.keys.map { |status| [I18n.t("activerecord.attributes.role.statuses.#{status}"), status] }, {}, class: "input" %>
    </div>

    <div>
    <%= form.label :menu_ids, "菜单", class: "block text-sm font-medium text-gray-700 mb-2" %>
    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
      <% Menu.all.each do |menu| %>
        <div class="flex items-center">
          <%= check_box_tag "role[menu_ids][]", menu.id, @role.menu_ids.include?(menu.id), 
              id: "role_menu_ids_#{menu.id}",
              class: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" %>
          <%= label_tag "role_menu_ids_#{menu.id}", menu.title, 
              class: "ml-2 block text-sm text-gray-700 cursor-pointer select-none" %>
        </div>
      <% end %>
    </div>
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", back_manage_roles_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>