<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
  <h1 class="text-xl">角色列表</h1>
  <%= link_to "添加角色", new_back_manage_role_path, class: "btn btn-primary" %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色名称</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色编码</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色描述</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @roles.each do |role| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= role.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= role.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= role.code %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= role.description %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= status_badge(role) %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <%= link_to "编辑", edit_back_manage_role_path(role), class: "btn btn-outline" %>
                <%= button_to "删除", back_manage_role_path(role), method: :delete, class: "btn btn-outline btn-error", form: { data: { turbo_confirm: "你确定要删除 ##{role.id}:#{role.name} 这个角色吗？" } } %>
              
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%== pagy_nav(@pagy) %>
  </div>
</div>