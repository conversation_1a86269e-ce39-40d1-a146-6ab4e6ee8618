<%= form_with(model: [:risk_manage, @black_bank_card], data: { turbo: false }) do |f| %>
  <%= render "shared/error_messages", object: @black_bank_card %>

  <div class="space-y-4">
    <div>
      <%= f.label :card_number, "银行卡号", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :card_number, class: "input" %>
    </div>

    <div>
      <%= f.label :remark, "备注", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_area :remark, rows: 3, class: "input" %>
    </div>
  </div>

  <div class="mt-2 flex justify-end space-x-3">
    <%= link_to "取消", risk_manage_black_bank_cards_path, class: "btn btn-outline" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 