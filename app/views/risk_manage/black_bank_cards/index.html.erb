<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: risk_manage_black_bank_cards_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "银行卡号 或者 备注" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to "添加银行卡黑名单", new_risk_manage_black_bank_card_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">银行卡号</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">备注</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加时间</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @black_bank_cards.each do |black_bank_card| %>
          <tr>
            <td class="px-6 py-4">
              <%= black_bank_card.id %>
            </td>
            <td class="px-6 py-4">
              <%= black_bank_card.card_number %>
            </td>
            <td class="px-6 py-4">
              <%= black_bank_card.remark %>
            </td>
            <td class="px-6 py-4">
              <%= black_bank_card.created_by %>
            </td>
            <td class="px-6 py-4">
              <%= black_bank_card.created_at&.strftime("%Y-%m-%d %H:%M:%S") %>
            </td>
            <td class="px-6 py-4 text-sm leading-5">
              <div class="flex justify-center gap-2">
                <%= button_to '删除', risk_manage_black_bank_card_path(black_bank_card), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除银行卡【#{black_bank_card.card_number}】吗？" } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%== pagy_nav(@pagy) %>
  </div>
</div> 