<%= form_with(model: [:risk_manage, @blacklist_record], data: { turbo: false }) do |f| %>
  <%= render "shared/error_messages", object: @blacklist_record %>

  <div class="space-y-4">
    <div>
      <%= f.label :user_id, "用户ID", class: "block text-sm font-medium text-gray-700" %>
      <%= f.number_field :user_id, class: "input" %>
    </div>

    <div>
      <%= f.label :ban_type, "黑名单类型", class: "block text-sm font-medium text-gray-700" %>
      <%= f.select :ban_type, BlacklistRecord.ban_types.keys, { include_blank: "请选择黑名单类型" }, class: "input" %>
    </div>

    <div>
      <%= f.label :reason, "原因", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_area :reason, rows: 3, class: "input" %>
    </div>

    <div>
      <%= f.label :actived, "是否生效", class: "block text-sm font-medium text-gray-700" %>
      <%= f.check_box :actived, class: "form-checkbox" %>
    </div>

    <div>
      <%= f.label :expires_at, "解封时间", class: "block text-sm font-medium text-gray-700" %>
      <%= f.datetime_field :expires_at, class: "input" %>
      <span class="text-sm text-gray-500">如果为空，则表示永久黑名单</span>
    </div>
  </div>

  <div class="mt-2 flex justify-end space-x-3">
    <%= link_to "取消", risk_manage_blacklist_records_path, class: "btn btn-outline" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 