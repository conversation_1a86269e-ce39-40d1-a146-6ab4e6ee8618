<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: risk_manage_blacklist_records_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "用户ID 或者 原因" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to "添加用户黑名单", new_risk_manage_blacklist_record_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">用户ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">黑名单类型</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">原因</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">解封时间</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加时间</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @blacklist_records.each do |record| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.id %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.user_id %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.ban_type %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.reason %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.actived ? "生效" : "已解封" %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.expires_at&.strftime("%Y-%m-%d %H:%M:%S") %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.created_by %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= record.created_at&.strftime("%Y-%m-%d %H:%M:%S") %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
              <%= button_to "删除", risk_manage_blacklist_record_path(record), method: :delete, class: "text-red-600 hover:text-red-900", data: { confirm: "确定要删除这条记录吗？" } %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="mt-4">
    <%== pagy_nav(@pagy) %>
  </div>
</div> 