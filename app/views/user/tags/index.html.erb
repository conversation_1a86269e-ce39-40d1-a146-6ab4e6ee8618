<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">会员标签</h1>
     <%= link_to "添加标签", new_user_tag_path, class: "btn btn-primary" %>
  </div>

  <div class="bg-white shadow rounded-lg p-4">
    <%= form_tag user_tags_path, method: :get, class: "flex flex-col sm:flex-row gap-4 items-end" do %>
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
        <%= text_field_tag :search, params[:search], class: "input input-bordered w-full sm:w-48", placeholder: "输入名称" %>
      </div>
      <div class="flex gap-2 w-full sm:w-auto">
        <%= submit_tag "搜索", class: "btn btn-primary flex-1 py-1 sm:flex-none" %>
        <%= link_to "重置", user_tags_path, class: "btn btn-outline flex-1 py-1 sm:flex-none" %>
      </div>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签名称</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">添加时间</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @tags.each do |tag| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= tag.id %></div>
            </td>
          
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= tag.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= tag.description %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= tag.created_by %></div>
            </td>      
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= status_badge(tag) %></div>
            </td> 
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= format_time(tag.created_at) %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <%= link_to "编辑", edit_user_tag_path(tag), class: "btn btn-outline" %>
                <%= button_to "删除", user_tag_path(tag), method: :delete, class: "btn btn-outline btn-error", form: { data: { turbo_confirm: "你确定要删除 ##{tag.id}:#{tag.name} 吗？" } } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <%== pagy_nav(@pagy) %>
  </div>
</div>