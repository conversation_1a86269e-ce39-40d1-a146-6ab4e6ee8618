<div class="flex items-center mb-6">
  <%= link_to user_tags_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-2xl font-bold"><%= @tag.new_record? ? "添加会员标签" : "编辑会员标签" %></h1>
</div>

<%= form_with(model: [:user, @tag], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @tag %>

  <div class="space-y-4">
    <div>
      <%= form.label :name, "名称 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :name, class: "input"%>
    </div>

    <div>
      <%= form.label :description, "描述 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :description, class: "textarea" %>
    </div>
     <div>
      <%= form.label :remark, "备注", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :remark, class: "input" %>
    </div>
    <div>
      <%= form.label :status, "状态 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.select :status, PaymentType.statuses.keys.map { |status| [I18n.t("activerecord.attributes.role.statuses.#{status}"), status] }, {}, class: "input" %>
    </div>

    <div>
     
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", pay_payment_types_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>