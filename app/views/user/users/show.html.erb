<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl flex items-center gap-2">
      <%= link_to_back user_users_path %>
      会员资料 ID:<%= @user.id %>
    </h1>
  </div>
</div>
<div role="tablist" class="tabs flex space-x-4 border-b border-gray-200 mb-4">
  <%= link_to "个人资料", user_user_path(op: "profile"), role: "tab", class: "tab px-4 py-2 text-sm font-medium rounded-t border-b-2 transition-all duration-200 #{params[:op].blank? || params[:op] == 'profile' ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-500 hover:text-blue-500 hover:border-blue-300'}"%>
  <%= link_to "团队", user_user_path(op: "team"), role: "tab", class: "tab px-4 py-2 text-sm font-medium rounded-t border-b-2 transition-all duration-200 #{params[:op] == 'team' ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-500 hover:text-blue-500 hover:border-blue-300'}" %>
  <%= link_to "充值日志", user_user_path(op: "recharge"), role: "tab", class: "tab px-4 py-2 text-sm font-medium rounded-t border-b-2 transition-all duration-200 #{params[:op] == 'recharge' ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-500 hover:text-blue-500 hover:border-blue-300'}" %>
  <%= link_to "提现日志", user_user_path(op: "withdraw"), role: "tab", class: "tab px-4 py-2 text-sm font-medium rounded-t border-b-2 transition-all duration-200 #{params[:op] == 'withdraw' ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-500 hover:text-blue-500 hover:border-blue-300'}" %>
  <%= link_to "游戏记录", user_user_path(op: "game"), role: "tab", class: "tab px-4 py-2 text-sm font-medium rounded-t border-b-2 transition-all duration-200 #{params[:op] == 'game' ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-500 hover:text-blue-500 hover:border-blue-300'}" %>
</div>

<div class="">
  <% case params[:op] %>
    <% when 'team' %>
      <%= render "team" %>
    <% when 'transaction' %>
      <%= render  'transaction' %>
    <% when 'recharge' %>
      <%= render 'recharge' %>
    <% when 'withdraw' %>
      <%= render  'withdraw' %>
    <% when 'game' %>
      <%= render 'game' %>
    <% else %>
      <%= render 'profile' %>
  <% end %>
</div>

