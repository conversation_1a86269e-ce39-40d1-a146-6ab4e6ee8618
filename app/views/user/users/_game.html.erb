<div class="space-y-6 p-4">
  <div class="flex flex-col space-y-4">
    <div class="bg-base-200 p-4 rounded-lg">
      <div class="flex flex-wrap gap-2 mb-4">        
        <% 7.downto(1) do |i| %>
          <% date = i.days.ago %>
          <%= link_to date.strftime("%Y-%m-%d"), 
              user_user_path(op: "game", start_time: date.beginning_of_day, end_time: date.end_of_day),
              class: "btn btn-sm #{params[:start_time].to_s.include?(date.strftime('%Y-%m-%d')) ? 'btn-primary' : 'btn-ghost'}" %>
        <% end %>
        <%= link_to "今天", 
            user_user_path(op: "game", start_time: Time.current.beginning_of_day, end_time: Time.current.end_of_day),
            class: "btn btn-sm #{params[:start_time].to_s.include?(Time.current.strftime('%Y-%m-%d')) ? 'btn-primary' : 'btn-ghost'}" %>
      </div>

      <%= form_tag user_user_path(@user), method: :get, class: "flex flex-col md:flex-row gap-4" do %>
        <%= hidden_field_tag :op, "game" %>
        <div class="form-control flex-1">
          <label class="label">
            <span class="label-text">开始时间</span>
          </label>
          <%= datetime_local_field_tag :start_time, @form_start_time, class: "input input-bordered w-full" %>
        </div>
        
        <div class="form-control flex-1">
          <label class="label">
            <span class="label-text">结束时间</span>
          </label>
          <%= datetime_local_field_tag :end_time, @form_end_time, class: "input input-bordered w-full" %>
        </div>
        
        <div class="form-control flex items-end">
          <%= submit_tag "查询", class: "btn btn-primary" %>
        </div>
      <% end %>
    </div>

    <% if @consumed_capacity.present? %>
      <div class="text-sm text-gray-600">
        消耗的 RCU: <%= @consumed_capacity.capacity_units %>
      </div>
    <% end %>
  </div>

  <div class="overflow-x-auto bg-base-100 rounded-lg shadow">
    <table class="table table-zebra w-full">
      <thead>
        <tr>
          <th>游戏编号</th>
          <th>回合ID</th>
          <th>下注金额</th>
          <th>赢取金额</th>
          <th>实际赢取</th>
          <th>下注前余额</th>
          <th>下注后余额</th>
          <th>时间</th>
        </tr>
      </thead>
      <tbody>
        <% if @game_records.present? %>
          <% @game_records.each do |record| %>
            <tr>
              <td><%= record["game_number"] %></td>
              <td><%= record["round_id"] %></td>
              <td><%= record["bet"] %></td>
              <td><%= record["win"] %></td>
              <td><%= record["real_win"] %></td>
              <td><%= record["before_money"] %></td>
              <td><%= record["after_money"] %></td>
              <td><%= Time.at(record["actionTimeEpoch"]).strftime("%Y-%m-%d %H:%M:%S") %></td>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="8" class="text-center">暂无记录</td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <% if @next_page_params.present? %>
  <%= link_to "下一页", user_user_path(@user, op: "game", **@next_page_params), class: "btn btn-primary" %>

<% else %>
  <p>没有更多数据</p>
<% end %>
</div>
