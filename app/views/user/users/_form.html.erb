<div class="flex items-center mb-6">
  <%= link_to user_users_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-2xl font-bold"><%= @user.new_record? ? "添加会员" : "编辑会员" %></h1>
</div>

<%= form_with(model: [:user, @user], class: "space-y-6", data: { turbo: false }) do |form| %>
  <% if @user.errors.any? %>
    <div class="bg-red-50 text-red-500 p-4 rounded-lg">
      <h2 class="font-bold"><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>
      <ul class="list-disc list-inside">
        <% @user.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="space-y-4">
   <div>
      <%= form.label :id, "ID *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :id, class: "input", readonly: !@user.new_record? %>
    </div>
    <div>
      <%= form.label :name, "用户名 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :name, class: "input", readonly: !@user.new_record? %>
    </div>
    <div>
      <%= form.label :email_address, "邮箱", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :email_address, class: "input", readonly: !@user.new_record?  %>
    </div>
     <div>
      <%= form.label :mobile, "手机号 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :mobile, class: "input", readonly: !@user.new_record?  %>
    </div>
    <div>
      <%= form.label :remark, "备注", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :remark, class: "input" %>
    </div>
    <div>
      <%= form.label :status, "状态 *", class: "block text-sm font-medium text-gray-700" %>
      <% User.statuses.each do |status, value| %>
          <label class="inline-flex items-center select-none">
            <%= form.radio_button :status, status, class: "radio h-4 w-4 text-indigo-600" %>
            <span class="ml-2"><%= I18n.t("common.status.#{status}") %></span>
          </label>
        <% end %>
    </div>
    <div>
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", user_users_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>