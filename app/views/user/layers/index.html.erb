<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">会员分层数据</h1>
  </div>

  <div class="bg-white shadow rounded-lg p-4">
   
    <%= form_with url: user_layers_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.select :search, User.layers.map { |k, v| [I18n.t("activerecord.attributes.user.layers.#{k}"), v] }, { include_blank: "全部", selected: params[:search] }, class: "input" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录时间</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @users.each do |user| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= user.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= user.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= user.mobile %></div>
            </td>
             <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= format_time(user.created_at) %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= format_time(user.last_login_at) %></div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <%== pagy_nav(@pagy) %>
  </div>
</div>