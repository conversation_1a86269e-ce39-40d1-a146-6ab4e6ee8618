<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="max-w-md mx-auto">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">修改密码</h1>

    <% if @user.errors.any? %>
      <div class="rounded-md bg-red-50 p-4 mb-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              <%= pluralize(@user.errors.count, "error") %> prohibited this password from being changed:
            </h3>
            <div class="mt-2 text-sm text-red-700">
              <ul class="list-disc pl-5 space-y-1">
                <% @user.errors.full_messages.each do |msg| %>
                  <li><%= msg %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <%= form_with model: @user, url: update_password_passwords_path, class: "space-y-6" do |f| %>
      <div>
        <%= f.label :current_password, "当前密码", class: "block text-sm font-medium text-gray-700" %>
        <%= f.password_field :current_password, class: "input" %>
      </div>

      <div>
        <%= f.label :password, "新密码", class: "block text-sm font-medium text-gray-700" %>
        <%= f.password_field :password, class: "input" %>
      </div>

      <div>
        <%= f.label :password_confirmation, "确认新密码", class: "block text-sm font-medium text-gray-700" %>
        <%= f.password_field :password_confirmation, class: "input" %>
      </div>

      <div>
        <%= f.submit "更新密码", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
    <% end %>
  </div>
</div> 