<div class="mx-auto max-w-7xl py-10 px-4 sm:px-6 lg:px-8">
  <div class="bg-white shadow rounded-lg max-w-md mx-auto p-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">Update your password</h1>

    <%= form_with url: password_path(params[:token]), method: :put, class: "space-y-6" do |form| %>
      <div>
        <%= form.label :password, "New password", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= form.password_field :password, required: true, autocomplete: "new-password", placeholder: "Enter new password", maxlength: 72, class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
        </div>
      </div>

      <div>
        <%= form.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= form.password_field :password_confirmation, required: true, autocomplete: "new-password", placeholder: "Repeat new password", maxlength: 72, class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
        </div>
      </div>

      <div>
        <%= form.submit "Save changes", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
    <% end %>
  </div>
</div>
