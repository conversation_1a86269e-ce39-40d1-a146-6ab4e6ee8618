<div class="fixed top-0 right-0 p-4">
  <%= link_to "代理后台", new_agent_portal_session_path, class: "btn btn-ghost btn-primary text-white" %>
</div>
<div class="w-full max-w-md">
  <div class="bg-white/90 backdrop-blur-sm p-8 rounded-xl shadow-lg">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900">
        渠道登录
      </h2>
      <p class="mt-2 text-sm text-gray-600">
        请输入您的账号信息
      </p>
    </div>

    <%= form_with url: channel_portal_session_path, class: "space-y-6", data: { turbo: false } do |form| %>
      <div class="space-y-4">
        <%= form.label :email_address, class: "block text-sm font-medium text-gray-700" do %>
          <span class="block mb-1">帐号或邮箱</span>
          <%= form.text_field :email_address, 
                        required: true, 
                        autofocus: true,
                        autocomplete: "username", 
                        placeholder: "请输入帐号或邮箱", 
                        value: params[:email_address],
                        class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white/80" %>
        <% end %>
      
        <%= form.label :password, class: "block text-sm font-medium text-gray-700" do %>
          <span class="block mb-1">密码</span>
          <%= form.password_field :password, 
              required: true, 
              autocomplete: "current-password", 
              placeholder: "请输入密码", 
              class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white/80" %>
        <% end %>

        <%= form.label :otp, class: "block text-sm font-medium text-gray-700" do %>
          <span class="block mb-1">验证码</span>
          <%= form.text_field :otp, 
            autocomplete: "off", 
            placeholder: "请输入验证码",
            class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white/80" %>
        <% end %>
      </div>

      <div class="flex items-center justify-end hidden">
        <div class="text-sm">
          <%= link_to "忘记密码？", new_password_path, class: "font-medium text-indigo-600 hover:text-indigo-500" %>
        </div>
      </div>

      <div>
        <%= form.submit "登录", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" %>
      </div>

      <% if Rails.env.development? %>
        <div class="mt-4 text-center text-sm text-gray-500">
          开发环境中默认帐号 admin / password123
        </div>
      <% end %>
    <% end %>
  </div>
</div>
