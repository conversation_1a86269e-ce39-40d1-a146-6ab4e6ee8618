<%= render "campaign/agents/tab" %>

<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl">推广类型管理</h1>
    <%= link_to "添加推广类型", new_campaign_promotion_type_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="overflow-x-auto">
    <table class="table table-zebra w-full">
      <thead>
        <tr>
          <th>名称</th>
          <th>代码</th>
          <th>排序</th>
          <th>状态</th>
          <th class="text-center">渠道数量</th>
          <th>创建人</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <% @promotion_types.each do |promotion_type| %>
          <tr>
            <td><%= promotion_type.name %></td>
            <td><%= promotion_type.code %></td>
            <td><%= promotion_type.sort %></td>
            <td>
              <div class="badge badge-soft <%= promotion_type.enabled? ? 'badge-success' : 'badge-error' %>">
                <%= promotion_type.status_text %>
              </div>
            </td>
            <td class="text-center"><%= promotion_type.channels.count %></td>
            <td><%= promotion_type.created_by %></td>
            <td>
              <div class="flex gap-2">
                <% if promotion_type.enabled? %>
                  <%= link_to "编辑", edit_campaign_promotion_type_path(promotion_type), class: "btn btn-outline btn-sm" %>
                  <%= button_to "禁用", toggle_status_campaign_promotion_type_path(promotion_type), method: :put, class: "btn btn-outline btn-error btn-sm", form: { data: { turbo_confirm: "确定要禁用<#{promotion_type.name}>吗？" } } %>
                <% else %>
                  <%= button_to "启用", toggle_status_campaign_promotion_type_path(promotion_type), method: :put, class: "btn btn-outline btn-success btn-sm", form: { data: { turbo_confirm: "确定要启用<#{promotion_type.name}>吗？" } } %>
                <% end %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
