<%= form_with(model: [:campaign, promotion_type], class: "space-y-4") do |f| %>
  <%= render "shared/error_messages", object: promotion_type %>

  <div class="form-control">
    <%= f.label :name, "名称 *", class: "label" %>
    <%= f.text_field :name, class: "input input-bordered w-full" %>
    <%= help_text "名称用于标识推广类型，不能重复" %>
  </div>

  <div class="form-control">
    <%= f.label :code, "代码 *", class: "label" %>
    <%= f.text_field :code, class: "input input-bordered w-full" %>
    <%= help_text "代码用于标识推广类型，不能重复" %>
  </div>

  <div class="form-control">
    <%= f.label :sort, "排序 *", class: "label" %>
    <%= f.number_field :sort, class: "input input-bordered w-full" %>
    <%= help_text "排序越大越靠前" %>
  </div>

  <div class="flex justify-end gap-2">
    <%= link_to "取消", campaign_promotion_types_path, class: "btn" %>
    <%= f.submit class: "btn btn-primary" %>
  </div>
<% end %>
