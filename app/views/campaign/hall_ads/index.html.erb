<%= render "campaign/hall_ads/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: campaign_hall_ads_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "广告名称" %>
      <%= f.select :ad_position, HallAd.ad_positions.map { |k, v| [I18n.t("activerecord.attributes.hall_ad.ad_positions.#{k}"), v] }, { include_blank: "全部位置", selected: params[:ad_position] }, class: "input" %>
      <%= f.select :redirect_type, HallAd.redirect_types.map { |k, v| [I18n.t("activerecord.attributes.hall_ad.redirect_types.#{k}"), v] }, { include_blank: "全部跳转类型", selected: params[:redirect_type] }, class: "input" %>
      <%= f.select :status, HallAd.statuses.map { |k, v| [I18n.t("activerecord.attributes.hall_ad.statuses.#{k}"), v] }, { include_blank: "全部状态", selected: params[:status] }, class: "input" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to "添加广告", new_campaign_hall_ad_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">展示位置</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">广告名称</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">广告图片</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">排序</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">跳转类型</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">跳转参数</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作日期</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @hall_ads.each do |hall_ad| %>
          <tr>
            <td class="px-6 py-4">
              <%= hall_ad.id %>
            </td>
            <td class="px-6 py-4">
              <%= hall_ad.ad_position_text %>
            </td>
            <td class="px-6 py-2">
              <%= hall_ad.name %>
            </td>
            <td class="px-6 py-4">
              <% if hall_ad.media.present? %>
                <img src="<%= hall_ad.media.url %>" class="h-10 w-10 object-cover rounded" alt="<%= hall_ad.name %>">
              <% end %>
            </td>
            <td class="px-6 py-4">
              <%= hall_ad.order %>
            </td>
            
            <td class="px-6 py-4">
              <%= hall_ad.redirect_type_text %>
            </td>
            <td class="px-6 py-4">
              <%= hall_ad.redirect_link %>
            </td>
            <td class="px-6 py-4">
              <span class="<%= hall_ad.onlined? ? 'text-green-600' : 'text-red-600' %>">
                <%= hall_ad.status_text %>
              </span>
            </td>
            <td class="px-6 py-4">
              <%= hall_ad.updated_at.strftime("%Y-%m-%d %H:%M") %>
            </td>
            <td class="px-6 py-4">
              <%= hall_ad.created_by %>
            </td>
            <td class="px-6 py-4 text-sm leading-5">
              <div class="flex justify-center gap-2">
                <%= link_to '编辑', edit_campaign_hall_ad_path(hall_ad), class: 'btn btn-outline' %>
                <%= button_to '删除', campaign_hall_ad_path(hall_ad), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除【#{hall_ad.name}】这个广告吗？" } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%== pagy_nav(@pagy) %>
  </div>
</div>

