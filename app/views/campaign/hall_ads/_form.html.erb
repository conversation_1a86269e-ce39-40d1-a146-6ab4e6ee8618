<%= form_with(model: [:campaign, @hall_ad], class: "space-y-6") do |f| %>
  <%= render "shared/error_messages", object: @hall_ad %>

  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
    <div>
      <%= f.label :name, "广告名称", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :name, class: "input" %>
    </div>

    <div>
      <%= f.label :media, "广告图片", class: "block text-sm font-medium text-gray-700" %>
      <%= f.file_field :media, class: "file-input" %>
    </div>

    <div>
      <%= f.label :media_type, "媒体类型", class: "block text-sm font-medium text-gray-700" %>
      <%= f.select :media_type, [["图片", "image"], ["视频", "video"]], {}, class: "select" %>
    </div>

    <div>
      <%= f.label :ad_position, "广告位置", class: "block text-sm font-medium text-gray-700" %>
      <%= f.select :ad_position, HallAd.ad_positions.map { |k, v| [I18n.t("activerecord.attributes.hall_ad.ad_positions.#{k}"), k] }, {}, class: "select" %>
    </div>

    <div>
      <%= f.label :redirect_type, "跳转类型", class: "block text-sm font-medium text-gray-700" %>
      <%= f.select :redirect_type, HallAd.redirect_types.map { |k, v| [I18n.t("activerecord.attributes.hall_ad.redirect_types.#{k}"), k] }, {}, class: "select" %>
    </div>

    <div>
      <%= f.label :redirect_link, "跳转链接", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :redirect_link, class: "input" %>
    </div>

    <div>
      <%= f.label :status, "状态", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-2 space-x-4">
        <% HallAd.statuses.each do |status, value| %>
          <label class="inline-flex items-center select-none">
            <%= f.radio_button :status, status, class: "radio h-4 w-4 text-indigo-600" %>
            <span class="ml-2"><%= I18n.t("activerecord.attributes.hall_ad.statuses.#{status}") %></span>
          </label>
        <% end %>
      </div>
    </div>

    <div>
      <%= f.label :order, "排序", class: "block text-sm font-medium text-gray-700" %>
      <%= f.number_field :order, class: "input" %>
    </div>

    <div>
      <%= f.label :expired_at, "过期时间", class: "block text-sm font-medium text-gray-700" %>
      <%= f.datetime_field :expired_at, class: "input" %>
    </div>
  </div>

  <div class="flex justify-end gap-3">
    <%= link_to "取消", campaign_hall_ads_path, class: "btn btn-outline" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %>