<%= form_with model: [:campaign, @popup_ad], class: "space-y-6" do |f| %>
  <%= render "shared/error_messages", object: @popup_ad %>
  <div class="grid grid-cols-2 gap-6">
    <div class="form-group">
      <%= f.label :name, "广告名称" %>
      <%= f.text_field :name, class: "input" %>
    </div>

    <div class="form-group">
      <%= f.label :media, "广告图片" %>
      <%= f.file_field :media, class: "file-input" %>
      <% if @popup_ad.media.present? %>
        <div class="mt-2">
          <img src="<%= @popup_ad.media.url %>" class="h-20 w-20 object-cover rounded" alt="<%= @popup_ad.name %>">
        </div>
      <% end %>
    </div>

    <div class="form-group">
      <%= f.label :display_occasion, "弹窗时机" %>
      <%= f.select :display_occasion, PopupAd.display_occasions.map { |k, v| [I18n.t("activerecord.attributes.popup_ad.display_occasions.#{k}"), v] }, {}, class: "input" %>
    </div>

    <div class="form-group">
      <%= f.label :visibility, "可见性" %>
      <%= f.select :visibility, PopupAd.visibilities.map { |k, v| [I18n.t("activerecord.attributes.popup_ad.visibilities.#{k}"), v] }, {}, class: "input" %>
    </div>

    <div class="form-group">
      <%= f.label :order, "排序" %>
      <%= f.number_field :order, class: "input" %>
    </div>

    <div class="form-group">
      <%= f.label :redirect_type, "跳转类型" %>
      <%= f.select :redirect_type, PopupAd.redirect_types.map { |k, v| [I18n.t("activerecord.attributes.popup_ad.redirect_types.#{k}"), v] }, {}, class: "input" %>
    </div>

    <div class="form-group">
      <%= f.label :redirect_link, "跳转参数" %>
      <%= f.text_field :redirect_link, class: "input" %>
    </div>

    <div>
      <%= f.label :status, "状态", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-2 space-x-4">
        <% HallAd.statuses.each do |status, value| %>
          <label class="inline-flex items-center select-none">
            <%= f.radio_button :status, status, class: "radio h-4 w-4 text-indigo-600" %>
            <span class="ml-2"><%= I18n.t("activerecord.attributes.hall_ad.statuses.#{status}") %></span>
          </label>
        <% end %>
      </div>
    </div>

    <div class="form-group">
      <%= f.label :expired_at, "过期时间" %>
      <%= f.datetime_field :expired_at, class: "input" %>
    </div>
  </div>

  <div class="flex justify-end gap-2">
    <%= link_to "取消", campaign_popup_ads_path, class: "btn btn-outline" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 