<%= render "campaign/agents/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center">
    <h1 class="text-xl">代理列表</h1>
    <%= link_to "添加代理", new_campaign_agent_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="bg-white shadow rounded-lg p-4">
    <%= form_tag campaign_agents_path, method: :get, class: "flex flex-col sm:flex-row gap-4 items-end" do %>
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
        <%= text_field_tag :search, params[:search], class: "input input-bordered w-full sm:w-64", placeholder: "输入代理商帐号 或者 昵称 搜索" %>
      </div>
      <%= select_tag :status, options_for_select([["正常", "actived"], ["禁用", "inactived"]], params[:status]), class: "input input-bordered w-full sm:w-64", include_blank: "全部状态" %>
      <div class="flex gap-2 w-full sm:w-auto">
        <%= submit_tag "搜索", class: "btn btn-primary flex-1 py-1 sm:flex-none" %>
        <%= link_to "重置", campaign_agents_path, class: "btn btn-outline flex-1 py-1 sm:flex-none" %>
      </div>
    <% end %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代理账号</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">昵称</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">谷歌密钥</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">登录次数</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录时间</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">添加时间</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">帐号状态</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @agents.each do |agent| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= agent.id %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 hover:text-blue-500 hover:underline"><%= link_to agent.name, campaign_agent_path(agent) %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= agent.nickname %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= agent.totp_secret&.truncate(10) %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= agent.login_count %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= format_time(agent.last_login_at) %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap truncate" title="<%= agent.remark %>">
              <%= agent.remark.to_s.truncate(20) %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= agent.created_at %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= agent.created_by %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%= status_badge(agent) %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <%= button_to "重置密码", reset_password_campaign_agent_path(agent), method: :post, class: "btn btn-outline", form: { data: { turbo_confirm: "你确定要重置 ##{agent.id}:#{agent.name} 这个代理商帐号的密码吗？" } } %>
                <%= link_to "编辑", edit_campaign_agent_path(agent), class: "btn btn-outline" %>
                <%= button_to "删除", campaign_agent_path(agent), method: :delete, class: "btn btn-outline btn-error", form: { data: { turbo_confirm: "你确定要删除 ##{agent.id}:#{agent.name} 这个代理商帐号吗？" } } %>
              
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <%== pagy_nav(@pagy) %>
  </div>
</div>