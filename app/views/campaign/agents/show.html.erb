<%= render "campaign/agents/tab" %>
<div class="p-4 bg-white rounded-lg shadow">
  <h2 class="text-xl flex items-center mb-6">
  <%= link_to campaign_agents_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  代理商信息</h2>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <span class="font-semibold">ID：</span>
      <span><%= @agent.id %></span>
    </div>
    <div>
      <span class="font-semibold">账号：</span>
      <span><%= @agent.name %></span>
    </div>
    <div>
      <span class="font-semibold">昵称：</span>
      <span><%= @agent.nickname %></span>
    </div>
    <div>
      <span class="font-semibold">邮箱：</span>
      <span><%= @agent.email_address %></span>
    </div>
    <div>
      <span class="font-semibold">手机号：</span>
      <span><%= @agent.mobile %></span>
    </div>
    <div>
      <span class="font-semibold">状态：</span>
      <span><%= I18n.t("activerecord.attributes.agent.statuses.#{@agent.status}") %></span>
    </div>

    <div>
      <span class="font-semibold">登录次数：</span>
      <span><%= @agent.login_count %></span>
    </div>
    <div>
      <span class="font-semibold">最后登录时间：</span>
      <span><%= @agent.last_login_at&.strftime("%Y-%m-%d %H:%M:%S") %></span>
    </div>
    <div>
      <span class="font-semibold">备注：</span>
      <span><%= @agent.remark %></span>
    </div>
    <div>
      <span class="font-semibold">添加人：</span>
      <span><%= @agent.created_by %></span>
    </div>
    <div>
      <span class="font-semibold">添加时间：</span>
      <span><%= @agent.created_at&.strftime("%Y-%m-%d %H:%M:%S") %></span>
    </div>
    <div>
      <span class="font-semibold">登录密钥：</span>
      <span><%= @agent.totp_secret %></span>
    </div>
  </div>
  <div class="mt-8 flex gap-4">
    <%= link_to "返回列表", campaign_agents_path, class: "btn" %>
    <%= link_to "编辑", edit_campaign_agent_path(@agent), class: "btn btn-primary" %>
  </div>
</div> 