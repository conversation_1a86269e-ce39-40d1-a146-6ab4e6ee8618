<div class="flex items-center mb-6 p-4">
  <h1 class="text-xl flex items-center gap-2">
    <%= link_to_back campaign_agents_path %>
    <%= agent.new_record? ? "添加代理" : "编辑代理" %>
  </h1>
</div>

<%= form_with(model: [:campaign, agent], class: "max-w-7xl mx-auto p-6 bg-white rounded-lg shadow space-y-6") do |f| %>

  <%= render "shared/error_messages", object: agent %>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <%= f.label :name, "代理商账号 *", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :name, class: "input input-bordered w-full", disabled: !agent.new_record? %>
      <span class="text-gray-500 text-xs">
        代理商账号只能包含字母、数字、下划线、中划线、点, 长度为2-24个字符。
      </span>
    </div>
    <div>
      <%= f.label :nickname, "昵称", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :nickname, class: "input input-bordered w-full" %>
      <span class="text-gray-500 text-xs">
        昵称长度为2-24个字符。
      </span>
    </div>
    <% if agent.new_record? %>
    <div>
      <%= f.label :password, "登录密码", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :password, class: "input input-bordered w-full" %>
      <span class="text-gray-500 text-xs">
        密码长度为8-32个字符，留空的情况会自动生成一个默认的固定密码 <%= Agent::DEFAULT_PASSWORD %>
      </span>
    </div>
    <% end %>
    <div>
      <%= f.label :remark, "备注", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_area :remark, class: "textarea textarea-bordered w-full" %>
      <span class="text-gray-500 text-xs">
        备注信息只用于系统内部管理，不会显示代理商个人信息中。最多50个字符。
      </span>
    </div>
  </div>
  <div class="flex justify-end">
    <%= f.submit class: "btn btn-primary" %>
  </div>
<% end %>
