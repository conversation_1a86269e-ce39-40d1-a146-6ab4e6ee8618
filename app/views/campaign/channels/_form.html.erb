<%= form_with model: [:campaign, @channel], class: "space-y-6" do |f| %>

  <%= render "shared/error_messages", object: @channel %>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <% # show agent select list %>
    <div class="form-control">
      <%= f.label :agent_id, "代理商", class: "label" %>
      <%= f.select :agent_id,
          options_from_collection_for_select(Agent.actived, :id, :name, @channel.agent_id),
          { include_blank: "请选择代理商" },
          { class: "select select-bordered w-full", required: true } %>
    </div>

    <div class="form-control">
      <%= f.label :promotion_type_id, "推广类型", class: "label" %>
      <%= f.select :promotion_type_id,
          options_from_collection_for_select(PromotionType.enabled, :id, :name, @channel.promotion_type_id),
          { include_blank: "请选择推广类型" },
          { class: "select select-bordered w-full", required: true } %>
    </div>

    <div class="form-control">
      <%= f.label :name, "渠道名称", class: "label" %>
      <%= f.text_field :name, class: "input input-bordered w-full", required: true %>
    </div>

    <div class="form-control">
      <%= f.label :nickname, "昵称", class: "label" %>
      <%= f.text_field :nickname, class: "input input-bordered w-full" %>
    </div>

    <% if @channel.new_record? %>
    <div>
      <%= f.label :password, "登录密码", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :password, class: "input input-bordered w-full" %>
      <span class="text-gray-500 text-xs">
        密码长度为8-32个字符，留空的情况会自动生成一个默认的固定密码 <%= Channel::DEFAULT_PASSWORD %>
      </span>
    </div>
    <% end %>

    <div class="form-control">
      <%= f.label :status, "状态", class: "label" %>
      <%= f.select :status, 
          options_for_select([["正常", "actived"], ["禁用", "inactived"]], @channel.status),
          {},
          class: "select select-bordered w-full" %>
    </div>

    <div class="form-control md:col-span-2">
      <%= f.label :remark, "备注", class: "label" %>
      <%= f.text_area :remark, class: "textarea textarea-bordered w-full", rows: 3 %>
    </div>
  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "取消", campaign_channels_path, class: "btn btn-outline" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 