<%= render "campaign/agents/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl">渠道管理</h1>
    <%= link_to "添加渠道", new_campaign_channel_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="bg-base-100 shadow-xl rounded-lg p-6">
    <div class="flex justify-between items-center mb-4">
      <div class="flex gap-4">
        <%= form_with url: campaign_channels_path, method: :get, class: "flex gap-4" do |f| %>
          <div class="form-control">
            <%= f.text_field :search, placeholder: "搜索渠道名称", class: "input input-bordered w-full", value: params[:search] %>
          </div>

          <div class="form-control">
            <%= f.select :agent_id,
                options_from_collection_for_select(Agent.actived, :id, :name, params[:agent_id]),
                { prompt: "选择代理商" },
                class: "select select-bordered w-full" %>
          </div>

          <div class="form-control">
            <%= f.select :status, 
                options_for_select([["全部", ""], ["活跃", "actived"], ["禁用", "inactived"]], params[:status]),
                {},
                class: "select select-bordered w-full" %>
          </div>
          <%= f.submit "搜索", class: "btn btn-primary" %>
        <% end %>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="table w-full">
        <thead>
          <tr>
            <th>ID</th>
            <th>代理</th>
            <th>名称</th>
            <th>昵称</th>
            <th>邮箱</th>
            <th>手机</th>
            <th>状态</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <% @channels.each do |channel| %>
            <tr>
              <td><%= channel.id %></td>
              <td><%= channel.agent&.name %></td>
              <td><%= channel.name %></td>
              <td><%= channel.nickname %></td>
              <td><%= channel.email_address %></td>
              <td><%= channel.mobile %></td>
              <td>
                  <span class="badge badge-soft <%= channel.status == 'actived' ? 'badge-success' : 'badge-error' %>">
                    <%= channel.status == 'actived' ? '活跃' : '禁用' %>
                  </span>
              </td>
              <td><%= channel.created_at.strftime("%Y-%m-%d %H:%M") %></td>
              <td>
                <div class="flex gap-2">
                  <%= link_to "查看", campaign_channel_path(channel), class: "btn btn-sm btn-info" %>
                  <%= link_to "编辑", edit_campaign_channel_path(channel), class: "btn btn-sm btn-warning" %>
                  <%= button_to "删除", campaign_channel_path(channel), 
                      method: :delete, 
                      class: "btn btn-sm btn-error btn-outline",
                      data: { turbo_confirm: "确定要删除这个渠道吗？" } %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <div class="mt-4">
      <%== pagy_nav(@pagy) %>
    </div>
  </div>
</div>