<%= render "game/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: game_integrators_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "集成商名称" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to '添加集成商', new_game_integrator_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">名称</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">编码</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">排序</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">开启状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white">
        <% @integrators.each do |integrator| %>
          <tr>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= integrator.id %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= integrator.name %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= integrator.code %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= integrator.order %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <span class="<%= integrator.onlined? ? 'text-green-600' : 'text-red-600' %>">
                <%= integrator.status_text %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= integrator.created_by %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200 text-sm leading-5">
            <div class="flex justify-center gap-2">
              <%= link_to '编辑', edit_game_integrator_path(integrator), class: 'btn btn-outline' %>
              <%= button_to '删除', game_integrator_path(integrator), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除【#{integrator.name}】这个集成商吗？" } %>
            </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
