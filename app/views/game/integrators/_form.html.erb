<div class="flex items-center mb-6">
  <%= link_to game_integrators_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-xl"><%= @integrator.new_record? ? "添加集成商" : "编辑集成商" %></h1>
</div>

<%= form_with(model: [:game, @integrator], data: { turbo: false }) do |f| %>
  <%= render "shared/error_messages", object: @integrator %>

  <div class="mb-4">
    <%= f.label :name, "集成商名称", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.text_field :name, class: "input", placeholder: "请输入集成商名称", required: true %>
  </div>

  <div class="mb-4">
    <%= f.label :code, "集成商编码", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.text_field :code, class: "input", placeholder: "请输入集成商编码", required: true %>
    <small class="text-xs text-gray-500">以小写字母开头，且只能包含字母、数字和下划线</span>
  </div>

  <div class="mb-4">
    <%= f.label :order, "排序", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.number_field :order, class: "input", placeholder: "请输入排序", required: true %>
    <small class="text-gray-500">排序越大越靠前</small>
  </div>

  <div class="mb-6">
    <%= f.label :status, "状态", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.select :status, Integrator.statuses.keys.map { |s| [I18n.t("activerecord.attributes.integrator.statuses.#{s}"), s] }, {}, class: "input" %>
  </div>

  <div class="flex items-center justify-between">
    <%= f.submit @integrator.new_record? ? "保存" : "保存", class: "btn btn-primary" %>
  </div>
<% end %> 