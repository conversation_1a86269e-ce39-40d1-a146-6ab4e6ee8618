<%= render "game/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: game_games_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "游戏名称" %>
      <%= f.select :game_platform_id, GamePlatform.all.map { |game_platform| [game_platform.name, game_platform.id] }, { include_blank: "全部平台", selected: params[:game_platform_id] }, class: "input" %>
      <%= f.select :integrator_id, Integrator.all.map { |integrator| [integrator.name, integrator.id] }, { include_blank: "全部集成商", selected: params[:integrator_id] }, class: "input" %>
      <%= f.select :game_type_id, GameType.all.map { |game_type| [game_type.name, game_type.id] }, { include_blank: "全部类型", selected: params[:game_type_id] }, class: "input" %>
      <%= f.select :status, [["已下线", "offlined"], ["已上线", "onlined"]], { include_blank: "全部状态", selected: params[:status] }, class: "input" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to '添加游戏', new_game_game_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">游戏名称</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">游戏封面</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">游戏平台</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">集成商</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">游戏类型</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">是否热门</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">是否DEMO</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">屏幕方向</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">排序</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @games.each do |game| %>
          <tr>
            <td class="px-6 py-4">
              <%= game.id %>
            </td>
            <td class="px-6 py-2">
                <%= game.name %>
            </td>
            <td class="px-6 py-4">
              <% if game.cover_url_0.present? %>
                <img src="<%= game.cover_url_0 %>" class="h-10 w-10 object-cover rounded" alt="<%= game.name %>">
                <%= link_to '查看', game.cover_url_0.url, target: '_blank', class: "text-xs text-gray-500" %>
              <% end %>
              <% if game.cover_url_1.present? %>
                <img src="<%= game.cover_url_1 %>" class="h-10 w-10 object-cover rounded" alt="<%= game.name %>">
                <%= link_to '查看', game.cover_url_1.url, target: '_blank', class: "text-xs text-gray-500" %>
              <% end %>
              <% if game.cover_url_2.present? %>
                <img src="<%= game.cover_url_2 %>" class="h-10 w-10 object-cover rounded" alt="<%= game.name %>">
                <%= link_to '查看', game.cover_url_2.url, target: '_blank', class: "text-xs text-gray-500" %>
              <% end %>
            </td>
            <td class="px-6 py-4">
              <%= game.game_platform.name %>
            </td>
            <td class="px-6 py-4">
              <%= game.integrator.name %>
            </td>
            <td class="px-6 py-4">
              <%= game.game_type.name %>
            </td>
            <td class="px-6 py-4">
              <% if game.hot? %>
                <span class="text-green-600">是</span>
              <% else %>
                <span class="text-red-600">否</span>
              <% end %>
            </td>
            <td class="px-6 py-4">
              <% if game.demo? %>
                <span class="text-green-600">是</span>
              <% else %>
                <span class="text-red-600">否</span>
              <% end %>
            </td>
            <td class="px-6 py-4">
              <%= game.screen_direction_text %>
            </td>
            <td class="px-6 py-4">
              <span class="<%= game.onlined? ? 'text-green-600' : 'text-red-600' %>">
                <%= game.status_text %>
              </span>
            </td>
            <td class="px-6 py-4">
              <%= game.order %>
            </td>
            <td class="px-6 py-4">
              <%= game.created_by %>
            </td>
            <td class="px-6 py-4 text-sm leading-5">
              <div class="flex justify-center gap-2">
                <%= link_to '编辑', edit_game_game_path(game), class: 'btn btn-outline' %>
                <%= button_to '删除', game_game_path(game), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除【#{game.name}】这个游戏吗？" } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%== pagy_nav(@pagy) %>
  </div>
</div> 