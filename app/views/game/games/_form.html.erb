<div class="flex items-center mb-6">
  <%= link_to game_games_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-xl"><%= @game.new_record? ? "添加游戏" : "编辑游戏" %></h1>
</div>

<%= form_with(model: [:game, @game], data: { turbo: false }) do |f| %>
  <%= render "shared/error_messages", object: @game %>

  <div class="mb-4">
    <%= f.label :name, "游戏名称", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.text_field :name, class: "input", placeholder: "请输入游戏名称", required: true %>
  </div>

  <div class="mb-4 flex flex-col sm:flex-row items-center gap-4">
    <div class="flex flex-col sm:flex-row items-start sm:items-center w-full sm:w-1/2">
      <%= f.label :integrator_identity, "集成商标识ID", class: "block text-gray-700 text-sm font-bold select-none shrink-0" %>
      <%= f.text_field :integrator_identity, class: "input sm:ml-2", placeholder: "请输入集成商标识" %>
    </div>

    <div class="flex flex-col sm:flex-row items-start sm:items-center w-full sm:w-1/2">
      <%= f.label :integrator_game_id, "集成商游戏ID", class: "block text-gray-700 text-sm font-bold select-none shrink-0" %>
      <%= f.text_field :integrator_game_id, class: "input sm:ml-2", placeholder: "请输入集成商游戏ID" %>
    </div>

  </div>

  <div class="mb-4">
    <%= f.label :cover_url_0, "游戏封面(竖长方形)", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.file_field :cover_url_0, class: "input" %>
    <span class="text-xs text-gray-500">请上传竖长方形的游戏封面</span>

    <% if @game.cover_url_0.present? %>
      <img src="<%= @game.cover_url_0 %>" class="h-10 w-10 object-cover rounded" alt="<%= @game.name %>">
    <% end %>
  </div>
  <div class="mb-4">
    <%= f.label :cover_url_1, "游戏封面(正方形)", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.file_field :cover_url_1, class: "input" %>
    <span class="text-xs text-gray-500">请上传正方形的游戏封面</span>

    <% if @game.cover_url_1.present? %>
      <img src="<%= @game.cover_url_1 %>" class="h-10 w-10 object-cover rounded" alt="<%= @game.name %>">
    <% end %>
  </div>
  <div class="mb-4">
    <%= f.label :cover_url_2, "游戏封面(横长方形)", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.file_field :cover_url_2, class: "input" %>
    <span class="text-xs text-gray-500">请上传横长方形的游戏封面</span>

    <% if @game.cover_url_2.present? %>
      <img src="<%= @game.cover_url_2 %>" class="h-10 w-10 object-cover rounded" alt="<%= @game.name %>">
    <% end %>
  </div>

  <div class="mb-4 flex flex-col sm:flex-row items-center gap-4">

    <div class="mb-4">
      <%= f.label :game_platform_id, "游戏平台", class: "block text-gray-700 text-sm font-bold mb-2" %>
      <%= f.collection_select :game_platform_id, GamePlatform.all, :id, :name, { prompt: "请选择游戏平台" }, { class: "input", required: true } %>
    </div>

    <div class="mb-4">
      <%= f.label :integrator_id, "集成商", class: "block text-gray-700 text-sm font-bold mb-2" %>
      <%= f.collection_select :integrator_id, Integrator.all, :id, :name, { prompt: "请选择集成商" }, { class: "input", required: true } %>
    </div>

    <div class="mb-4">
      <%= f.label :game_type_id, "游戏类型", class: "block text-gray-700 text-sm font-bold mb-2" %>
      <%= f.collection_select :game_type_id, GameType.all, :id, :name, { prompt: "请选择游戏类型" }, { class: "input", required: true } %>
    </div>

  </div>

  <div class="mb-6">
    <%= f.label :status, "状态", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.select :status, Game.statuses.keys.map { |s| [I18n.t("activerecord.attributes.game.statuses.#{s}"), s] }, {}, class: "input" %>
  </div>

  <div class="mb-6">
    <%= f.label :screen_direction, "屏幕方向", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.select :screen_direction, Game.screen_directions.keys.map { |s| [I18n.t("activerecord.attributes.game.screen_directions.#{s}"), s] }, {}, class: "input" %>
  </div>

  <div class="mb-6 flex items-center gap-4">
    <div class="flex items-center">
      <%= f.label :hot, "是否为热门游戏", class: "block text-gray-700 text-sm font-bold select-none" %>
      <%= f.check_box :hot, class: "ml-2" %>
    </div>
    <div class="flex items-center">
      <%= f.label :demo, "是否为demo游戏", class: "block text-gray-700 text-sm font-bold select-none" %>
      <%= f.check_box :demo, class: "ml-2" %>
    </div>
    <div class="flex items-center">
      <%= f.label :suggested, "是否为推荐游戏", class: "block text-gray-700 text-sm font-bold select-none" %>
      <%= f.check_box :suggested, class: "ml-2" %>
    </div>
  </div>

  <div class="mb-6">
    <%= f.label :order, "排序", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.number_field :order, class: "input", placeholder: "请输入排序" %>
  </div>
  

  <div class="flex items-center justify-between">
    <%= f.submit @game.new_record? ? "保存" : "保存", class: "btn btn-primary" %>
  </div>
<% end %> 