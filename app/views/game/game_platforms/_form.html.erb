<div class="flex items-center mb-6">
  <%= link_to game_game_platforms_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  <% end %>
  <h1 class="text-xl"><%= @game_platform.new_record? ? "添加游戏平台" : "编辑游戏平台" %></h1>
</div>

<%= form_with(model: [:game, @game_platform], data: { turbo: false }) do |f| %>
  <%= render "shared/error_messages", object: @game_platform %>

  <div class="mb-4">
    <%= f.label :name, "平台名称", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.text_field :name, class: "input", placeholder: "请输入平台名称", required: true %>
  </div>

  <div class="mb-4">
    <%= f.label :cover_url, "平台封面", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.file_field :cover_url, class: "input", placeholder: "请输入平台封面URL" %>
    <small class="text-xs text-gray-500">请上传未选中状态的图片</small>

    <% if @game_platform.cover_url.present? %>
      <img src="<%= @game_platform.cover_url %>" class="h-10 w-10 object-cover rounded" alt="<%= @game_platform.name %>">
    <% end %>
  </div>

  <div class="mb-4">
  <%= f.label :cover_url_selected, "平台封面(选中状态)", class: "block text-gray-700 text-sm font-bold mb-2" %>
  <%= f.file_field :cover_url_selected, class: "input", placeholder: "请输入平台封面URL" %>
  <small class="text-xs text-gray-500">请上传选中状态的图片</small>

  <% if @game_platform.cover_url_selected.present? %>
    <img src="<%= @game_platform.cover_url_selected %>" class="h-10 w-10 object-cover rounded" alt="<%= @game_platform.name %>">
  <% end %>
  </div>

  <div class="mb-4">
    <%= f.label :order, "排序", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.number_field :order, class: "input", placeholder: "请输入排序", required: true %>
    <small class="text-xs text-gray-500">排序越大越靠前</small>
  </div>

  <div class="mb-6">
    <%= f.label :status, "状态", class: "block text-gray-700 text-sm font-bold mb-2" %>
    <%= f.select :status, GamePlatform.statuses.keys.map { |s| [I18n.t("activerecord.attributes.game_platform.statuses.#{s}"), s] }, {}, class: "input" %>
  </div>

  <div class="flex items-center justify-between">
    <%= f.submit @game_platform.new_record? ? "保存" : "保存", class: "btn btn-primary" %>
  </div>
<% end %>
