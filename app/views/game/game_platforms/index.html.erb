<%= render "game/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <%= form_with url: game_game_platforms_path, method: :get, class: "flex items-center gap-2" do |f| %>
      <%= f.text_field :search, value: params[:search].to_s.strip, class: "input", placeholder: "游戏平台名称" %>
      <%= f.submit "搜索", class: "btn btn-primary" %>
    <% end %>
    <%= link_to '添加游戏平台', new_game_game_platform_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">名称</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">封面</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">开启状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">排序</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white">
        <% @game_platforms.each do |game_platform| %>
          <tr>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_platform.id %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_platform.name %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <div class="flex items-center gap-2">
              <% if game_platform.cover_url.present? %>
                <div class="flex flex-col items-center">
                  <img src="<%= game_platform.cover_url %>" class="h-10 w-10 object-cover rounded" alt="<%= game_platform.name %>">
                  <%= link_to '查看', game_platform.cover_url.url, target: '_blank', class: "text-xs text-gray-500" %>
                </div>
              <% end %>
              <% if game_platform.cover_url_selected.present? %>
                <div class="flex flex-col items-center">
                  <img src="<%= game_platform.cover_url_selected %>" class="h-10 w-10 object-cover rounded" alt="<%= game_platform.name %>">
                  <%= link_to '查看', game_platform.cover_url_selected.url, target: '_blank', class: "text-xs text-gray-500" %>
                </div>
              <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <span class="<%= game_platform.onlined? ? 'text-green-600' : 'text-red-600' %>">
                <%= game_platform.status_text %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_platform.created_by %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_platform.order %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200 text-sm leading-5">
              <div class="flex justify-center gap-2">
                <%= link_to '编辑', edit_game_game_platform_path(game_platform), class: 'btn btn-outline' %>
                <%= button_to '删除', game_game_platform_path(game_platform), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除【#{game_platform.name}】这个游戏平台吗？" } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
