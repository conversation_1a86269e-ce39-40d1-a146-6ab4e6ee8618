<%= render "game/tab" %>
<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl">游戏类型管理</h1>
    <%= link_to '添加游戏类型', new_game_game_type_path, class: 'btn btn-primary btn-sm' %>
  </div>

  <div class="bg-white shadow rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">名称</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">开启状态</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">添加人</th>
          <th class="px-6 py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">排序</th>
          <th class="px-6 py-3 border-b border-gray-200 text-center text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody class="bg-white">
        <% @game_types.each do |game_type| %>
          <tr>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_type.id %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_type.name %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <span class="<%= game_type.onlined? ? 'text-green-600' : 'text-red-600' %>">
                <%= game_type.status_text %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_type.created_by %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200">
              <%= game_type.order %>
            </td>
            <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-200 text-sm leading-5">
            <div class="flex justify-center gap-2">
              <%= link_to '编辑', edit_game_game_type_path(game_type), class: 'btn btn-outline' %>
              <%= button_to '删除', game_game_type_path(game_type), method: :delete, class: 'btn btn-outline btn-error', data: { turbo_confirm: "确定要删除【#{game_type.name}】这个游戏类型吗？" } %>
            </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
