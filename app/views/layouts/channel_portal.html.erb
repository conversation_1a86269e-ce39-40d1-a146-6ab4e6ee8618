<!DOCTYPE html>
<html data-theme="corporate">
  <head>
    <title><%= content_for(:title) || "Agent Admin" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag :app %>
    <%= javascript_importmap_tags %>
    <style>
      .scrollbar-hide {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Chrome, Safari and Opera */
      }
    </style>
  </head>

  <body class="bg-gray-100">
    <div id="flash_messages" class="flash-container">
      <%= render "shared/flash_messages" %>
    </div>
    <div class="h-screen flex overflow-hidden">
      <!-- Sidebar -->
      <div class="w-32 md:w-52 flex flex-col border-r border-gray-200 text-sm h-screen overflow-hidden">
        <div class="px-4 py-2 flex-shrink-0">
          <h1 class="text-xl font-bold">渠道后台</h1>
        </div>
        <nav class="flex-1 overflow-y-auto scrollbar-hide overscroll-none">
          <%= link_to agent_portal_root_path, class: "block px-2 sm:px-4 py-1 hover:bg-gray-300 #{current_page?(dashboard_path) ? 'bg-gray-300' : ''}" do %>
            <span>首页</span>
          <% end %>
        </nav>

      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col h-screen overflow-hidden">        
        <div class="p-2 flex justify-end border-b border-gray-200 flex-shrink-0">
          <div class="dropdown dropdown-end dropdown-hover">
            <div tabindex="0" role="button" class="btn btn-ghost">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-user-round-icon lucide-circle-user-round"><path d="M18 20a6 6 0 0 0-12 0"/><circle cx="12" cy="10" r="4"/><circle cx="12" cy="12" r="10"/></svg>
              <div class="flex flex-col items-start">
                <div class="font-medium"><%= Current.channel&.name %></div>
                <div class="text-xs font-normal text-gray-400"><%= Current.channel&.nickname %></div>
              </div>
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-1 w-32 p-2 shadow-sm">
              <li>
                <%= link_to channel_portal_profile_setting_path, class: "justify-center" do -%>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wrench-icon lucide-wrench"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>
                  个人设置
                <% end -%>
              </li>
              <li>
                <%= link_to channel_portal_session_path, data: { turbo_method: :delete }, class: "justify-center" do -%>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-out-icon lucide-log-out"><path d="m16 17 5-5-5-5"/><path d="M21 12H9"/><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/></svg>
                  退出登录
                <% end %>
              </li>
            </ul>
          </div>
        </div>          
        <div class="flex-1 overflow-y-auto scrollbar-hide">
          <%= yield %>
        </div>          
      </div>
    </div>

    <!-- 在布局文件中添加侧边栏 -->
    <%= turbo_frame_tag "sidebar_frame" %>

    <%= turbo_frame_tag "modal" %> 
  </body>
</html>