<!DOCTYPE html>
<html data-theme="corporate">
  <head>
    <title><%= content_for(:title) || "Game Admin" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag :app %>
    <%= javascript_importmap_tags %>
    <style>
      .scrollbar-hide {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Chrome, Safari and Opera */
      }
    </style>
  </head>

  <body class="bg-gray-100">
    <div id="flash_messages" class="flash-container">
      <%= render "shared/flash_messages" %>
    </div>
    <div class="h-screen flex overflow-hidden">
      <!-- Sidebar -->
      <div class="w-32 md:w-52 flex flex-col border-r border-gray-200 text-sm h-screen overflow-hidden">
        <div class="px-4 py-2 flex-shrink-0">
          <h1 class="text-xl font-bold">Platform Admin</h1>
        </div>
        <nav class="flex-1 overflow-y-auto scrollbar-hide overscroll-none">
          <%= link_to dashboard_path, class: "block px-2 sm:px-4 py-1 hover:bg-gray-300 #{current_page?(dashboard_path) ? 'bg-gray-300' : ''}" do %>
            <span>首页</span>
          <% end %>
          <%= link_to dashboard_period_report_path, class: "block px-2 sm:px-4 py-1 hover:bg-gray-300 #{current_page?(dashboard_period_report_path) ? 'bg-gray-300' : ''}" do %>
            <span>时段报表</span>
          <% end %>
          <%= render "shared/function_menus/financial_manage" %>
          <%= render "shared/function_menus/campaign" %>
          <%= render "shared/function_menus/risk_manage" %>
          <%= render "shared/function_menus/data_statistics" %>
          <%= render "shared/function_menus/data_export" %>
          
          <%= render "shared/function_menus/users_manage" %>
          <%= render "shared/function_menus/game_manage" %>
          <%= render "shared/function_menus/back_manage" %>
        </nav>

        <!-- User Dropdown -->
        <div class="border-t border-gray-300 flex-shrink-0" data-controller="dropdown">
          <button class="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-300" data-action="click->dropdown#toggle">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                <span class="text-sm font-medium">
                  <% if Current.admin %>
                    <%= Current.admin.email_address.first.upcase %>
                  <% elsif Current.agent %>
                    <%= Current.agent.email_address.first.upcase %>
                  <% elsif Current.channel %>
                    <%= Current.channel.email_address.first.upcase %>
                  <% end %>
                </span>
              </div>
              <span class="ml-3 text-sm">
                <% if Current.admin %>
                  <%= Current.admin.email_address %>
                <% elsif Current.agent %>
                  <%= Current.agent.email_address %>
                <% elsif Current.channel %>
                  <%= Current.channel.email_address %>
                <% end %>
              </span>
            </div>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <div class="hidden absolute bottom-0 left-0 w-52 bg-gray-100 border-t border-gray-300 text-sm" data-dropdown-target="menu" data-turbo-prefetch="false">
            <%= link_to "系统设置", "#", class: "block px-4 py-3 border-b border-gray-300 text-sm hover:bg-gray-300" %>
            <%= link_to "日志查看", "#", class: "block px-4 py-3 border-b border-gray-300 text-sm hover:bg-gray-300" %>
            <%= link_to "任务管理", "#", class: "block px-4 py-3 text-sm hover:bg-gray-300" %>
            <div class="border-t border-gray-300">
              <%= button_to "退出登录", session_path, method: :delete, class: "w-full text-left px-4 py-2 text-sm hover:bg-gray-300" %>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col h-screen overflow-hidden">        
        <div class="p-2 flex justify-end items-center border-b border-gray-200 flex-shrink-0">
          <%= link_to "Redis Stream", system_redis_streams_path, class: "btn btn-sm btn-ghost" %>
          <%= link_to "系统信息", system_system_info_path, class: "btn btn-sm btn-ghost" %>
          <%= link_to "系统设置", system_setting_items_path, class: "btn btn-sm btn-ghost" %>
          <div class="dropdown dropdown-end dropdown-hover">
            <div tabindex="0" role="button" class="btn btn-ghost">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-user-round-icon lucide-circle-user-round"><path d="M18 20a6 6 0 0 0-12 0"/><circle cx="12" cy="10" r="4"/><circle cx="12" cy="12" r="10"/></svg>
              <div class="flex flex-col items-start">
                <div class="font-medium"><%= Current.admin&.name %></div>
                <div class="text-xs font-normal text-gray-400"><%= Current.admin&.nickname %></div>
              </div>
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-1 w-32 p-2 shadow-sm">
              <li>
                <%= link_to profile_setting_path, class: "justify-center" do -%>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wrench-icon lucide-wrench"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>
                  个人设置
                <% end -%>
              </li>
              <li>
                <%= link_to session_path, data: { turbo_method: :delete }, class: "justify-center" do -%>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-out-icon lucide-log-out"><path d="m16 17 5-5-5-5"/><path d="M21 12H9"/><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/></svg>
                  退出登录
                <% end %>
              </li>
            </ul>
          </div>
        </div>          
        <div class="flex-1 overflow-y-auto scrollbar-hide">
          <%= yield %>
        </div>          
      </div>
    </div>

    <!-- 在布局文件中添加侧边栏 -->
    <%= turbo_frame_tag "sidebar_frame" %>

    <%= turbo_frame_tag "modal" %> 
  </body>
</html>