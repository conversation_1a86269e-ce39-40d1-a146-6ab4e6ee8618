<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "SaaS" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app %>
    <%= javascript_importmap_tags %>
  </head>
  <body class="min-h-screen bg-slate-50 relative overflow-x-hidden" style="background-image: url(<%= asset_path('StackedWave.svg') %>); background-size: cover; background-position: center;">
    <div id="flash_messages" class="fixed top-4 right-4 z-50">
      <%= render "shared/flash_messages" %>
    </div>

    <div class="min-h-screen flex items-center justify-center p-8">
      <%= yield %>
    </div>
  </body>
</html>