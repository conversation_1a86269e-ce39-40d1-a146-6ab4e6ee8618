.flash-container {
    position: fixed; /* 固定位置，不随滚动条移动 */
    top: 1rem;       /* 距离顶部 1rem (或其他你喜欢的值) */
    left: 50%;       /* 水平居中定位 */
    transform: translateX(-50%); /* 确保真正居中 */
    z-index: 1050;   /* 确保在大多数元素之上 */
    width: auto;     /* 宽度自适应内容 */
    max-width: 80%;  /* 设置一个最大宽度，防止过长 */
    display: flex;     /* 使用 flexbox 布局内部 flash 消息 */
    flex-direction: column; /* 垂直堆叠多个 flash 消息 */
    align-items: center; /* 水平居中内部的 flash 消息 */
  }
  
  .flash {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem; /* 多个消息之间的间距 */
    border: 1px solid transparent;
    border-radius: 0.25rem;
    min-width: 250px; /* 设置一个最小宽度 */
    text-align: center; /* 文本居中 */
    opacity: 1; /* 初始不透明 */
    transition: opacity 0.5s ease-out; /* 添加淡出过渡效果 */
    position: relative; /* 为了关闭按钮的绝对定位 */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* 添加一点阴影 */
  }
  
  /* 针对不同类型的 flash 添加不同样式 (示例) */
  .flash-notice {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
  }
  
  .flash-alert {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
  }
  
  /* 淡出时的样式 */
  .flash.fade-out {
    opacity: 0;
  }
  
  /* 关闭按钮样式 (可选) */
  .flash .close-flash {
    position: absolute;
    top: 0.5rem;
    right: 0.75rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    line-height: 1;
    color: inherit;
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
  }
  .flash .close-flash:hover {
    opacity: 1;
  }