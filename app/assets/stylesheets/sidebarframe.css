/* 包装容器 */
[data-controller="sidebarframe"] {
  position: relative;
}

/* 初始化状态 - 隐藏 */
[data-controller="sidebarframe"].initializing .sidebarframe {
  transform: translateX(400px); /* 初始时在右侧隐藏 */
}

[data-controller="sidebarframe"].initializing .sidebarframe-overlay {
  opacity: 0;
  visibility: hidden;
}

/* 关闭动画 */
[data-controller="sidebarframe"].removing .sidebarframe {
  transform: translateX(400px); /* 滑出隐藏 */
}

/* 侧边栏基础样式 */
.sidebarframe {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100%;
  background: white;
  box-shadow: -2px 0 8px rgba(0,0,0,0.1);
  transition: transform 0.25s ease-out;
  z-index: 1000;
  transform: translateX(0); /* 默认显示 */
  overflow-y: auto; /* 内容过多时可滚动 */
}

/* 遮罩层 */
.sidebarframe-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  opacity: 1;
  visibility: visible;
  transition: opacity 0.25s ease-out, visibility 0.25s ease-out;
  z-index: 999;
}

/* 关闭动画 */
[data-controller="sidebarframe"].removing .sidebarframe-overlay {
  opacity: 0;
  visibility: hidden;
}