<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="1440" height="560" preserveAspectRatio="none" viewBox="0 0 1440 560"><g mask="url(&quot;#SvgjsMask1021&quot;)" fill="none"><rect width="1440" height="560" x="0" y="0" fill="rgba(80, 141, 204, 1)"></rect><path d="M5 194L4 565" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M506 334L505 646" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M19 299L18 134" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M640 557L639 334" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M179 100L178 345" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M340 37L339 210" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M392 397L391 780" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M421 170L420 444" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M791 394L790 805" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M849 50L848 -270" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1321 161L1320 -45" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M714 379L713 733" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1262 89L1261 -330" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M882 447L881 744" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M1169 511L1168 905" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M420 268L419 -122" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M609 526L608 239" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M299 3L298 262" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1062 214L1061 609" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1342 76L1341 -216" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M1097 176L1096 547" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M1321 524L1320 342" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M165 481L164 843" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M80 123L79 -219" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M298 191L297 332" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M19 271L18 -94" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M459 306L458 726" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M970 540L969 934" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M1414 111L1413 -137" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M439 312L438 99" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M857 1L856 -177" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1350 214L1349 37" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M1155 181L1154 377" stroke-width="10" stroke="url(&quot;#SvgjsLinearGradient1023&quot;)" stroke-linecap="round" class="Down"></path><path d="M912 370L911 73" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M716 117L715 -244" stroke-width="6" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path><path d="M105 508L104 336" stroke-width="8" stroke="url(&quot;#SvgjsLinearGradient1022&quot;)" stroke-linecap="round" class="Up"></path></g><defs><mask id="SvgjsMask1021"><rect width="1440" height="560" fill="#ffffff"></rect></mask><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="SvgjsLinearGradient1022"><stop stop-color="rgba(28, 83, 142, 0)" offset="0"></stop><stop stop-color="#1c538e" offset="1"></stop></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="SvgjsLinearGradient1023"><stop stop-color="rgba(28, 83, 142, 0)" offset="0"></stop><stop stop-color="#1c538e" offset="1"></stop></linearGradient></defs></svg>