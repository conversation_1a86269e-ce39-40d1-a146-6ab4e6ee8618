admin_totp_secret_dev = "2TPLLNWCG7CDNVSCUZ5RJRFE3YZVZ2LQ"

admin = Admin.create!(name: "admin", email_address: "<EMAIL>", password: "password123",
    nickname: "管理员呀", mobile: "8613512345678", remark: "管理员",
    department: "admin", totp_secret: admin_totp_secret_dev)

admin_role = Role.create!(name: "admin", code: "admin")
staff_role = Role.create!(name: "staff", code: "staff")
finance_role = Role.create!(name: "finance", code: "finance")
operation_role = Role.create!(name: "operation", code: "operation")
hr_role = Role.create!(name: "hr", code: "hr")
marketing_role = Role.create!(name: "marketing", code: "marketing")
tech_role = Role.create!(name: "tech", code: "tech")
legal_role = Role.create!(name: "legal", code: "legal")

admin.add_role(admin_role)

admin.add_role(staff_role)
admin.add_role(finance_role)
admin.add_role(operation_role)
admin.add_role(hr_role)
admin.add_role(marketing_role)
admin.add_role(tech_role)
admin.add_role(legal_role)

load Rails.root.join('db/seeds/menus.rb')
load Rails.root.join('db/seeds/admins.rb')
load Rails.root.join('db/seeds/promotion_types.rb')
load Rails.root.join('db/seeds/agents.rb')
load Rails.root.join('db/seeds/users.rb')
load Rails.root.join('db/seeds/settings.rb')
load Rails.root.join('db/seeds/payment_types.rb')
load Rails.root.join('db/seeds/payments.rb')
load Rails.root.join('db/seeds/withdraws.rb')
load Rails.root.join('db/seeds/recharges.rb')
load Rails.root.join('db/seeds/white_ip_and_black_ips.rb')
load Rails.root.join('db/seeds/black_bank_card.rb')
load Rails.root.join('db/seeds/game_platforms.rb')
load Rails.root.join('db/seeds/game_types.rb')
load Rails.root.join('db/seeds/integrators.rb')
# load Rails.root.join('db/seeds/games.rb')
load Rails.root.join('db/seeds/ads.rb')
