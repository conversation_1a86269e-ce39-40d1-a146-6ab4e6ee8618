# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_02_085128) do
  create_table "admin_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "admin_id", null: false, comment: "管理员ID"
    t.bigint "role_id", null: false, comment: "角色ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id", "role_id"], name: "index_admin_roles_on_admin_id_and_role_id", unique: true
    t.index ["admin_id"], name: "index_admin_roles_on_admin_id"
    t.index ["role_id"], name: "index_admin_roles_on_role_id"
  end

  create_table "admins", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "管理员用户名"
    t.string "nickname", comment: "昵称"
    t.string "email_address", null: false, comment: "邮箱地址"
    t.string "mobile", comment: "手机号"
    t.string "password_digest", null: false, comment: "密码摘要"
    t.string "totp_secret", comment: "2FA密钥，创建管理员时自动生成"
    t.string "department", default: "game", null: false, comment: "部门，详细定义见代码"
    t.string "avatar", comment: "头像URL"
    t.text "remark", comment: "备注"
    t.integer "status", default: 0, null: false, comment: "状态，详细定义见代码"
    t.string "last_login_ip", comment: "最后登录IP"
    t.datetime "last_login_at", comment: "最后登录时间"
    t.string "last_login_user_agent", comment: "最后登录的浏览器信息"
    t.integer "login_count", default: 0, null: false, comment: "登录次数"
    t.string "timezone", default: "UTC", null: false, comment: "时区"
    t.datetime "deleted_at", comment: "软删除时间"
    t.string "created_by", comment: "创建人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email_address"], name: "index_admins_on_email_address", unique: true
    t.index ["name"], name: "index_admins_on_name", unique: true
  end

  create_table "ads", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", comment: "广告名称"
    t.string "media", comment: "图片"
    t.string "media_type", default: "image", comment: "媒体类型: 图片/视频"
    t.string "type", comment: "单表类型: 大厅广告/弹窗广告/悬浮入口广告"
    t.string "ad_position", comment: "广告位置"
    t.string "redirect_type", comment: "跳转类型"
    t.string "redirect_link", comment: "跳转链接"
    t.string "display_occasion", comment: "展示场合/时机: 每日首次登录/每次刷新"
    t.string "display_platform", comment: "展示平台,暂时无用"
    t.string "visibility", comment: "可见性: 全体/未充值/已充值"
    t.string "created_by", comment: "创建人"
    t.integer "status", default: 1, comment: "状态: 0-下线 1-上线"
    t.integer "order", default: 0, comment: "排序, 越大越靠前"
    t.datetime "expired_at", comment: "过期时间, 为空则永久有效"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "agents", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "代理商名称"
    t.string "nickname", comment: "代理商昵称"
    t.string "email_address", comment: "邮箱地址"
    t.string "mobile", comment: "手机号"
    t.string "password_digest", null: false, comment: "密码摘要"
    t.string "totp_secret", comment: "2FA密钥"
    t.string "avatar", comment: "头像URL"
    t.string "remark", comment: "备注"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.string "last_login_ip", comment: "最后登录IP"
    t.datetime "last_login_at", comment: "最后登录时间"
    t.string "last_login_user_agent", comment: "最后登录的浏览器信息"
    t.integer "login_count", default: 0, null: false, comment: "登录次数"
    t.string "timezone", default: "UTC", null: false, comment: "时区"
    t.string "created_by", comment: "创建人"
    t.datetime "deleted_at", comment: "软删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_agents_on_name", unique: true
  end

  create_table "bank_cards", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "关联用户"
    t.string "card_number", null: false, comment: "卡号"
    t.string "card_username", null: false, comment: "卡户名"
    t.string "card_type", comment: "卡类型"
    t.string "card_bank", comment: "卡银行"
    t.string "card_province", comment: "卡省份"
    t.string "card_city", comment: "卡城市"
    t.string "card_branch", comment: "卡支行"
    t.string "remark", comment: "备注"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bank_cards_on_user_id"
  end

  create_table "black_bank_cards", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "card_number", null: false, comment: "卡号"
    t.text "remark", comment: "备注"
    t.string "created_by", comment: "创建人"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "black_ips", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "ip", null: false, comment: "IP地址"
    t.string "remark", comment: "备注"
    t.string "created_by", comment: "创建人"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "blacklist_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "用户ID"
    t.integer "ban_type", null: false, comment: "黑名单类型"
    t.string "reason", comment: "黑名单原因"
    t.boolean "actived", default: true, comment: "是否生效"
    t.datetime "expires_at", comment: "自动解封时间"
    t.integer "operator_id", comment: "操作人ID"
    t.string "created_by", comment: "操作人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_blacklist_records_on_user_id"
  end

  create_table "buttons", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "按钮名称"
    t.string "identifier", null: false, comment: "按钮标识符"
    t.integer "status", default: 0, comment: "状态，详细定义见代码"
    t.text "description", comment: "按钮描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["identifier"], name: "index_buttons_on_identifier", unique: true
  end

  create_table "channels", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "渠道名称"
    t.string "nickname", comment: "渠道昵称"
    t.string "email_address", comment: "邮箱地址"
    t.string "mobile", comment: "手机号"
    t.string "password_digest", comment: "密码摘要"
    t.string "avatar", comment: "头像URL"
    t.text "remark", comment: "备注"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.string "last_login_ip", comment: "最后登录IP"
    t.datetime "last_login_at", comment: "最后登录时间"
    t.string "last_login_user_agent", comment: "最后登录的浏览器信息"
    t.integer "login_count", default: 0, null: false, comment: "登录次数"
    t.string "timezone", default: "UTC", null: false, comment: "时区"
    t.string "advertiser", comment: "广告方"
    t.string "pixel_id", comment: "像素ID"
    t.string "pixel_token", comment: "像素Token"
    t.bigint "agent_id", null: false
    t.bigint "promotion_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agent_id"], name: "index_channels_on_agent_id"
    t.index ["name"], name: "index_channels_on_name", unique: true
    t.index ["promotion_type_id"], name: "index_channels_on_promotion_type_id"
  end

  create_table "deposits", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "用户ID"
    t.decimal "amount", precision: 10, scale: 2, default: "0.0", comment: "充值金额"
    t.decimal "bonus", precision: 10, scale: 2, default: "0.0", comment: "奖励金额"
    t.decimal "bonus_multiplier", precision: 10, scale: 2, default: "0.0", comment: "奖励打码倍率"
    t.decimal "required_volume", precision: 10, scale: 2, default: "0.0", comment: "本次充值贡献的打码量"
    t.string "remark", comment: "备注"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.string "failed_reason", comment: "失败原因"
    t.datetime "completed_at", comment: "完成时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_deposits_on_user_id"
  end

  create_table "game_platforms", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "平台名称"
    t.string "cover_url", comment: "平台封面URL"
    t.string "cover_url_selected", comment: "平台封面URL(选中状态)"
    t.integer "order", default: 0, null: false, comment: "排序, 越大越靠前"
    t.integer "status", default: 1, null: false, comment: "状态: 0-下线 1-上线"
    t.string "created_by", comment: "创建人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_game_platforms_on_name", unique: true
  end

  create_table "game_types", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "类型名称"
    t.integer "order", default: 0, null: false, comment: "排序, 越大越靠前"
    t.integer "status", default: 1, null: false, comment: "状态: 0-下线 1-上线"
    t.string "created_by", comment: "创建人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "games", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "game_platform_id", null: false, comment: "游戏平台ID"
    t.bigint "integrator_id", comment: "集成商关联ID"
    t.bigint "game_type_id", null: false, comment: "游戏类型ID"
    t.string "name", null: false, comment: "游戏名称"
    t.text "description", comment: "游戏描述"
    t.string "integrator_identity", comment: "集成商标识"
    t.string "integrator_game_id", comment: "集成商游戏ID"
    t.integer "order", default: 0, comment: "排序, 越大越靠前"
    t.boolean "demo", default: false, comment: "是否为demo游戏"
    t.boolean "hot", default: false, comment: "是否为热门游戏"
    t.boolean "suggested", default: false, comment: "是否为推荐游戏"
    t.datetime "suggested_at", comment: "推荐时间"
    t.string "cover_url_0", comment: "游戏封面URL(竖长方形)"
    t.string "cover_url_1", comment: "游戏封面URL(正方形)"
    t.string "cover_url_2", comment: "游戏封面URL(横长方形)"
    t.string "cover_url_3", comment: "游戏封面URL"
    t.string "cover_url_4", comment: "游戏封面URL"
    t.text "settings", comment: "游戏设置"
    t.integer "screen_direction", default: 0, comment: "游戏方向: 0-自動 1-竖屏 2-横屏"
    t.integer "status", default: 1, null: false, comment: "状态: 0-下线 1-上线"
    t.string "created_by", comment: "创建人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["game_platform_id"], name: "index_games_on_game_platform_id"
    t.index ["game_type_id"], name: "index_games_on_game_type_id"
    t.index ["integrator_id"], name: "index_games_on_integrator_id"
  end

  create_table "integrators", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "集成商名称"
    t.string "code", null: false, comment: "集成商编码"
    t.text "description", comment: "集成商描述"
    t.text "settings", comment: "集成商设置"
    t.integer "order", default: 0, null: false, comment: "排序, 越大越靠前"
    t.integer "status", default: 1, null: false, comment: "状态: 0-下线 1-上线"
    t.string "created_by", comment: "创建人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_integrators_on_code", unique: true
    t.index ["name"], name: "index_integrators_on_name", unique: true
  end

  create_table "menus", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "menu_type", null: false, comment: "菜单类型，详细定义见代码"
    t.string "name", null: false, comment: "菜单名称"
    t.string "title", null: false, comment: "菜单标题"
    t.string "icon", comment: "菜单图标"
    t.string "route_name", null: false, comment: "路由名称"
    t.string "path", comment: "路由路径"
    t.string "layout", comment: "布局组件"
    t.string "component", comment: "页面组件"
    t.integer "parent_id", comment: "父级菜单ID"
    t.integer "order", default: 0, comment: "排序"
    t.integer "status", default: 0, comment: "状态，详细定义见代码"
    t.boolean "hidden", default: false, comment: "是否隐藏"
    t.text "description", comment: "菜单描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "payment_types", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", comment: "类型名称"
    t.boolean "status", default: false, comment: "启用状态"
    t.integer "weight", default: 0, comment: "权重"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "payments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.decimal "recharge_fee_rate", precision: 10, scale: 4, default: "0.0", comment: "支付费率"
    t.decimal "min_recharge", precision: 16, scale: 2, default: "0.0", comment: "最小充值"
    t.decimal "max_recharge", precision: 16, scale: 2, default: "0.0", comment: "最大充值"
    t.integer "recharge_weight", default: 0, comment: "充值权重"
    t.string "recharge_method", limit: 50, comment: "充值方法, 根据pay.json中开放的定义来"
    t.bigint "payment_type_id", comment: "支付类型"
    t.string "payment_way", limit: 50, comment: "支付打开方式"
    t.boolean "recharge_enabled", default: true, comment: "充值开关"
    t.decimal "withdraw_fee_rate", precision: 10, scale: 4, default: "0.0", comment: "提现费率"
    t.decimal "min_withdraw", precision: 16, scale: 2, default: "0.0", comment: "最小提现"
    t.decimal "max_withdraw", precision: 16, scale: 2, default: "0.0", comment: "最大提现"
    t.integer "withdraw_weight", default: 0, comment: "提现权重"
    t.boolean "withdraw_enabled", default: false, comment: "提现开关"
    t.string "name", limit: 100, comment: "支付名称"
    t.string "page_callback_url", comment: "页面回调"
    t.string "recharge_request_url", comment: "充值请求地址"
    t.string "recharge_callback_url", comment: "充值回调地址, todo: removed"
    t.string "withdraw_request_url", comment: "提现请求地址"
    t.string "withdraw_callback_url", comment: "提现回调地址, todo: removed"
    t.string "ip_whitelist", comment: "IP白名单，逗号分隔"
    t.text "extras", comment: "额外配置json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payment_type_id"], name: "index_payments_on_payment_type_id"
  end

  create_table "promotion_types", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "code", null: false
    t.integer "status", default: 0
    t.integer "sort", default: 0
    t.string "created_by"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "recharges", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "agent_id", default: 0, null: false, comment: "总代"
    t.integer "channel_id", default: 0, null: false, comment: "渠道"
    t.bigint "user_id", comment: "用户ID"
    t.string "username", limit: 100, comment: "用户名"
    t.decimal "amount", precision: 16, scale: 2, comment: "充值金额"
    t.decimal "gift_amount", precision: 16, scale: 2, default: "0.0", comment: "赠送金额"
    t.boolean "first_recharge", default: false, comment: "是否首充"
    t.string "order_no", limit: 100, comment: "平台订单号"
    t.bigint "payment_id", comment: "支付通道"
    t.string "payment_order_no", limit: 100, comment: "支付订单号"
    t.string "ip", limit: 64, comment: "IP地址"
    t.string "app_type", limit: 50, comment: "应用类型"
    t.integer "status", default: 0, comment: "订单状态"
    t.datetime "callback_time", comment: "回调时间"
    t.datetime "recharge_time", comment: "充值时间"
    t.text "result", comment: "回调结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "activity_id", default: 0, comment: "活动ID"
    t.index ["order_no"], name: "index_recharges_on_order_no", unique: true
    t.index ["payment_id"], name: "index_recharges_on_payment_id"
    t.index ["payment_order_no"], name: "index_recharges_on_payment_order_no"
    t.index ["user_id"], name: "index_recharges_on_user_id"
  end

  create_table "register_tracks", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "用户ID"
    t.integer "agent_id", default: 0, null: false, comment: "代理ID"
    t.integer "channel_id", default: 0, null: false, comment: "渠道ID"
    t.integer "parent_id", default: 0, null: false, comment: "上级ID"
    t.integer "super_id", default: 0, null: false, comment: "上上级ID"
    t.integer "super_parent_id", default: 0, null: false, comment: "上上上级ID"
    t.string "ua", default: "", null: false, comment: "浏览器信息"
    t.string "device_id", default: "", null: false, comment: "设备ID"
    t.string "ip", default: "", null: false, comment: "IP地址"
    t.text "ad_id", null: false, comment: "广告ID"
    t.text "ad_click_id", null: false, comment: "广告点击ID"
    t.string "device_type", default: "", null: false, comment: "设备安装类型:0 H5 1 PWA 2 APK"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_register_tracks_on_user_id"
  end

  create_table "role_buttons", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "role_id", null: false, comment: "角色ID"
    t.bigint "button_id", null: false, comment: "按钮ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["button_id"], name: "index_role_buttons_on_button_id"
    t.index ["role_id"], name: "index_role_buttons_on_role_id"
  end

  create_table "role_menus", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "role_id", null: false, comment: "角色ID"
    t.bigint "menu_id", null: false, comment: "菜单ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["menu_id"], name: "index_role_menus_on_menu_id"
    t.index ["role_id"], name: "index_role_menus_on_role_id"
  end

  create_table "roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false, comment: "角色名称"
    t.string "code", null: false, comment: "角色编码"
    t.integer "status", default: 0, null: false, comment: "状态，详细定义见代码"
    t.text "description", comment: "角色描述"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_roles_on_code", unique: true
    t.index ["name"], name: "index_roles_on_name", unique: true
  end

  create_table "sessions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "admin_id", comment: "关联的登录:管理员ID"
    t.bigint "agent_id", comment: "关联的登录:代理商ID"
    t.bigint "channel_id", comment: "关联的登录:渠道ID"
    t.string "ip_address", comment: "登录IP地址"
    t.string "user_agent", comment: "登录用户代理"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "settings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false, comment: "键"
    t.string "label", null: false, comment: "标签"
    t.text "value", comment: "值"
    t.string "value_type", null: false, comment: "值类型"
    t.text "options", comment: "选项"
    t.string "description", comment: "描述"
    t.string "created_by", comment: "创建人"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.integer "sort", default: 0, comment: "排序"
    t.string "section", null: false, comment: "分组"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key", "section"], name: "index_settings_on_key_and_section", unique: true
  end

  create_table "system_logs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "actor_type", null: false
    t.bigint "actor_id", null: false
    t.string "loggable_type", null: false
    t.bigint "loggable_id", null: false
    t.string "ip", limit: 64, comment: "IP"
    t.string "user_agent", comment: "用户浏览器信息"
    t.integer "operate", null: false, comment: "操作类型"
    t.string "action", comment: "操作"
    t.text "snapshot", comment: "快照"
    t.datetime "created_at", null: false, comment: "创建时间"
    t.index ["actor_id", "actor_type"], name: "index_system_logs_on_actor_id_and_actor_type"
    t.index ["actor_type", "actor_id"], name: "index_system_logs_on_actor"
    t.index ["loggable_type", "loggable_id"], name: "index_system_logs_on_loggable"
  end

  create_table "tags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "created_by", comment: "创建人"
    t.string "remark", comment: "备注"
    t.boolean "status", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_tags_on_name", unique: true
  end

  create_table "user_order_summaries", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "total_recharge_count", default: 0, comment: "总入金次数"
    t.decimal "total_recharge_amount", precision: 16, scale: 2, default: "0.0", comment: "总入金金额"
    t.datetime "first_recharge_at", comment: "首次入金时间"
    t.decimal "first_recharge_amount", precision: 16, scale: 2, default: "0.0", comment: "首次入金金额"
    t.integer "total_withdraw_count", default: 0, comment: "总出金次数"
    t.decimal "total_withdraw_amount", precision: 16, scale: 2, default: "0.0", comment: "总出金金额"
    t.datetime "first_withdraw_at", comment: "首次出金时间"
    t.decimal "first_withdraw_amount", precision: 16, scale: 2, default: "0.0", comment: "首次出金金额"
    t.decimal "recharge_withdraw_diff", precision: 10, comment: "充提差"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_order_summaries_on_user_id"
  end

  create_table "user_tags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tag_id", null: false
    t.string "created_by", comment: "创建人"
    t.string "remark", comment: "备注"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tag_id"], name: "index_user_tags_on_tag_id"
    t.index ["user_id", "tag_id"], name: "index_user_tags_on_user_id_and_tag_id", unique: true
    t.index ["user_id"], name: "index_user_tags_on_user_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", comment: "用户名"
    t.string "nickname", comment: "昵称"
    t.string "email_address", comment: "邮箱地址"
    t.string "mobile", comment: "手机号"
    t.string "password_digest", null: false, comment: "密码摘要"
    t.string "totp_secret", comment: "2FA密钥"
    t.string "avatar", comment: "头像URL"
    t.text "remark", comment: "备注"
    t.integer "status", default: 0, null: false, comment: "状态：0-正常，1-禁用"
    t.string "last_login_ip", comment: "最后登录IP"
    t.datetime "last_login_at", comment: "最后登录时间"
    t.string "last_login_user_agent", comment: "最后登录的浏览器信息"
    t.integer "login_count", default: 0, null: false, comment: "登录次数"
    t.string "timezone", default: "UTC", null: false, comment: "时区"
    t.datetime "deleted_at", comment: "软删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_users_on_name", unique: true
  end

  create_table "wallet_exts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "用户ID"
    t.decimal "commission", precision: 16, scale: 2, default: "0.0", comment: "佣金"
    t.decimal "recharge_reward", precision: 16, scale: 2, default: "0.0", comment: "充值奖励"
    t.datetime "updated_at", comment: "最后更新时间"
    t.index ["user_id"], name: "index_wallet_exts_on_user_id", unique: true
  end

  create_table "wallets", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "用户ID"
    t.decimal "balance", precision: 16, scale: 2, default: "0.0", comment: "余额"
    t.decimal "betting_volume", precision: 16, scale: 2, default: "0.0", comment: "已累积打码量"
    t.decimal "required_volume", precision: 16, scale: 2, default: "0.0", comment: "所需打码量"
    t.decimal "remain_volume", precision: 16, scale: 2, default: "0.0", comment: "剩余打码量"
    t.decimal "total_win", precision: 16, scale: 2, default: "0.0", comment: "总输赢"
    t.decimal "total_real_win", precision: 16, scale: 2, default: "0.0", comment: "总实际输赢"
    t.integer "bet_count", default: 0, comment: "总下注次数"
    t.datetime "updated_at", comment: "最后更新时间"
    t.index ["user_id"], name: "index_wallets_on_user_id", unique: true
  end

  create_table "white_ips", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "ip", null: false, comment: "IP地址"
    t.string "remark", comment: "备注"
    t.string "created_by", comment: "创建人"
    t.integer "status", default: 0, comment: "状态, 详细定义见代码"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "withdraws", id: { type: :bigint, unsigned: true }, charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", comment: "提现申请表", force: :cascade do |t|
    t.integer "agent_id", default: 0, null: false, comment: "总代"
    t.integer "channel_id", default: 0, null: false, comment: "渠道"
    t.bigint "user_id", comment: "用户ID"
    t.string "order_no", limit: 64, comment: "提现订单号"
    t.string "username", limit: 100, comment: "用户名"
    t.decimal "amount", precision: 16, scale: 2, null: false, comment: "提现金额"
    t.decimal "fee", precision: 16, scale: 2, default: "0.0", comment: "手续费"
    t.decimal "real_amount", precision: 16, scale: 2, null: false, comment: "实际提现金额"
    t.integer "status", limit: 1, default: 0, null: false, comment: "提现状态"
    t.integer "risk", limit: 1, default: 0, comment: "封控状态"
    t.datetime "review_time", comment: "审核时间"
    t.datetime "process_time", comment: "处理时间（打款发起时间）"
    t.datetime "finish_time", comment: "完成时间"
    t.string "ip", limit: 45, comment: "申请时用户IP"
    t.bigint "bank_card_id", comment: "银行卡"
    t.bigint "payment_id", comment: "支付通道"
    t.string "payment_order_no", limit: 100, comment: "支付订单号"
    t.string "remark", comment: "备注"
    t.text "result", comment: "回调结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bank_card_id"], name: "index_withdraws_on_bank_card_id"
    t.index ["order_no"], name: "index_withdraws_on_order_no", unique: true
    t.index ["payment_id"], name: "index_withdraws_on_payment_id"
    t.index ["status"], name: "idx_status"
    t.index ["user_id"], name: "index_withdraws_on_user_id"
  end

  add_foreign_key "blacklist_records", "users"
  add_foreign_key "channels", "agents"
  add_foreign_key "channels", "promotion_types"
  add_foreign_key "payments", "payment_types"
  add_foreign_key "recharges", "payments"
  add_foreign_key "recharges", "users"
  add_foreign_key "role_buttons", "buttons"
  add_foreign_key "role_buttons", "roles"
  add_foreign_key "role_menus", "menus"
  add_foreign_key "role_menus", "roles"
  add_foreign_key "user_order_summaries", "users"
  add_foreign_key "user_tags", "tags"
  add_foreign_key "user_tags", "users"
  add_foreign_key "withdraws", "bank_cards"
  add_foreign_key "withdraws", "payments"
  add_foreign_key "withdraws", "users"
end
