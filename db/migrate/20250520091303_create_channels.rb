class CreateChannels < ActiveRecord::Migration[8.0]
  def change
    # 推广类型
    create_table :promotion_types do |t|
      t.string :name
      t.string :code, null: false
      t.integer :status, default: 0
      t.integer :sort, default: 0
      t.string :created_by
      t.timestamps
    end

    # 推广渠道
    create_table :channels do |t|
      t.string :name, null: false, comment: '渠道名称'
      t.string :nickname, comment: '渠道昵称'
      t.string :email_address, comment: '邮箱地址'
      t.string :mobile, comment: '手机号'
      t.string :password_digest, comment: '密码摘要'

      t.string :avatar, comment: '头像URL'
      t.text :remark, comment: '备注'
      t.integer :status, default: 0, comment: '状态, 详细定义见代码'

      t.string :last_login_ip, comment: '最后登录IP'
      t.datetime :last_login_at, comment: '最后登录时间'
      t.string :last_login_user_agent, comment: '最后登录的浏览器信息'
      t.integer :login_count, null: false, default: 0, comment: '登录次数'
      t.string :timezone, null: false, default: "UTC", comment: '时区'

      t.string :advertiser, comment: '广告方'
      t.string :pixel_id, comment: '像素ID'
      t.string :pixel_token, comment: '像素Token'

      t.references :agent, null: false, foreign_key: true
      t.references :promotion_type, null: false, foreign_key: true

      t.timestamps
    end

    add_index :channels, :name, unique: true
  end
end
