class CreateDeposits < ActiveRecord::Migration[8.0]
  def change
    # 充值记录表
    create_table :deposits do |t|
      t.bigint :user_id, null: false, comment: '用户ID'
      t.decimal :amount, precision: 10, scale: 2, default: 0, comment: '充值金额'
      t.decimal :bonus, precision: 10, scale: 2, default: 0, comment: '奖励金额'
      t.decimal :bonus_multiplier, precision: 10, scale: 2, default: 0, comment: '奖励打码倍率'
      t.decimal :required_volume, precision: 10, scale: 2, default: 0, comment: '本次充值贡献的打码量'
      t.string :remark, comment: '备注'
      # 充值状态：pending, completed, failed
      t.integer :status, default: 0, comment: '状态, 详细定义见代码'
      t.string :failed_reason, comment: '失败原因'
      t.datetime :completed_at, comment: '完成时间'
      t.timestamps
    end

    # amount: 用户实际充值金额（如10元）。
    # bonus: 奖励金额（如5元）。
    # bonus_multiplier: 奖励对应的打码倍率（如2.0）。
    # required_volume: 本次充值增加的打码量（amount + bonus * bonus_multiplier）

    add_index :deposits, :user_id
  end
end
