class CreateGamePlatforms < ActiveRecord::Migration[8.0]
  def change
    # 游戏平台
    create_table :game_platforms do |t|
      t.string :name, null: false, comment: '平台名称'
      t.string :cover_url, comment: '平台封面URL'
      t.string :cover_url_selected, comment: '平台封面URL(选中状态)'
      t.integer :order, null: false, default: 0, comment: '排序, 越大越靠前'
      t.integer :status, null: false, default: 1, comment: '状态: 0-下线 1-上线'
      t.string :created_by, comment: '创建人'
      t.timestamps
    end

    add_index :game_platforms, :name, unique: true
  end
end
