class CreateGames < ActiveRecord::Migration[8.0]
  def change
    # 游戏列表
    create_table :games do |t|
      t.references :game_platform, null: false, comment: '游戏平台ID'
      t.references :integrator, comment: '集成商关联ID'
      t.references :game_type, null: false, comment: '游戏类型ID'
      t.string :name, null: false, comment: '游戏名称'
      t.text :description, comment: '游戏描述'
      t.string :integrator_identity, comment: '集成商标识'
      t.string :integrator_game_id, comment: '集成商游戏ID'
      t.integer :order, default: 0, comment: '排序, 越大越靠前'
      t.boolean :demo, default: false, comment: '是否为demo游戏'
      t.boolean :hot, default: false, comment: '是否为热门游戏'
      t.boolean :suggested, default: false, comment: '是否为推荐游戏'
      t.datetime :suggested_at, comment: '推荐时间'
      t.string :cover_url_0, comment: '游戏封面URL(竖长方形)'
      t.string :cover_url_1, comment: '游戏封面URL(正方形)'
      t.string :cover_url_2, comment: '游戏封面URL(横长方形)'
      t.string :cover_url_3, comment: '游戏封面URL'
      t.string :cover_url_4, comment: '游戏封面URL'
      t.text :settings, comment: '游戏设置'
      t.integer :screen_direction, default: 0, comment: '游戏方向: 0-自動 1-竖屏 2-横屏'
      t.integer :status, null: false, default: 1, comment: '状态: 0-下线 1-上线'
      t.string :created_by, comment: '创建人'
      t.timestamps
    end
  end
end
