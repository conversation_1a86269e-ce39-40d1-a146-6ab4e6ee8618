class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.string :name, comment: '用户名'
      t.string :nickname, comment: '昵称'
      t.string :email_address, comment: '邮箱地址'
      t.string :mobile, comment: '手机号'

      t.string :password_digest, null: false, comment: '密码摘要'
      t.string :totp_secret, comment: '2FA密钥'

      t.string :avatar, comment: '头像URL'
      t.text :remark, comment: '备注'
      t.integer :status, null: false, default: 0, comment: '状态：0-正常，1-禁用'


      t.string :last_login_ip, comment: '最后登录IP'
      t.datetime :last_login_at, comment: '最后登录时间'
      t.string :last_login_user_agent, comment: '最后登录的浏览器信息'
      t.integer :login_count, null: false, default: 0, comment: '登录次数'
      t.string :timezone, null: false, default: "UTC", comment: '时区'

      t.datetime :deleted_at, comment: '软删除时间'
      t.timestamps
    end
    add_index :users, :name, unique: true
    # add_index :users, :email_address, unique: true
  end
end
