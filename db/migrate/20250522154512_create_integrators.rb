class CreateIntegrators < ActiveRecord::Migration[8.0]
  def change
    # 游戏集成商
    create_table :integrators do |t|
      t.string :name, null: false, comment: '集成商名称'
      t.string :code, null: false, comment: '集成商编码'
      t.text :description, comment: '集成商描述'
      t.text :settings, comment: '集成商设置'
      t.integer :order, null: false, default: 0, comment: '排序, 越大越靠前'
      t.integer :status, null: false, default: 1, comment: '状态: 0-下线 1-上线'
      t.string :created_by, comment: '创建人'
      t.timestamps
    end

    add_index :integrators, :name, unique: true
    add_index :integrators, :code, unique: true
  end
end
