class CreateWallets < ActiveRecord::Migration[8.0]
  def change
    # 用户钱包表
    create_table :wallets do |t|
      t.bigint :user_id, null: false, comment: '用户ID'
      # 用户账户总余额（充值+奖励+游戏输赢）
      t.decimal :balance, precision: 16, scale: 2, default: 0, comment: '余额'

      # 已累计的下注金额，用于计算是否满足打码量要求
      t.decimal :betting_volume, precision: 16, scale: 2, default: 0, comment: '已累积打码量'
      # 提现所需的总打码量（基于充值和奖励计算）
      t.decimal :required_volume, precision: 16, scale: 2, default: 0, comment: '所需打码量'

      # 添加打码量字段
      t.decimal :remain_volume, precision: 16, scale: 2, default: 0, comment: '剩余打码量'
      t.decimal :total_win, precision: 16, scale: 2, default: 0, comment: '总输赢'
      t.decimal :total_real_win, precision: 16, scale: 2, default: 0, comment: '总实际输赢'
      t.integer :bet_count, default: 0, comment: '总下注次数'

      t.datetime :updated_at, comment: '最后更新时间'
    end

    add_index :wallets, :user_id, unique: true
  end
end
