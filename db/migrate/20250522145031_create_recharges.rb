class CreateRecharges < ActiveRecord::Migration[8.0]
  def change
    create_table :recharges do |t|
      t.integer :agent_id, null: false, default: 0, comment: '总代'
      t.integer :channel_id, null: false, default: 0, comment: '渠道'
      t.references :user, foreign_key: true, comment: '用户ID'
      t.string :username, limit: 100, comment: '用户名'
      t.decimal :amount, precision: 16, scale: 2, comment: '充值金额'
      t.decimal :gift_amount, precision: 16, scale: 2, default: 0, comment: '赠送金额'
      t.boolean :first_recharge, default: false, comment: '是否首充'
      t.string :order_no, limit: 100, comment: '平台订单号'
      t.references :payment, foreign_key: true, comment: '支付通道'
      t.string :payment_order_no, limit: 100, comment: '支付订单号'
      t.string :ip, limit: 64, comment: 'IP地址'
      t.string :app_type, limit: 50, comment: '应用类型'
      t.integer :status, default: 0, comment: '订单状态'
      t.datetime :callback_time, comment: '回调时间'
      t.datetime :recharge_time, comment: '充值时间'
      t.text :result, comment: '回调结果'
      t.timestamps
    end

    add_index :recharges, :order_no, unique: true
    add_index :recharges, :payment_order_no
  end
end
