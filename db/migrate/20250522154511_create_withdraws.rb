class CreateWithdraws < ActiveRecord::Migration[8.0]
  def change
    create_table :withdraws, id: :bigint, unsigned: true, comment: '提现申请表' do |t|
      t.integer :agent_id, null: false, default: 0, comment: '总代'
      t.integer :channel_id, null: false, default: 0, comment: '渠道'
      t.references :user, foreign_key: true, comment: '用户ID'
      t.string  :order_no, limit: 64, comment: '提现订单号'
      t.string  :username, limit: 100, comment: '用户名'
      t.decimal :amount, precision: 16, scale: 2, null: false, comment: '提现金额'
      t.decimal :fee, precision: 16, scale: 2, default: 0.0, comment: '手续费'
      t.decimal :real_amount, precision: 16, scale: 2, null: false, comment: '实际提现金额'
      t.integer :status, limit: 1, null: false, default: 0, comment: '提现状态'
      t.integer :risk,   limit: 1, default: 0, comment: '封控状态'
      t.datetime :review_time, comment: '审核时间'
      t.datetime :process_time, comment: '处理时间（打款发起时间）'
      t.datetime :finish_time, comment: '完成时间'
      t.string :ip, limit: 45, comment: '申请时用户IP'
      t.references :bank_card, foreign_key: true, comment: '银行卡'
      t.references :payment, foreign_key: true, comment: '支付通道'
      t.string :payment_order_no, limit: 100, comment: '支付订单号'
      t.string :remark, limit: 255, comment: '备注'
      t.text :result, comment: '回调结果'
      t.timestamps
    end

    # 添加索引
    add_index :withdraws, :order_no, unique: true
    add_index :withdraws, :status, name: 'idx_status'
  end
end
