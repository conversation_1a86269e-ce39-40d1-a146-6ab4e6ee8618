class CreateWalletExts < ActiveRecord::Migration[8.0]
  def change
    # 用户钱包扩展表, 用于记录用户获取的各种奖励
    # 这个表应该随着用户创建时跟着用户钱包表一起创建
    create_table :wallet_exts do |t|
      t.bigint :user_id, null: false, comment: '用户ID'
      t.decimal :commission, precision: 16, scale: 2, default: 0, comment: '佣金'
      # 记录充值奖励
      t.decimal :recharge_reward, precision: 16, scale: 2, default: 0, comment: '充值奖励'

      # TODO: 添加其他奖励字段

      t.datetime :updated_at, comment: '最后更新时间'
    end

    add_index :wallet_exts, :user_id, unique: true
  end
end
