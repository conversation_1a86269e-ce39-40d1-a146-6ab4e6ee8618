class CreateAds < ActiveRecord::Migration[8.0]
  def change
    create_table :ads do |t|
      t.string :name, comment: "广告名称"
      t.string :media, comment: "图片"
      t.string :media_type, default: "image", comment: "媒体类型: 图片/视频"
      t.string :type, comment: "单表类型: 大厅广告/弹窗广告/悬浮入口广告"
      t.string :ad_position, comment: "广告位置"
      t.string :redirect_type, comment: "跳转类型"
      t.string :redirect_link, comment: "跳转链接"
      t.string :display_occasion, comment: "展示场合/时机: 每日首次登录/每次刷新"
      t.string :display_platform, comment: "展示平台,暂时无用"
      t.string :visibility, comment: "可见性: 全体/未充值/已充值"
      t.string :created_by, comment: "创建人"
      t.integer :status, default: 1, comment: "状态: 0-下线 1-上线"
      t.integer :order, default: 0, comment: "排序, 越大越靠前"
      t.datetime :expired_at, comment: "过期时间, 为空则永久有效"
      t.timestamps
    end
  end
end
