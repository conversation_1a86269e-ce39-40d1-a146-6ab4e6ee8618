class CreateAgents < ActiveRecord::Migration[8.0]
  def change
    # 代理商
    create_table :agents do |t|
      t.string :name, null: false, comment: '代理商名称'
      t.string :nickname, comment: '代理商昵称'
      t.string :email_address, comment: '邮箱地址'
      t.string :mobile, comment: '手机号'

      t.string :password_digest, null: false, comment: '密码摘要'
      t.string :totp_secret, comment: '2FA密钥'

      t.string :avatar, comment: '头像URL'
      t.string :remark, comment: '备注'
      t.integer :status, default: 0, comment: '状态, 详细定义见代码'

      t.string :last_login_ip, comment: '最后登录IP'
      t.datetime :last_login_at, comment: '最后登录时间'
      t.string :last_login_user_agent, comment: '最后登录的浏览器信息'
      t.integer :login_count, null: false, default: 0, comment: '登录次数'
      t.string :timezone, null: false, default: "UTC", comment: '时区'

      t.string :created_by, comment: '创建人'
      t.datetime :deleted_at, comment: '软删除时间'
      t.timestamps
    end

    add_index :agents, :name, unique: true
  end
end
