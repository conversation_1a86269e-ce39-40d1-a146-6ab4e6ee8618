class CreateSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :settings do |t|
      t.string :key, null: false, comment: '键'
      t.string :label, null: false, comment: '标签'
      t.text :value, comment: '值'
      t.string :value_type, null: false, comment: '值类型'
      t.text :options, comment: '选项'
      t.string :description, comment: '描述'
      t.string :created_by, comment: '创建人'
      t.integer :status, default: 0, comment: '状态, 详细定义见代码'
      t.integer :sort, default: 0, comment: '排序'
      t.string :section, null: false, comment: '分组'
      t.timestamps
    end

    # unique key and section
    add_index :settings, [ :key, :section ], unique: true
  end
end
