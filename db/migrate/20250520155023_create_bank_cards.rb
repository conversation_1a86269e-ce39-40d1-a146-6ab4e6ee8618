class CreateBankCards < ActiveRecord::Migration[8.0]
  def change
    create_table :bank_cards do |t|
      t.references :user, null: false, comment: '关联用户'
      t.string :card_number, null: false, comment: '卡号'
      t.string :card_username, null: false, comment: '卡户名'

      t.string :card_type, null: true, comment: '卡类型'
      t.string :card_bank, null: true, comment: '卡银行'
      t.string :card_province, null: true, comment: '卡省份'
      t.string :card_city, null: true, comment: '卡城市'
      t.string :card_branch, null: true, comment: '卡支行'
      t.string :remark, comment: '备注'
      t.integer :status, default: 0, comment: '状态, 详细定义见代码'
      t.timestamps
    end
  end
end
