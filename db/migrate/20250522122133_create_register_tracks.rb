class CreateRegisterTracks < ActiveRecord::Migration[8.0]
  def change
    create_table :register_tracks do |t|
      t.references :user, null: false, comment: '用户ID'

      t.integer :agent_id, null: false, default: 0, comment: '代理ID'
      t.integer :channel_id, null: false, default: 0, comment: '渠道ID'
      t.integer :parent_id, null: false, default: 0, comment: '上级ID'
      t.integer :super_id, null: false, default: 0, comment: '上上级ID'
      t.integer :super_parent_id, null: false, default: 0, comment: '上上上级ID'
      t.string :ua, null: false, default: "", comment: '浏览器信息'
      t.string :device_id, null: false, default: "", comment: '设备ID'
      t.string :ip, null: false, default: "", comment: 'IP地址'
      t.text :ad_id, null: false, comment: '广告ID'
      t.text :ad_click_id, null: false, comment: '广告点击ID'
      t.string :device_type, null: false, default: "", comment: '设备安装类型:0 H5 1 PWA 2 APK'

      t.timestamps
    end
  end
end
