class CreateUserOrederSummaries < ActiveRecord::Migration[8.0]
  def change
    create_table :user_oreder_summaries do |t|
      t.references :user, null: false, foreign_key: true
      t.integer :total_recharge_count, default: 0, comment: "总入金次数"
      t.decimal :total_recharge_amount, precision: 16, scale: 2, default: 0, comment: "总入金金额"
      t.datetime :first_recharge_at, comment: "首次入金时间"
      t.decimal :first_recharge_amount, precision: 16, scale: 2, default: 0, comment: "首次入金金额"
      t.integer :total_withdraw_count, default: 0, comment: "总出金次数"
      t.decimal :total_withdraw_amount, precision: 16, scale: 2, default: 0, comment: "总出金金额"
      t.datetime :first_withdraw_at, comment: "首次出金时间"
      t.decimal :first_withdraw_amount, precision: 16, scale: 2, default: 0, comment: "首次出金金额"
      t.decimal :recharge_withdraw_diff, comment: "充提差"

      t.timestamps
    end
  end
end
