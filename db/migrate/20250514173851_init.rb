class Init < ActiveRecord::Migration[8.0]
  def change
    create_table :admins do |t|
      t.string :name, null: false, comment: '管理员用户名'
      t.string :nickname, comment: '昵称'
      t.string :email_address, null: false, comment: '邮箱地址'
      t.string :mobile, comment: '手机号'

      t.string :password_digest, null: false, comment: '密码摘要'
      t.string :totp_secret, comment: '2FA密钥，创建管理员时自动生成'

      t.string :department, null: false, default: "game", comment: '部门，详细定义见代码'

      t.string :avatar, comment: '头像URL'
      t.text :remark, comment: '备注'
      t.integer :status, null: false, default: 0, comment: '状态，详细定义见代码'

      t.string :last_login_ip, comment: '最后登录IP'
      t.datetime :last_login_at, comment: '最后登录时间'
      t.string :last_login_user_agent, comment: '最后登录的浏览器信息'
      t.integer :login_count, null: false, default: 0, comment: '登录次数'
      t.string :timezone, null: false, default: "UTC", comment: '时区'

      t.datetime :deleted_at, comment: '软删除时间'
      t.string :created_by, comment: '创建人'
      t.timestamps
    end
    add_index :admins, :name, unique: true
    add_index :admins, :email_address, unique: true

    create_table :roles do |t|
      t.string :name, null: false, comment: '角色名称'
      t.string :code, null: false, comment: '角色编码'
      t.integer :status, null: false, default: 0, comment: '状态，详细定义见代码'
      t.text :description, comment: '角色描述'
      t.timestamps
    end
    add_index :roles, :name, unique: true
    add_index :roles, :code, unique: true

    create_table :admin_roles do |t|
      t.references :admin, null: false, comment: '管理员ID'
      t.references :role, null: false, comment: '角色ID'
      t.timestamps
    end
    add_index :admin_roles, [ :admin_id, :role_id ], unique: true

    create_table :menus do |t|
      t.string :menu_type, null: false, comment: '菜单类型，详细定义见代码'
      t.string :name, null: false, comment: '菜单名称'
      t.string :title, null: false, comment: '菜单标题'
      t.string :icon, comment: '菜单图标'
      t.string :route_name, null: false, comment: '路由名称'
      t.string :path, comment: '路由路径'
      t.string :layout, comment: '布局组件'
      t.string :component, comment: '页面组件'
      t.integer :parent_id, comment: '父级菜单ID'
      t.integer :order, default: 0, comment: '排序'
      t.integer :status, default: 0, comment: '状态，详细定义见代码'
      t.boolean :hidden, default: false, comment: '是否隐藏'
      t.text :description, comment: '菜单描述'
      t.timestamps
    end

    create_table :buttons do |t|
      t.string :name, null: false, comment: '按钮名称'
      t.string :identifier, null: false, comment: '按钮标识符'
      t.integer :status, default: 0, comment: '状态，详细定义见代码'
      t.text :description, comment: '按钮描述'
      t.timestamps
    end
    add_index :buttons, :identifier, unique: true

    create_table :role_menus do |t|
      t.references :role, null: false, foreign_key: true, comment: '角色ID'
      t.references :menu, null: false, foreign_key: true, comment: '菜单ID'
      t.timestamps
    end

    create_table :role_buttons do |t|
      t.references :role, null: false, foreign_key: true, comment: '角色ID'
      t.references :button, null: false, foreign_key: true, comment: '按钮ID'
      t.timestamps
    end
  end
end
