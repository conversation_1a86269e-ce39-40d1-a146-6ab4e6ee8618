class CreatePayments < ActiveRecord::Migration[8.0]
  def change
    create_table :payments do |t|
      # 充值设置
      t.decimal :recharge_fee_rate, precision: 10, scale: 4, default: 0, comment: '支付费率'
      t.decimal :min_recharge, precision: 16, scale: 2, default: 0, comment: '最小充值'
      t.decimal :max_recharge, precision: 16, scale: 2, default: 0, comment: '最大充值'
      t.integer :recharge_weight, default: 0, comment: '充值权重'
      t.string :recharge_method, limit: 50, comment: '充值方法, 根据pay.json中开放的定义来'
      t.references :payment_type, foreign_key: true, comment: '支付类型'
      t.string :payment_way, limit: 50, comment: '支付打开方式'
      t.boolean :recharge_enabled, default: true, comment: '充值开关'

      # 提现设置
      t.decimal :withdraw_fee_rate, precision: 10, scale: 4, default: 0, comment: '提现费率'
      t.decimal :min_withdraw, precision: 16, scale: 2, default: 0, comment: '最小提现'
      t.decimal :max_withdraw, precision: 16, scale: 2, default: 0, comment: '最大提现'
      t.integer :withdraw_weight, default: 0, comment: '提现权重'
      t.boolean :withdraw_enabled, default: false, comment: '提现开关'

      # 基础设置
      t.string :name, limit: 100, comment: '支付名称'
      t.string :page_callback_url, limit: 255, comment: '页面回调'
      t.string :recharge_request_url, limit: 255, comment: '充值请求地址'
      t.string :recharge_callback_url, limit: 255, comment: '充值回调地址, todo: removed'
      t.string :withdraw_request_url, limit: 255, comment: '提现请求地址'
      t.string :withdraw_callback_url, limit: 255, comment: '提现回调地址, todo: removed'
      t.string :ip_whitelist, limit: 255, comment: 'IP白名单，逗号分隔'
      t.text :extras, comment: '额外配置json'
      t.timestamps
    end
  end
end
