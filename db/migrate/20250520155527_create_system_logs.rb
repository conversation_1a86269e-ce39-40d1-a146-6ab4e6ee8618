class CreateSystemLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :system_logs do |t|
      t.references :actor, polymorphic: true, null: false
      t.references :loggable, polymorphic: true, null: false
      t.string :ip, limit: 64, comment: 'IP'
      t.string :user_agent, limit: 255, comment: '用户浏览器信息'
      t.integer :operate, null: false, comment: "操作类型"
      t.string :action, comment: '操作'
      t.text :snapshot, comment: '快照'
      t.datetime :created_at, null: false, comment: '创建时间'
    end

    add_index :system_logs, [ :actor_id, :actor_type ]
  end
end
