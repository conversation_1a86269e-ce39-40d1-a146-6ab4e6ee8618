class CreateBlacklistRecords < ActiveRecord::Migration[8.0]
  def change
    create_table :blacklist_records do |t|
      t.references :user, null: false, foreign_key: true, comment: '用户ID'
      t.integer :ban_type, null: false, comment: "黑名单类型"
      t.string :reason, comment: "黑名单原因"
      t.boolean :actived, default: true, comment: "是否生效"
      t.datetime :expires_at, comment: "自动解封时间"
      t.integer :operator_id, comment: "操作人ID"
      t.string :created_by, comment: "操作人"
      t.timestamps
    end
  end
end
