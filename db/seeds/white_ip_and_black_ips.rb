# Create some initial white IPs
WhiteIp.create!([
  { ip: '***********', remark: 'Internal Network', created_by: 'system', status: 0 },
  { ip: '********', remark: 'Development Server', created_by: 'system', status: 0 },
  { ip: '**********', remark: 'Testing Environment', created_by: 'system', status: 0 }
])

# Create some initial black IPs
BlackIp.create!([
  { ip: '***********00', remark: '刷子', created_by: 'system', status: 0 },
  { ip: '********00', remark: '机器人', created_by: 'system', status: 0 },
  { ip: '************', remark: '大量恶意请求', created_by: 'system', status: 0 }
])
