admin_totp_secret_dev = "3LHGFMFS2I6URF6O5D5N2HXXSAVQP6H6"

30.times do |i|
  Admin.create!(name: "admin#{i}", email_address: "admin#{i}@example.com", password: "password",
    nickname: "管理员#{i}", mobile: "8613012345678", remark: "管理员#{i}",
    department: "admin", totp_secret: admin_totp_secret_dev)
end

marketing_admin = Admin.create!(name: "marketing", email_address: "<EMAIL>", password: "password",
    nickname: "运营呀", mobile: "8613012345678", remark: "运营专员",
    department: "marketing", totp_secret: admin_totp_secret_dev)

marketing_role = Role.find_by(code: "marketing")
marketing_admin.add_role(marketing_role)
