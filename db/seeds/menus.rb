# 清理现有数据
# Menu.destroy_all

# 创建首页菜单
home_menu = Menu.create!(
  name: "home", title: "首页", icon: "mdi:home", order: 0, path: "/home", component: "view.home", layout: "base",
  menu_type: "menu", route_name: "home"
)

# 创建系统管理菜单
system_menu = Menu.create!(
  name: "system", title: "系统管理", icon: "mdi:cog", order: 1, path: "/system", component: nil, layout: "base",
  menu_type: "directory", route_name: "system"
)

logs_menu = Menu.create!(
  name: "log", title: "日志记录", icon: "mdi:cog", order: 1, path: "/log", component: nil, layout: "base",
  menu_type: "directory", route_name: "log"
)

logs_menu = Menu.create!(
  name: "syslog", title: "系统日志", icon: "mdi:cog", order: 1, path: "/syslog", component: nil, layout: "base",
  menu_type: "directory", route_name: "syslog"
)

logs_menu = Menu.create!(
  name: "channel", title: "渠道管理", icon: "mdi:cog", order: 1, path: "/channel", component: nil, layout: "base",
  menu_type: "directory", route_name: "channel"
)

logs_menu = Menu.create!(
  name: "pay", title: "支付管理", icon: "mdi:cog", order: 1, path: "/pay", component: nil, layout: "base",
  menu_type: "directory", route_name: "pay"
)

logs_menu = Menu.create!(
  name: "app", title: "APP管理", icon: "mdi:cog", order: 1, path: "/app", component: nil, layout: "base",
  menu_type: "directory", route_name: "app"
)

logs_menu = Menu.create!(
  name: "game", title: "游戏管理", icon: "mdi:cog", order: 1, path: "/game", component: nil, layout: "base",
  menu_type: "directory", route_name: "game"
)

logs_menu = Menu.create!(
  name: "member", title: "用户管理", icon: "mdi:cog", order: 1, path: "/member", component: nil, layout: "base",
  menu_type: "directory", route_name: "member"
)

logs_menu = Menu.create!(
  name: "report", title: "数据报表", icon: "mdi:cog", order: 1, path: "/report", component: nil, layout: "base",
  menu_type: "directory", route_name: "report"
)

# 创建系统管理子菜单
admin_menu = Menu.create!(
  name: "admin", title: "管理员", icon: "mdi:account-cog", order: 1, path: "/system/admin", component: "view.system_admin", layout: nil,
  menu_type: "menu", route_name: "system_admin", parent: system_menu
)

role_menu = Menu.create!(
  name: "role", title: "角色", icon: "mdi:shield-account", order: 2, path: "/system/role", component: "view.system_role", layout: nil,
  menu_type: "menu", route_name: "system_role", parent: system_menu
)

menu_menu = Menu.create!(
  name: "menu", title: "菜单", icon: "mdi:menu", order: 3, path: "/system/menu", component: "view.system_menu", layout: nil,
  menu_type: "menu", route_name: "system_menu", parent: system_menu
)

button_menu = Menu.create!(
  name: "button", title: "按钮", icon: "mdi:gesture-tap-button", order: 4, path: "/system/button", component: "view.system_button", layout: nil,
  menu_type: "menu", route_name: "system_button", parent: system_menu
)

# 为 admin 角色分配所有菜单
admin_role = Role.find_by(code: "admin")
if admin_role
  [ system_menu, admin_menu, role_menu, menu_menu, button_menu, home_menu ].each do |menu|
    admin_role.menus << menu unless admin_role.menus.include?(menu)
  end
end
