# Import games from csv file
require "csv"

count = 0
Game.transaction do
  game_type = GameType.find_by!(name: "Slot")
  integrator = Integrator.find_by!(name: "好兄弟小游戏")
  file = Rails.root.join("test/fixtures/files/game-list.csv")
  if not File.exist?(file)
    puts "File not found: #{file}"
    return
  end
  CSV.foreach(file, headers: false) do |row|
    platform_id = row[1].to_i
    game_name = row[2].to_s
    game_cover = row[3].to_s
    is_hot = row[5].to_i == 1
    is_top = row[6].to_i == 1
    order = row[8].to_i
    integrator_identity = row[10].to_s
    integrator_game_id = row[11].to_s
    game = Game.new(
      game_type_id: game_type.id,
      integrator_id: integrator.id,
      game_platform_id: platform_id,
      name: game_name,
      hot: is_hot,
      suggested: is_top,
      order: order,
      integrator_identity: integrator_identity,
      integrator_game_id: integrator_game_id,
      created_by: "admin"
    )
    game.fetch_remote_cover(game_cover)
    if game.save
      count += 1
      puts "#{count}: Fetch remote cover: #{game_name}"
    else
      puts "Game error: #{game.errors.full_messages.join(", ")}" if game.errors.any?
    end
  end
end
puts "#{count} Games imported successfully"
