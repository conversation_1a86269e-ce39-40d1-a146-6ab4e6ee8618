[
  { id: 2, name: "Pragmatic Play", created_by: "admin", order: 30, file_name: "PragmaticPlay.png" },
  { id: 3, name: "<PERSON><PERSON><PERSON>", created_by: "admin", order: 4, file_name: "JiLi.png" },
  { id: 13, name: "PG Soft", created_by: "admin", order: 32, file_name: "PGSoft.png" },
  { id: 24, name: "Origin<PERSON>", created_by: "admin", order: 29, file_name: "Originais.png" },
  { id: 28, name: "TT Slot", created_by: "admin", order: 28, file_name: "TT_Slot.png" },
  { id: 29, name: "Rectangle", created_by: "admin", order: 29, file_name: "Rectangle.png" },
  { id: 30, name: "Revenge", created_by: "admin", order: 30, file_name: "Revenge.png" }
].each do |game_platform|
  file_path = Rails.root.join("test/fixtures/files/game_platforms/#{game_platform[:file_name]}")
  game_platform = GamePlatform.new(game_platform.except(:file_name))
  if File.exist?(file_path)
    File.open(file_path, "rb") do |file|
      game_platform.cover_url = file
      game_platform.cover_url_selected = file
    end
  else
    puts "File not found: #{file_path}"
  end
  game_platform.save!
end
