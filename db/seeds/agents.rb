agents = 13
agents.times do |i|
  Agent.create!(
    name: "agent_#{i + 1}",
    nickname: "Agent #{i + 1}",
    email_address: "agent#{i + 1}@example.com",
    mobile: "1#{rand(3..9)}#{rand(100000000..999999999)}",
    password: "password123",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=agent#{i + 1}",
    remark: "Seed agent #{i + 1}",
    status: [ :actived, :inactived ].sample,
    timezone: [ "UTC", "Asia/Shanghai", "America/New_York" ].sample,
    created_by: "system"
  )
end

puts "Created #{agents} agents"
