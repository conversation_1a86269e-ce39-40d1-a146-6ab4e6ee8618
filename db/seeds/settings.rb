# 充值配置
settings = [
  { key: "recharge_default", label: "默认金额", value: 50, value_type: 'integer', description: "默认金额" },
  { key: "recharge_min", label: "充值最小值", value: 50, value_type: 'integer', description: "充值最小值" },
  { key: "recharge_max", label: "充值最大值", value: 500000, value_type: 'integer', description: "充值最大值" },
  { key: "recharge_items", label: "快捷充值项", value: '20,50,100,200,500,1000', value_type: 'string', description: "快捷充值项,多个逗号隔开" },
  { key: "recharge_bet_multiplier", label: "充值本金打码倍数", value: 1, value_type: 'boolean', description: "充值本金打码倍数" },
  { key: "recharge_cpf", label: "充值卡号校验开关", value: 1, value_type: 'boolean', description: "充值卡号校验开关" },
  { key: "recharge_open_input", label: "充值输入框开关", value: 1, value_type: 'boolean', description: "充值输入框开关" }
]

settings.each do |attrs|
  Setting.create!(attrs.merge(section: "recharge"))
end


# 提现配置
settings = [
  { key: "withdraw_min", label: "最小提现", value: "100", value_type: "integer", description: "最小提现", section: "withdraw" },
  { key: "withdraw_max", label: "最大提现", value: "50000", value_type: "integer", description: "最大提现", section: "withdraw" },
  { key: "withdraw_account_limit", label: "账号限制", value: "", value_type: "string", description: "账号限制", section: "withdraw" },
  { key: "withdraw_review_type", label: "提现审核", value: "", value_type: "boolean", description: "自动/人工", section: "withdraw" },
  { key: "withdraw_field_over_amount", label: "字段提款过审金额", value: "", value_type: "integer", description: "字段提款过审金额", section: "withdraw" },
  { key: "withdraw_order_limit", label: "提现下注次数限制", value: "", value_type: "integer", description: "提现下注次数限制", section: "withdraw" },
  { key: "withdraw_to_recharge_multiplier", label: "提现金额与充值金额的倍数", value: "", value_type: "float", description: "提现金额与充值金额的倍数", section: "withdraw" },
  { key: "withdraw_bet_multiplier", label: "提现流水倍数", value: "3", value_type: "float", description: "提现流水倍数", section: "withdraw" },
  { key: "withdraw_bet_multiplier_gt", label: "提现流水倍数（大于）", value: "3", value_type: "float", description: "提现流水倍数（大于）", section: "withdraw" },
  { key: "withdraw_first_manual", label: "首次是否人工", value: "0", value_type: "boolean", description: "首次是否人工", section: "withdraw" },
  { key: "withdraw_first_manual_min", label: "首次金额人工审核范围最小值", value: "200", value_type: "integer", description: "首次金额人工审核范围最小值", section: "withdraw" },
  { key: "withdraw_first_manual_max", label: "首次金额人工审核范围最大值", value: "100000", value_type: "integer", description: "首次金额人工审核范围最大值", section: "withdraw" }
]

settings.each do |s|
  Setting.create!(s)
end

# 注册登录配置: 货币符号/游客注册开关/手机绑定开关/FB绑定开关/同ip最大注册次数/下载奖励/赠送数据打码倍数/注册后下注apk开关/回传设备开关/安卓下载地址/iOS地址/服务条款地址/二维码地址
settings = [
  { key: "currency_symbol", label: "货币符号", value: "", value_type: "string", description: "货币符号的详细描述", section: "register_login" },
  { key: "guest_register_switch", label: "游客注册开关", value: "", value_type: "boolean", description: "游客注册开关的详细描述", section: "register_login" },
  { key: "phone_binding_switch", label: "手机绑定开关", value: "", value_type: "boolean", description: "手机绑定开关的详细描述", section: "register_login" },
  { key: "fb_binding_switch", label: "FB绑定开关", value: "", value_type: "boolean", description: "FB绑定开关的详细描述", section: "register_login" },
  { key: "same_ip_max_register_times", label: "同ip最大注册次数", value: "", value_type: "integer", description: "同ip最大注册次数的详细描述", section: "register_login" },
  { key: "download_reward", label: "下载奖励", value: "", value_type: "integer", description: "下载奖励的详细描述", section: "register_login" },
  { key: "gift_data_code_multiple", label: "赠送数据打码倍数", value: "", value_type: "integer", description: "赠送数据打码倍数的详细描述", section: "register_login" },
  { key: "apk_bet_after_register", label: "注册后下注apk开关", value: "", value_type: "boolean", description: "注册后下注apk开关的详细描述", section: "register_login" },
  { key: "back_device_switch", label: "回传设备开关", value: "", value_type: "boolean", description: "回传设备开关的详细描述", section: "register_login" },
  { key: "android_download_address", label: "安卓下载地址", value: "", value_type: "string", description: "安卓下载地址的详细描述", section: "register_login" },
  { key: "ios_address", label: "iOS地址", value: "", value_type: "string", description: "iOS地址的详细描述", section: "register_login" },
  { key: "service_terms_address", label: "服务条款地址", value: "", value_type: "string", description: "服务条款地址的详细描述", section: "register_login" },
  { key: "qr_code_address", label: "二维码地址", value: "", value_type: "string", description: "二维码地址的详细描述", section: "register_login" }
]

settings.each do |s|
  Setting.create!(s)
end

# 消息配置
# liveChat适配id/TG机器人token/tg频道/错误信息播报/提款播报群ID
settings = [
  { key: "liveChat_adapt_id", label: "liveChat适配id", value: "", value_type: "string", description: "liveChat适配id的详细描述", section: "message" },
  { key: "tg_robot_token", label: "TG机器人token", value: "", value_type: "string", description: "TG机器人token的详细描述", section: "message" },
  { key: "tg_channel", label: "TG频道", value: "", value_type: "string", description: "TG频道的详细描述", section: "message" },
  { key: "error_info_broadcast", label: "错误信息播报", value: "", value_type: "string", description: "错误信息播报的详细描述", section: "message" },
  { key: "withdraw_broadcast_group_id", label: "提款播报群ID", value: "", value_type: "string", description: "提款播报群ID的详细描述", section: "message" }
]

settings.each do |s|
  Setting.create!(s)
end

# 分享配置
# 分享链接/分享标题/分享详情/邀请邮件/联系邮箱/分享图片
settings = [
  { key: "share_link", label: "分享链接", value: "", value_type: "string", description: "分享链接的详细描述", section: "share" },
  { key: "share_title", label: "分享标题", value: "", value_type: "string", description: "分享标题的详细描述", section: "share" },
  { key: "share_detail", label: "分享详情", value: "", value_type: "string", description: "分享详情的详细描述", section: "share" },
  { key: "share_email", label: "邀请邮件", value: "", value_type: "string", description: "邀请邮件的详细描述", section: "share" },
  { key: "share_contact_email", label: "联系邮箱", value: "", value_type: "string", description: "联系邮箱的详细描述", section: "share" },
  { key: "share_image", label: "分享图片", value: "", value_type: "string", description: "分享图片的详细描述", section: "share" }
]

settings.each do |s|
  Setting.create!(s)
end


# 平台配置: 平台标题/平台地址/平台域名/资源域名
settings = [
  { key: "platform_title", label: "平台标题", value: "0", value_type: "string", description: "平台标题的详细描述", section: "platform" },
  { key: "platform_url", label: "平台地址", value: "", value_type: "string", description: "平台地址的详细描述", section: "platform" },
  { key: "platform_domain", label: "平台域名", value: "", value_type: "string", description: "平台域名的详细描述", section: "platform" },
  { key: "platform_assets_domain", label: "资源域名", value: "", value_type: "string", description: "资源域名的详细描述", section: "platform" }
]

settings.each do |s|
  Setting.create!(s)
end
