#!/bin/bash -e

# Enable jemalloc for reduced memory usage and latency.
if [ -z "${LD_PRELOAD+x}" ]; then
    LD_PRELOAD=$(find /usr/lib -name libjemalloc.so.2 -print -quit)
    export LD_PRELOAD
fi

# 检查是否已经配置了凭证
if [ ! -f config/credentials/production.key ]; then
  echo "==============================================="
  echo "欢迎使用 Catalyst Admin Backend!"
  echo "==============================================="
  ./bin/rails credentials:edit --environment=production
  echo ""
fi

# If running the rails server then create or migrate existing database
if [ "${@: -2:1}" == "./bin/rails" ] && [ "${@: -1:1}" == "server" ]; then
  ./bin/rails db:prepare
fi

exec "${@}"
