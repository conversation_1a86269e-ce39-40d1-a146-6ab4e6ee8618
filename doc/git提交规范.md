# Git 提交规范（Conventional Commits）

为了提高项目的可维护性和团队协作效率，推荐使用 **Conventional Commits（约定式提交）** 作为 Git 提交信息的标准格式。

---

## ✅ 提交格式

```bash
<type>(<scope>): <subject>

<body> (可选)
<footer> (可选)
```

---

## ✅ 提交类型（type）

| 类型         | 说明                                         |
| ---------- | ------------------------------------------ |
| `feat`     | 新功能（feature）                               |
| `fix`      | 修复 bug                                     |
| `docs`     | 仅文档变更                                      |
| `style`    | 不影响功能的代码格式修改（空格、缩进、分号等）                    |
| `refactor` | 代码重构（不改变功能、不修复 bug）                        |
| `perf`     | 性能优化                                       |
| `test`     | 添加或修改测试代码                                  |
| `chore`    | 构建过程或辅助工具的变动（不影响源代码或测试）                    |
| `revert`   | 回滚某个提交                                     |
| `build`    | 打包构建相关的变更（如修改 webpack、构建脚本等）               |
| `ci`       | CI 配置相关的变更（如 GitHub Actions、Jenkinsfile 等） |

---

## ✅ 作用域（scope，可选）

用于说明本次提交影响的范围，例如模块名、功能点名：

* `auth`: 登录模块
* `api`: 接口层
* `frontend`: 前端代码
* `liutest`: 测试模块

---

## ✅ 提交示例

### 🎯 新功能

```bash
feat(auth): 添加用户登录接口
```

### 🐛 修复 bug

```bash
fix(agent): 修复代理人信息无法更新的 bug
```

### 📄 修改文档

```bash
docs(readme): 更新项目启动方式说明
```

### 🔧 代码重构

```bash
refactor(liutest): 优化 get_test 接口结构
```

### 🧪 测试代码

```bash
test(roles): 补充角色控制器测试用例
```

---

## ✅ 提交建议

1. 保持简洁清晰，一行不超过 72 字符。
2. 如果需要详细说明变更内容，在 `body` 中补充。
3. 避免中英文混写混乱，保持统一风格。


