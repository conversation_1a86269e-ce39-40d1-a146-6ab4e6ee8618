## Redis Stream 命令

### 创建 Stream 和消费者组
```bash
# 连接到 Redis
redis-cli

# 创建 Stream 和消费者组
# 0 表示从头开始消费所有历史消息 —— 包括最早的那一条
XGROUP CREATE game_actions game_actions_group 0 MKSTREAM

# $ 只处理创建消费者组之后新写入的消息，忽略历史消息
XGROUP CREATE game_actions game_actions_group $ MKSTREAM
```

### 发送测试消息
```bash
# 发送一条游戏动作消息
XADD game_actions * data '{"user_id":"user123","timestamp":1234567890,"action_type":"login","game_id":"game1","detail":"user login"}'
```

### 查看 Stream 消息
```bash
# 查看 Stream 中的所有消息
XRANGE game_actions - +

# 查看消费者组信息
XINFO GROUPS game_actions

# 查看消费者信息
XINFO CONSUMERS game_actions game_actions_group

通过这个命令可以查看到当前的消费者列表，包括消费者的名称/pending数量/空闲时长

# 查看未确认的消息
XPENDING game_actions game_actions_group
```

### 消费者组读取消息
```bash
# 使用消费者组读取消息
XREADGROUP GROUP game_actions_group game_actions_consumer COUNT 1 STREAMS game_actions >
```

### 确认消息处理完成
```bash
# 确认消息处理完成（替换 {message-id} 为实际的消息 ID）
XACK game_actions game_actions_group {message-id}
```

### 清空消息队列

```bash
# 删除整个 Stream
DEL game_actions

# 或者，如果只想删除所有消息但保留 Stream 和消费者组
XTRIM game_actions MAXLEN 0

```