# Deploy

Deploy 分为两种 docker 和 capistrano

docker 部署


echo **************************************** | docker login ghcr.io -u 用户名 --password-stdin

```
cp env.sample .env
docker compose pull
docker compose down
docker compose up -d
```

常用命令

```
docker compose run web

docker volume ls
docker volume prune && docker volume rm catalyst-admin_mysql-data
docker exec -it catalyst-admin-db-1 bash
docker exec -it catalyst-admin-web-1 bash
docker rm -f $(docker ps -a -q)

sudo docker cp catalyst-admin-web-1:/rails/public/. /var/www/bms_assets/
sudo chown -R caddy:caddy /var/www/bms_assets/
sudo tail -f /var/log/caddy/bms.access.log
sudo tail -f /var/log/caddy/assets.access.log
```

## caddy config

bms的 caddy 的配置文件位于 /etc/caddy/Caddyfile, 对应到项目中的 doc/deploy/Caddyfile

sudo systemctl start caddy
sudo systemctl restart caddy

## capistrano deploy

```
bundle exec cap production deploy
```
sudo vim ~/.config/systemd/user/puma.service
systemctl --user daemon-reload
systemctl --user status puma.service
systemctl --user start puma.service
systemctl --user stop puma.service
ps -ef | grep puma

## Update

cd ~/catalyst-admin/
docker compose pull
docker compose down
docker compose up -d
sudo docker cp catalyst-admin-web-1:/rails/public/. /var/www/bms_assets/
sudo chown -R caddy:caddy /var/www/bms_assets/
RAILS_ENV=production bundle exec rails s -p 3010