# 认证接口文档

## 登录接口

### 接口说明
- 接口路径：`/api/v1/auth/login`
- 请求方式：POST
- 接口描述：管理员登录接口，支持用户名密码和TOTP双因素认证

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_name | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| totp | string | 否 | TOTP验证码（生产环境必填） |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.token | string | JWT令牌 |
| data.refresh_token | string | 刷新令牌 |

### 响应示例
```json
{
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiJ9..."
  },
  "code": "0000",
  "msg": "请求成功"
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名或密码错误 |
| 1002 | 账户已锁定 |
| 1003 | 账户已删除 |
| 1004 | 请输入TOTP验证码 |
| 1005 | TOTP验证码错误 |

## 获取用户信息

### 接口说明
- 接口路径：`/api/v1/auth/user_info`
- 请求方式：GET
- 接口描述：获取当前登录用户的信息，包括用户ID、用户名、角色和权限按钮

### 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer token |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.user_id | string | 用户ID |
| data.user_name | string | 用户名 |
| data.roles | array | 角色列表 |
| data.buttons | array | 可访问的按钮权限列表 |

### 响应示例
```json
{
  "data": {
    "user_id": "1",
    "user_name": "admin",
    "roles": ["admin"],
    "buttons": ["add", "edit", "delete"]
  },
  "code": "0000",
  "msg": "请求成功"
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 8888 | 登录信息未找到 |
| 8889 | 登录信息已过期 | 