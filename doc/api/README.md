# API 接口文档

## 1. 接口规范

### 1.1 版本控制
- API 采用版本化设计，当前版本为 v1
- 版本号体现在URL路径中：`/api/v1/...`
- 未来版本升级将使用新的版本号，如 `/api/v2/...`

### 1.2 RESTful 风格
- 遵循 REST 架构风格
- 使用 HTTP 方法表示操作类型：
  - GET：查询资源
  - POST：创建资源
  - PUT：更新资源
  - DELETE：删除资源
- 资源使用复数形式，如 `/users`、`/orders`

### 1.3 模块划分
- API 按业务模块进行划分
- 示例：用户模块 `/api/v1/user/users`
- 每个模块的详细接口文档将在单独的 markdown 文件中说明

## 2. 响应格式

### 2.1 统一响应结构
```json
{
    "code": "0000",
    "msg": "请求成功",
    "data": {
        // 具体的业务数据
    }
}
```

### 2.2 状态码说明
- 0000：成功
- 1000-1999：客户端错误
- 2000-2999：服务端错误
- 3000-3999：业务逻辑错误

## 3. 通用字段说明

### 3.1 通用基础字段
- `id`：主键，唯一标识符
- `created_by`: 记录创建人
- `created_at`：记录创建时间，格式：ISO 8601
- `updated_at`：记录更新时间，格式：ISO 8601

### 3.2 分页参数

请求参数：

响应参数：current用于显示页码(默认值1)，size 用于控制每页显示数量(默认值20, 最大不超过2000)
- `current`：当前页码，从1开始
- `size`：每页记录数
- `pages`: 总页数
- `total`：总记录数

## 4. 命名规范

### 4.1 URL命名
- 使用小写字母
- 单词之间使用下划线连接
- 示例：`/api/v1/user_profile`

### 4.2 参数命名
- 使用小写字母
- 单词之间使用下划线连接
- 示例：`user_name`、`created_at`

## 5. 安全规范

### 5.1 认证
- 使用 JWT Token 进行身份认证
- Token 通过 Authorization header 传递
- 格式：`Authorization: Bearer <token>`

### 5.2 权限控制
- 基于 RBAC 模型进行权限管理
- 接口访问需要进行权限校验

## 6. 错误处理

### 6.1 错误响应格式
```json
{
    "code": "1001",
    "msg": "错误描述信息",
    "data": null
}
```

### 6.2 常见错误码
- 0001：参数错误
- 8888：未授权
- 4030：禁止访问
- 2001：服务器内部错误

## 7. 接口文档维护

### 7.1 文档更新
- 接口变更需要同步更新文档
- 重大变更需要标注版本号
- 废弃的接口需要标注废弃时间

### 7.2 文档格式
- 使用 Markdown 格式
- 每个模块的接口文档单独存放
- 包含接口描述、请求参数、响应示例等信息

## 8. 模块文档

### 8.1 认证模块
- [认证接口文档](auth.md)
  - 登录
  - 获取用户信息

### 8.2 管理员模块
- [管理员接口文档](admins.md)
  - 管理员列表
  - 管理员详情
  - 创建管理员
  - 更新管理员
  - 删除管理员

### 8.3 角色模块
- [角色接口文档](roles.md)
  - 角色列表
  - 角色详情
  - 创建角色
  - 更新角色
  - 角色菜单管理

### 8.4 菜单模块
- [菜单接口文档](menus.md)
  - 菜单列表
  - 菜单树
  - 更新菜单
  - 删除菜单
  - 批量删除菜单