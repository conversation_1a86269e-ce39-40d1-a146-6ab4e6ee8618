# 角色接口文档

## 获取角色列表

### 接口说明
- 接口路径：`/api/v1/roles`
- 请求方式：GET
- 接口描述：获取角色列表，支持分页和条件筛选

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | integer | 否 | 当前页码，默认1 |
| size | integer | 否 | 每页记录数，默认10 |
| role_name | string | 否 | 角色名称，支持模糊搜索 |
| role_code | string | 否 | 角色代码，支持模糊搜索 |
| status | integer | 否 | 状态（1:启用 2:禁用） |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.records | array | 角色列表 |
| data.records[].id | integer | 角色ID |
| data.records[].role_name | string | 角色名称 |
| data.records[].role_code | string | 角色代码 |
| data.records[].role_desc | string | 角色描述 |
| data.records[].status | integer | 状态（1:启用 2:禁用） |
| data.total | integer | 总记录数 |
| data.size | integer | 每页记录数 |
| data.current | integer | 当前页码 |

### 响应示例
```json
{
  "code": "0000",
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "role_name": "管理员",
        "role_code": "admin",
        "role_desc": "系统管理员",
        "status": 1
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

## 获取所有角色

### 接口说明
- 接口路径：`/api/v1/roles/all`
- 请求方式：GET
- 接口描述：获取所有启用的角色列表

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | array | 角色列表 |
| data[].id | integer | 角色ID |
| data[].role_name | string | 角色名称 |
| data[].role_code | string | 角色代码 |

## 创建角色

### 接口说明
- 接口路径：`/api/v1/roles`
- 请求方式：POST
- 接口描述：创建新的角色

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| role_name | string | 是 | 角色名称 |
| role_code | string | 是 | 角色代码 |
| role_desc | string | 否 | 角色描述 |
| menu_ids  | array | 否 | 角色拥有的菜单ID|

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.id | integer | 角色ID |
| data.role_name | string | 角色名称 |
| data.role_code | string | 角色代码 |
| data.role_desc | string | 角色描述 |
| data.status | integer | 状态（1:启用 2:禁用） |

## 更新角色

### 接口说明
- 接口路径：`/api/v1/roles/:id`
- 请求方式：PUT
- 接口描述：更新角色信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 角色ID |
| role_name | string | 是 | 角色名称 |
| role_code | string | 是 | 角色代码 |
| role_desc | string | 否 | 角色描述 |
| status | integer | 否 | 状态（1:启用 2:禁用） |

### 响应参数
同创建角色接口

## 获取角色菜单

### 接口说明
- 接口路径：`/api/v1/roles/:id/menus`
- 请求方式：GET
- 接口描述：获取角色关联的菜单ID列表

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 角色ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | array | 菜单ID列表 |

## 更新角色菜单

### 接口说明
- 接口路径：`/api/v1/roles/:id/menus`
- 请求方式：PUT
- 接口描述：更新角色关联的菜单

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 角色ID |
| menu_ids | array | 是 | 菜单ID列表 |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0001 | 参数错误或操作失败 | 

## 删除角色

### 接口说明
- 接口路径：`/api/v1/roles/:id`
- 请求方式：DELETE
- 接口描述：删除指定ID的角色

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 角色ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

### 响应示例
```json
{
  "code": "0000",
  "msg": "success",
  "data": null
}
``` 