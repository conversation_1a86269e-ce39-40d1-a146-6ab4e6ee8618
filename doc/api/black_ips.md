# IP黑名单接口文档

## 获取IP黑名单列表

### 接口说明
- 接口路径：`/api/v1/black_ips`
- 请求方式：GET
- 接口描述：获取IP黑名单列表，支持分页

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | integer | 否 | 当前页码，默认1 |
| size | integer | 否 | 每页记录数，默认20, 最大200 |
| search | string | 否 | 用来模糊匹配IP |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.current | integer | 当前页码 |
| data.size | integer | 每页记录数 |
| data.total | integer | 总记录数 |
| data.pages | integer | 总页数 |
| data.records | array | IP黑名单列表 |
| data.records[].id | integer | 记录ID |
| data.records[].ip | string | IP地址 |
| data.records[].remark | string | 备注 |
| data.records[].created_at | string | 创建时间 |

### 响应示例
```json
{
    "code": "0000",
    "msg": "请求成功",
    "data": {
        "current": 1,
        "total": 2,
        "size": 20,
        "pages": 1,
        "records": [
            {
                "id": 1,
                "ip": "*************",
                "remark": "Suspicious Activity",
                "created_at": "2024-03-20 14:21:23"
            },
            {
                "id": 2,
                "ip": "**********",
                "remark": "Malicious Traffic",
                "created_at": "2024-03-20 14:22:24"
            }
        ]
    }
}
```

## 获取IP黑名单详情

### 接口说明
- 接口路径：`/api/v1/black_ips/:id`
- 请求方式：GET
- 接口描述：获取指定IP黑名单的详细信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 记录ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.id | integer | 记录ID |
| data.ip | string | IP地址 |
| data.remark | string | 备注 |
| data.created_at | string | 创建时间 |

### 响应示例
```json
{
    "code": "0000",
    "msg": "success",
    "data": {
        "id": 1,
        "ip": "*************",
        "remark": "Suspicious Activity",
        "created_at": "2024-03-20 14:21:23"
    }
}
```

## 创建IP黑名单

### 接口说明
- 接口路径：`/api/v1/black_ips`
- 请求方式：POST
- 接口描述：添加新的IP到黑名单

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| black_ip[ip] | string | 是 | IP地址 |
| black_ip[remark] | string | 否 | 备注 |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.id | integer | 记录ID |
| data.ip | string | IP地址 |
| data.remark | string | 备注 |
| data.created_at | string | 创建时间 |

### 响应示例
```json
{
    "code": "0000",
    "msg": "success",
    "data": {
        "id": 1,
        "ip": "*************",
        "remark": "Suspicious Activity",
        "created_at": "2024-03-20 14:21:23"
    }
}
```

## 更新IP黑名单

### 接口说明
- 接口路径：`/api/v1/black_ips/:id`
- 请求方式：PATCH
- 接口描述：更新IP黑名单信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 记录ID |
| black_ip[ip] | string | 是 | IP地址 |
| black_ip[remark] | string | 否 | 备注 |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.id | integer | 记录ID |
| data.ip | string | IP地址 |
| data.remark | string | 备注 |
| data.created_at | string | 创建时间 |

### 响应示例
```json
{
    "code": "0000",
    "msg": "请求成功",
    "data": {
        "id": 1,
        "ip": "*************",
        "remark": "Updated Remark",
        "created_at": "2024-03-20 14:21:23"
    }
}
```

## 删除IP黑名单

### 接口说明
- 接口路径：`/api/v1/black_ips/:id`
- 请求方式：DELETE
- 接口描述：从黑名单中删除IP

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 记录ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

### 响应示例
```json
{
    "code": "0000",
    "msg": "请求成功",
    "data": null
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0000 | 成功 |
| 0001 | 参数错误或操作失败 |
| 4040 | 记录不存在 |
| 8888 | 未授权访问 | 