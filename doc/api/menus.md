# 菜单接口文档

## 获取菜单列表

### 接口说明
- 接口路径：`/api/v1/menus`
- 请求方式：GET
- 接口描述：获取菜单列表，以树形结构返回

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.records | array | 菜单树形列表 |
| data.records[].id | integer | 菜单ID |
| data.records[].parent_id | integer | 父菜单ID，0表示根菜单 |
| data.records[].menu_type | string | 菜单类型（1:目录 2:菜单） |
| data.records[].menu_name | string | 菜单名称 |
| data.records[].route_name | string | 路由名称 |
| data.records[].route_path | string | 路由路径 |
| data.records[].component | string | 组件路径 |
| data.records[].icon | string | 图标 |
| data.records[].icon_type | string | 图标类型 |
| data.records[].order | integer | 排序号 |
| data.records[].status | integer | 状态 |
| data.records[].status_name | string | 状态 |
| data.records[].hide_in_menu | boolean | 是否在菜单中隐藏 |
| data.records[].created_by | string | 创建人 |
| data.records[].created_at | string | 创建时间 |
| data.records[].updated_at | string | 更新时间 |
| data.records[].children | array | 子菜单列表 |
| data.total | integer | 总记录数 |
| data.size | integer | 每页记录数 |
| data.current | integer | 当前页码 |

### 响应示例
```json
{
  "code": "0000",
  "msg": "请求成功",
  "data": {
    "records": [
      {
        "id": 1,
        "parent_id": 0,
        "menu_type": "1",
        "menu_name": "系统管理",
        "route_name": "system",
        "route_path": "/system",
        "component": "Layout",
        "icon": "setting",
        "icon_type": "1",
        "order": 1,
        "status": 1,
        "status_name": "active",
        "hide_in_menu": false,
        "created_by": "admin",
        "created_at": "2024-01-01 00:00:00",
        "update_time": "2024-01-01 00:00:00",
        "children": []
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

## 获取菜单树

### 接口说明
- 接口路径：`/api/v1/menus/tree`
- 请求方式：GET
- 接口描述：获取简化的菜单树形结构

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | array | 菜单树形列表 |
| data[].id | integer | 菜单ID |
| data[].label | string | 菜单名称 |
| data[].p_id | integer | 父菜单ID，0表示根菜单 |
| data[].children | array | 子菜单列表 |

## 更新菜单

### 接口说明
- 接口路径：`/api/v1/menus/:id`
- 请求方式：PUT
- 接口描述：更新菜单信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 菜单ID |
| menu_name | string | 是 | 菜单名称 |
| route_name | string | 是 | 路由名称 |
| route_path | string | 是 | 路由路径 |
| component | string | 是 | 组件路径 |
| icon | string | 否 | 图标 |
| menu_type | string | 是 | 菜单类型（1:目录 2:菜单） |
| order | integer | 否 | 排序号 |
| status | string | 否 | 状态（1:启用 2:禁用） |
| hide_in_menu | boolean | 否 | 是否在菜单中隐藏 |
| parent_id | integer | 否 | 父菜单ID，0表示根菜单 |

### 响应参数
同获取菜单列表的单个记录格式

## 删除菜单

### 接口说明
- 接口路径：`/api/v1/menus/:id`
- 请求方式：DELETE
- 接口描述：删除指定菜单

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 菜单ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

## 批量删除菜单

### 接口说明
- 接口路径：`/api/v1/menus/batch_destroy`
- 请求方式：DELETE
- 接口描述：批量删除菜单

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 菜单ID列表 |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0001 | 参数错误或操作失败 | 