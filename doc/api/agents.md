# 代理商接口文档

### 接口说明
- 接口路径：`/api/v1/agents`
- 请求方式：GET
- 请求参数: size 参数控制每页数量， current 用于控制页码
- 接口描述：获取代理商列表，支持分页

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | integer | 否 | 当前页码，默认1 |
| size | integer | 否 | 每页记录数，默认20，最大200 |

### 响应结果

```json
{
    "code": "0000",
    "data": {
        "current": 1,
        "pages": 7,
        "size": 2,
        "total": 13
        "records": [
            {
                "email_address": "<EMAIL>",
                "id": 1,
                "mobile": "18482983305",
                "name": "agent_1",
                "nickname": "Agent 1",
                "remark": "Seed agent 1",
                "status": 1,
                "status_name": "active",
                "created_at": "",
                "created_by": "admin"
            },
            {
                "email_address": "<EMAIL>",
                "id": 2,
                "mobile": "13527243209",
                "name": "agent_2",
                "nickname": "Agent 2",
                "remark": "Seed agent 2",
                "status": 1,
                "status_name": "active",
                "created_at": "",
                "created_by": "admin"
            }
        ]
    },
    "msg": "请求成功"
}
```

### 创建代理商
- 接口路径：`/api/v1/agents`
- 请求方式：POST
- 接口描述：创建新的代理商

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 代理商名称 |
| nickname | string | 是 | 代理商昵称 |

### 响应结果
```json
{
    "code": "0000",
    "data": {
        "id": 1,
        "name": "New Agent",
        "nickname": "New",
        "status": 1,
        "status_name": "active"
    },
    "msg": "请求成功"
}
```

### 更新代理商
- 接口路径：`/api/v1/agents/:id`
- 请求方式：PUT
- 接口描述：更新指定代理商信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 代理商名称 |
| nickname | string | 否 | 代理商昵称 |

### 响应结果
```json
{
    "code": "0000",
    "data": {
        "id": 1,
        "name": "Updated Agent",
        "nickname": "Updated",
        "status": 1
    },
    "msg": "请求成功"
}
```

### 错误响应
当请求参数无效时，将返回以下格式的响应：
```json
{
    "code": "0001",
    "data": null,
    "msg": "参数错误"
}
```