# 管理员接口文档

## 获取管理员列表

### 接口说明
- 接口路径：`/api/v1/admins`
- 请求方式：GET
- 接口描述：获取管理员列表，支持分页和包含已删除的管理员

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | integer | 否 | 当前页码，默认1 |
| size | integer | 否 | 每页记录数，默认20, 最大2000 |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | object | 响应数据 |
| data.current | integer | 当前页码 |
| data.size | integer | 每页记录数 |
| data.total | integer | 总记录数 |
| data.records | array | 管理员列表 |
| data.records[].id | integer | 管理员ID |
| data.records[].name | string | 用户名 |
| data.records[].nickname | string | 昵称 |
| data.records[].status | integer | 状态 |
| data.records[].status_name | string | 状态（1:正常 2:锁定 3:已删除） |
| data.records[].roles | array | 角色列表 |
| data.records[].created_by | string | 创建人 |
| data.records[].login_count | integer | 登录次数 |
| data.records[].last_login_at | string | 最后登录时间 |
| data.records[].last_login_ip | string | 最后登录IP |
| data.records[].created_at | string | 创建时间 |

### 响应示例
```json
{
    "code": "0000",
    "data": {
        "current": 1,
        "pages": 1,
        "records": [
            {
                "created_at": "2025-05-22 16:46:07",
                "created_by": null,
                "email": "<EMAIL>",
                "id": 1,
                "last_login_at": null,
                "last_login_ip": null,
                "login_count": 0,
                "mobile": "8613512345678",
                "nickname": "管理员呀",
                "remark": "管理员",
                "roles": [
                    {
                        "code": "admin",
                        "id": 1,
                        "name": "admin"
                    },
                    {
                        "code": "staff",
                        "id": 2,
                        "name": "staff"
                    },
                    {
                        "code": "finance",
                        "id": 3,
                        "name": "finance"
                    },
                    {
                        "code": "operation",
                        "id": 4,
                        "name": "operation"
                    },
                    {
                        "code": "hr",
                        "id": 5,
                        "name": "hr"
                    },
                    {
                        "code": "marketing",
                        "id": 6,
                        "name": "marketing"
                    },
                    {
                        "code": "tech",
                        "id": 7,
                        "name": "tech"
                    },
                    {
                        "code": "legal",
                        "id": 8,
                        "name": "legal"
                    }
                ],
                "status": 0,
                "status_name": "active",
                "username": "admin"
            },
            {
                "created_at": "2025-05-22 16:46:08",
                "created_by": null,
                "email": "<EMAIL>",
                "id": 2,
                "last_login_at": null,
                "last_login_ip": null,
                "login_count": 0,
                "mobile": "8613012345678",
                "nickname": "运营呀",
                "remark": "运营专员",
                "roles": [
                    {
                        "code": "marketing",
                        "id": 6,
                        "name": "marketing"
                    }
                ],
                "status": 0,
                "status_name": "active",
                "username": "marketing"
            }
        ],
        "size": 20,
        "total": 2
    },
    "msg": "请求成功"
}
```

## 获取管理员详情

### 接口说明
- 接口路径：`/api/v1/admins/:id`
- 请求方式：GET
- 接口描述：获取指定管理员的详细信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 管理员ID |

### 响应参数
同列表接口的单个记录格式

## 创建管理员

### 接口说明
- 接口路径：`/api/v1/admins`
- 请求方式：POST
- 接口描述：创建新的管理员账号

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| admin[name] | string | 是 | 用户名 |
| admin[nickname] | string | 否 | 昵称 |
| admin[password] | string | 是 | 密码 |
| admin[mobile] | string | 否 | 手机号 |
| admin[department] | string | 否 | 部门 |
| admin[status] | string | 否 | 状态 |
| admin[avatar] | string | 否 | 头像 |
| admin[remark] | string | 否 | 备注 |
| admin[role_ids] | array | 否 | 角色代码列表 |

### 响应参数
同列表接口的单个记录格式

```json
{
    "code": "0000",
    "data": {
        "created_at": "2025-05-20 14:21:23",
        "created_by": null,
        "deleted_at": null,
        "email": "<EMAIL>",
        "id": 1,
        "last_login_at": null,
        "last_login_ip": null,
        "login_count": 0,
        "mobile": "8613512345678",
        "nickname": "管理员呀",
        "remark": "管理员",
        "roles": [
            {
                "code": "marketing",
                "id": 6,
                "name": "marketing"
            }
        ],
        "status": 0,
        "updated_at": "2025-05-20 14:21:23",
        "username": "admin"
    },
    "msg": "请求成功"
}
```

## 更新管理员

### 接口说明
- 接口路径：`/api/v1/admins/:id`
- 请求方式：PUT
- 接口描述：更新管理员信息

### 请求参数
同创建管理员接口

### 响应参数
同列表接口的单个记录格式

## 删除管理员

### 接口说明
- 接口路径：`/api/v1/admins/:id`
- 请求方式：DELETE
- 接口描述：软删除管理员账号

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 管理员ID |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | string | 状态码 |
| msg | string | 状态信息 |
| data | null | 响应数据 |

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 1001 | 参数错误或操作失败 | 