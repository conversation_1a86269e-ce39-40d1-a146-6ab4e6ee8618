bms.sancatalyst.com {
    log {
        output file /var/log/caddy/bms.access.log
        format json
    }

    @static {
        path /assets/* *.ico *.css *.js *.gif *.jpg *.jpeg *.png *.svg *.woff *.woff2            
    }

    handle @static {
        root * /var/www/bms_assets 
        file_server
        header Cache-Control "public, max-age=31536000"
    }

    reverse_proxy localhost:3000    
    tls <EMAIL>
}

front-api.sancatalyst.com {
    log {
        output file /var/log/caddy/front-api.access.log
        format json
    }

    reverse_proxy localhost:3001
    tls <EMAIL>
}

bms-dev.sancatalyst.com {
    log {
        output file /var/log/caddy/bms-dev.access.log
        format json
    }

    @static {
        path /assets/* *.ico *.css *.js *.gif *.jpg *.jpeg *.png *.svg *.woff *.woff2            
    }

    handle @static {
        root * /var/www/bms/public
        file_server
        header Cache-Control "public, max-age=31536000"
    }

    reverse_proxy localhost:3010
    tls <EMAIL>
}

assets.sancatalyst.com {
    tls <EMAIL>
    root * /var/www/bms_assets
    file_server

    log {
        output file /var/log/caddy/assets.access.log
        format json
    }

    header {
        Cache-Control "public, max-age=31536000, immutable"
        Access-Control-Allow-Origin "https://bms.sancatalyst.com"
        Access-Control-Allow-Methods "GET, OPTIONS"
        Access-Control-Allow-Headers "Origin, Content-Type, Accept"
        Access-Control-Allow-Credentials "true"
        Vary "Origin"
    }

    @preflight {
        method OPTIONS
        path /assets/*
    }
    respond @preflight 204
    try_files {path} /404.html
}