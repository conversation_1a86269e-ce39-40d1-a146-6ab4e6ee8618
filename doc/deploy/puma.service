[Unit]
Description=Puma HTTP Server for myapp
After=network.target

[Service]
Type=simple
WorkingDirectory=/var/www/bms/current

Environment=RAILS_ENV=production
Environment=BUNDLE_GEMFILE=/var/www/bms/current/Gemfile

ExecStart=/home/<USER>/.rbenv/bin/rbenv exec bundle exec puma -p 3010 --pidfile tmp/pids/puma.pid

Restart=always
RestartSec=10s

StandardOutput=journal

[Install]
WantedBy=multi-user.target