# 产品需求文档 (PRD)

## 项目概述

**版本**: 1.0  
**创建日期**: 2025年5月15日  
**目标**: 构建一个基于Ruby on Rails的游戏业务运营平台，支持用户注册、充值、下注、提现等功能，引入打码量机制以控制提现条件，提升用户参与度和平台安全性。

本项目旨在提供一个安全、易用的游戏业务运营平台，用户通过充值获得余额，参与多种游戏的下注，累积打码量以解锁提现权限。系统支持奖励机制，鼓励用户充值和游戏参与。

----

## 核心需求

### 1. 用户管理
- **注册与登录**: 用户可以通过用户名、邮箱和密码注册账户，并通过邮箱和密码登录。
- **账户信息**: 用户拥有一个资金账户，记录余额、已累积打码量和所需打码量。
- **安全性**: 密码加密存储，支持基本的账户安全措施（如密码重置）。

### 2. 充值与奖励
- **充值**: 用户可通过支付渠道为账户充值（如10元），充值金额直接增加账户余额。
- **奖励机制**: 充值时可获得额外奖励（如充值10元送5元），奖励金额计入余额但需满足打码量要求才能提现。
- **打码量计算**: 
  - 充值金额直接计入打码量（如充值10元，打码量+10）。
  - 奖励金额按倍率计算打码量（如奖励5元，倍率2.0，打码量+5*2=10）。
  - 总打码量 = 充值金额 + 奖励金额 * 倍率。
- **记录**: 每次充值生成记录，包含充值金额、奖励金额、倍率和贡献的打码量。

### 3. 游戏下注
- **下注**: 用户在游戏中下注（如下注5元），下注金额从余额扣除，累加到已打码量。
- **输赢结算**: 每次下注后根据游戏结果计算输赢（如赢2元或输5元），更新账户余额。
- **游戏类型**: 支持多种游戏（如老虎机、扑克），每种游戏记录下注金额和输赢结果。
- **记录**: 每次下注生成记录，包含下注金额、输赢结果和游戏类型。

### 4. 提现
- **提现请求**: 用户可申请提现账户余额，需指定提现金额。
- **提现条件**: 
  - 提现金额大于某一个阈值。
  - 账户余额需大于或等于提现金额。
  - 已累积打码量需大于或等于所需打码量。
- **审批流程**: 系统自动检查提现条件，满足则通过，否则拒绝并说明原因（如“打码量不足”）。
- **记录**: 每次提现生成记录，包含金额、状态（待处理、通过、拒绝）和拒绝原因。