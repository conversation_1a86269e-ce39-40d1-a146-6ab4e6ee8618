#!/bin/bash

# Script to install Git hooks for the project
# This script sets up pre-commit hooks to automatically format code

set -e

echo "🔧 Installing Git hooks for catalyst-admin-backend..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Error: Not in a Git repository. Please run this script from the project root."
    exit 1
fi

# Create hooks directory if it doesn't exist
mkdir -p .git/hooks

# Install pre-commit hook
echo "📝 Installing pre-commit hook..."

cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# Pre-commit hook to format code before committing
# This hook runs ./script/format_code.sh before each commit

set -e

echo "🔧 Running code formatter before commit..."

# Check if format_code.sh exists
if [ ! -f "./script/format_code.sh" ]; then
    echo "❌ Error: ./script/format_code.sh not found"
    exit 1
fi

# Make sure the script is executable
chmod +x ./script/format_code.sh

# Run the format script
echo "📝 Formatting code with RuboCop..."
./script/format_code.sh

# Check if there are any changes after formatting
if ! git diff --quiet; then
    echo "📋 Code formatting made changes. Adding formatted files to commit..."
    
    # Add all modified files to the staging area
    git add -A
    
    echo "✅ Code formatted and changes staged for commit"
else
    echo "✅ No formatting changes needed"
fi

echo "🚀 Pre-commit hook completed successfully"
EOF

# Make the hook executable
chmod +x .git/hooks/pre-commit

# Make sure format_code.sh is executable
chmod +x script/format_code.sh

echo "✅ Git hooks installed successfully!"
echo ""
echo "📋 What was installed:"
echo "  - Pre-commit hook: Automatically runs ./script/format_code.sh before each commit"
echo ""
echo "🎯 Usage:"
echo "  - The hook will run automatically on every 'git commit'"
echo "  - To skip the hook temporarily: git commit --no-verify"
echo "  - To run formatting manually: ./script/format_code.sh"
echo ""
echo "🚀 You're all set! Code will be automatically formatted before each commit."
