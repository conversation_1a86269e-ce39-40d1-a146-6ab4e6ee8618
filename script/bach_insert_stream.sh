#!/bin/bash

# 连接到 Redis 并批量插入消息
echo "Starting batch insertion of 5000 messages..."

# 使用管道批量插入消息
for i in {1..20}; do
  user_id="28"
  bet="100"
  real_win="80" 
  win="-10"
  game_number="game_${i}"
  round_id="round_${i}"
  before_money="1000"
  after_money="990"
  # bet_time need to be random between current week start and end
  bet_time=$(date -d "last week" +%s)
  bet_time=$((bet_time + $RANDOM % 1000000))

  redis-cli XADD game_bet_stream "*" \
    user_id "$user_id" \
    bet "$bet" \
    real_win "$real_win" \
    win "$win" \
    game_number "$game_number" \
    round_id "$round_id" \
    before_money "$before_money" \
    after_money "$after_money" \
    bet_time "$bet_time" > /dev/null

  # 每1000条消息显示一次进度
  if (( i % 1000 == 0 )); then
    echo "Inserted $i messages..."
  fi
done

echo "Batch insertion completed"
