#!/usr/bin/env ruby
# 添加测试消息到 Redis Stream
# 使用方法: rails runner script/add_test_messages.rb

require_relative "../config/environment"

def get_integer_input(prompt, min: nil, max: nil)
  loop do
    print "#{prompt}: "
    input = gets.chomp.strip
    return nil if input.empty?

    begin
      value = Integer(input)
      if min && value < min
        puts "输入值必须大于等于 #{min}"
        next
      end
      if max && value > max
        puts "输入值必须小于等于 #{max}"
        next
      end
      return value
    rescue ArgumentError
      puts "请输入有效的整数"
    end
  end
end

def get_time_input(prompt)
  loop do
    print "#{prompt} (格式: YYYY-MM-DD HH:MM:SS，留空使用当前时间): "
    input = gets.chomp.strip

    # input may be: MM-DD, HH:MM:SS, need add current year
    if input.match?(/^\d{2}-\d{2}$/) # MM-DD
      input = "#{Time.now.year}-#{input}" # YYYY-MM-DD
    end
    if input.match?(/^\d{4}-\d{2}-\d{2}$/) # YYYY-MM-DD
      input = "#{input} 08:18:18"
    end
    if input.match?(/^\d{2}:\d{2}:\d{2}$/) # HH:MM:SS
      # need padding 0 for month and day
      month = Time.now.month.to_s.rjust(2, "0")
      day = Time.now.day.to_s.rjust(2, "0")
      input = "#{Time.now.year}-#{month}-#{day} #{input}"
    end
    puts "time:#{input}"

    return Time.now.to_i if input.empty?

    begin
      return Time.parse(input).to_i
    rescue ArgumentError
      puts "请输入有效的时间格式"
    end
  end
end

def get_user_wallet_amount(user_id)
  user = User.find(user_id)
  wallet = user.wallet
  if wallet.nil?
    puts "警告: 用户没有钱包记录，将使用随机金额"
    return rand(1000..10000)
  end

  amount = wallet.balance
  puts "用户当前钱包余额: #{amount}"
  amount
end

def get_valid_user_id
  loop do
    user_id = get_integer_input("用户ID", min: 1)
    return rand(1..100) if user_id.nil? # 如果用户没有输入，使用随机值

    user = User.find_by(id: user_id)
    if user.nil?
      puts "错误: 用户ID #{user_id} 不存在，请重新输入"
      next
    end

    puts "找到用户: #{user.name} (ID: #{user.id}) username: #{user.name} mobile: #{user.mobile} created_at: #{user.created_at}"
    get_user_wallet_amount(user_id)  # 显示用户当前金额
    return user_id
  end
end

def add_test_messages
  stream_key = RedisStreamProcessor::STREAM_KEY

  puts "开始添加测试消息..."
  puts "Stream Key: #{stream_key}"
  puts "请按提示输入消息参数（直接回车使用默认值）"
  puts "---"

  begin
    Rails.cache.redis.with do |redis_client|
      loop do
        user_id = get_valid_user_id
        bet = get_integer_input("下注金额", min: 0) || rand(10..1000)
        win = get_integer_input("赢利") || rand(-1000..1000)
        real_win = win - bet  # 实际赢利 = 赢利 - 下注金额

        before_money = get_user_wallet_amount(user_id)
        after_money = before_money + real_win  # 下注后金额 = 下注前金额 + 实际赢利
        # 为了防止有小数点，x 100
        before_money = (before_money * 100).to_i
        after_money = (after_money * 100).to_i

        message = {
          user_id: user_id,
          bet: bet,
          real_win: real_win,
          win: win,
          game_number: "G#{rand(1000..9999)}",
          round_id: "R#{rand(10000..99999)}",
          before_money: before_money,
          after_money: after_money,
          bet_time: get_time_input("下注时间")
        }
        puts "message:#{message.inspect}"

        entry_id = redis_client.xadd(stream_key, message)
        puts "\n添加消息成功:"
        puts "Entry ID: #{entry_id}"
        puts "Message: #{message.inspect}"
        puts "---"

        print "是否继续添加消息？(y/n): "
        break unless gets.chomp.downcase == 'y'
        puts
      end
    end
    puts "测试消息添加完成"
  rescue => e
    puts "错误: #{e.message}"
    puts e.backtrace
  end
end

# 执行添加消息
add_test_messages
