#!/bin/bash
# 登录到集成服务器中，执行更新脚本，拉取最新构建的docker镜像, 更新tt_front_v1，并重启服务
ssh -p 10022 -i ~/.ssh/devopshk.pem ubuntu@16.162.120.92 '
cd ~/catalyst-admin
docker compose pull
docker compose down
docker compose up -d
sudo docker cp catalyst-admin-web-1:/rails/public/. /var/www/bms_assets/
sudo chown -R caddy:caddy /var/www/bms_assets/

# 添加S3同步
echo "Syncing assets to S3..."
docker exec catalyst-admin-web-1 bundle exec rake assets:sync_to_s3

echo "###### update tt_front_v1 #######"
cd ~/code/tt_front_v1
echo "node version: $(node -v)"
echo "npm version: $(npm -v)"
git restore .
git checkout main
git pull
npm install
sudo rm -rf dist
npm run build:test
sudo rm -rf /var/www/tt_front_v1
sudo cp -r dist /var/www/tt_front_v1
sudo chown -R caddy:caddy /var/www/tt_front_v1
echo "###### Finished update tt_front_v1 #######"

sudo systemctl restart caddy.service

echo "Finished all update"
'
