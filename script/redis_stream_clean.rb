require 'redis'
require 'time'

# 初始化 Redis 连接
redis = Redis.new(
  host: '127.0.0.1',
  port: 6379,
  db: 0
)

# 设置 Stream 名称
stream_name = "mystream"

# 使用 UTC 时间计算当前时间减去 1 天的毫秒时间戳
cutoff_time = Time.now.utc - 86400 # 86400 秒 = 1 天
cutoff_ms = (cutoff_time.to_f * 1000).to_i
min_id = "#{cutoff_ms}-0"

# 执行 XTRIM MINID 操作
result = redis.call("XTRIM", stream_name, "MINID", min_id)

puts "✅ 删除截止 ID 小于 #{min_id} 的消息，共清理：#{result} 条"
puts "清理时间点：#{cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
