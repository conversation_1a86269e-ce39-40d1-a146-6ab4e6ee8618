#!/usr/bin/env ruby
# 消费 Redis Stream 中的消息
# 使用方法: rails runner script/consume_stream.rb

require_relative "../config/environment"

def consume_messages
  stream_key = RedisStreamProcessor::STREAM_KEY
  group_name = RedisStreamProcessor::GROUP_NAME
  consumer_name = RedisStreamProcessor::CONSUMER_NAME

  puts "开始消费消息..."
  puts "Stream Key: #{stream_key}"
  puts "Group Name: #{group_name}"
  puts "Consumer Name: #{consumer_name}"

  begin
    Rails.cache.redis.with do |redis_client|
      # 确保 stream 存在
      unless redis_client.exists?(stream_key)
        puts "创建新的 Stream..."
        redis_client.xadd(stream_key, { init: "true" })
      end

      # 确保 consumer group 存在
      begin
        redis_client.xgroup(:create, stream_key, group_name, "0", mkstream: true)
        puts "创建 Consumer Group 成功"
      rescue Redis::CommandError => e
        if e.message.include?("BUSYGROUP")
          puts "Consumer Group 已存在"
        else
          raise e
        end
      end

      # 注册消费者
      begin
        redis_client.xgroup(:createconsumer, stream_key, group_name, consumer_name)
        puts "注册消费者成功"
      rescue Redis::CommandError => e
        puts "消费者可能已存在: #{e.message}"
      end

      loop do
        # 读取消息
        messages = redis_client.xreadgroup(
          group_name,
          consumer_name,
          stream_key,
          ">",
          count: 10,
          block: 2000
        )

        if messages.empty?
          next
        end

        puts "收到#{messages.size}条新消息:"
        messages.each do |stream, entries|
          puts "正在处理#{entries.size}条entry:"
          entries.each do |entry_id, fields|
            puts "Entry ID: #{entry_id}, Fields: #{fields.inspect}"
            puts "--------------------------------"
            sleep 3
            # 确认消息已处理
            redis_client.xack(stream_key, group_name, entry_id)
            puts "消息已确认"
          end
        end
      end
    end
  rescue => e
    puts "错误: #{e.message}"
    puts e.backtrace
  end
end

# 执行消费
consume_messages
