# 表参数
table_name = "UserBetLogs_v1"

# 创建表
begin
  DYNAMO_DB_CLIENT.create_table(
    table_name: table_name,
    attribute_definitions: [
      { attribute_name: 'tenantIdDay', attribute_type: 'S' },
      { attribute_name: 'userIdActionTime', attribute_type: 'N' },
      { attribute_name: 'tenantIdUserId', attribute_type: 'S' },
      { attribute_name: 'actionTimeEpoch', attribute_type: 'N' }
    ],
    key_schema: [
      { attribute_name: 'tenantIdDay', key_type: 'HASH' },
      { attribute_name: 'userIdActionTime', key_type: 'RANGE' }
    ],
    global_secondary_indexes: [
      {
        index_name: 'UserIdIndex',
        key_schema: [
          { attribute_name: 'tenantIdUserId', key_type: 'HASH' },
          { attribute_name: 'actionTimeEpoch', key_type: 'RANGE' }
        ],
        projection: {
          projection_type: 'ALL'
        }
      }
    ],
    billing_mode: 'PAY_PER_REQUEST'
  )

  puts "正在创建表 '#{table_name}'..."

  # 等待表创建完成
  DYNAMO_DB_CLIENT.wait_until(:table_exists, table_name: table_name) do |w|
    w.max_attempts = 20
    w.delay = 5
  end

  puts "表 '#{table_name}' 创建成功！"

  DYNAMO_DB_CLIENT.update_time_to_live(
    table_name: table_name,
    time_to_live_specification: {
      attribute_name: 'ttl',
      enabled: true
    }
  )

rescue Aws::DynamoDB::Errors::ServiceError => e
  puts "创建表时出错: #{e.message}"
end
