### **Ruby on Rails 开发规范**
1. **版本与规范**  
   - 使用 **最新的稳定版本** 的 Ruby 和 Rails。  
   - 严格遵循 Rails 默认约定：
     - RESTful 路由设计，复数模型名（如 `User` → `users` 表），控制器类名复数
     （如 `UsersController`）, UsersController 对应到 app/controllers/users_controller.rb 文件。  
     - 优先使用内置方法（如 `link_to`, `form_with`），避免重复造轮子。

2. **代码风格**  
   - **字符串**：统一使用双引号（`"string"`），即使无插值。  
   - **ERB 模板**：使用 `<%= %>` 和 `<% %>`，避免冗余换行；视图文件后缀为 `.html.erb` 和 `.turbo_stream.erb`。  
   - **枚举（enum）**：使用时，它的第一个参数是一个Symbol，类似的格式如下：  enum :status, {  pending: 0,  paid: 1 }

3. **依赖管理**  
   - 禁止无明确授权引入新 Gem。若需添加，必须符合以下条件之一：  
     - Rails 内置功能无法实现。  
     - 能显著提升性能/安全性。  
   - 例外：允许默认包含 `tailwindcss-rails`（见样式规范）。

4. **测试要求**  
   - **测试驱动开发**：所有新功能需附带测试（只能使用Rails自带的测试框架），覆盖率需包括：  
     - 正常流程（Happy Path）。  
     - 边界条件（如空值、极值）。  
     - 异常处理（如无效参数、权限不足）。  
   - 测试文件名格式：`<model>_test.rb`（模型测试）、`<controller>_test.rb`（控制器测试）。
   - 只需要测试Controller和Model，不需要测试View和Helper
   - 每次在改动了相关的文件代码后，都需求运行此文件对应的测试文件，确保代码的正确性。

5. **路由与页面**  
   - **首页**：默认路由为 `root "home#index"`，除非明确要求修改。  
   - **Current 用户**：通过 `Current.admin` 获取当前用户
   - 与用户登录状态相关的代码位于 app/controllers/concerns/authentication.rb 文件中

6. **样式规范**  
   - 使用 **Tailwind CSS** 进行样式设计，禁止内联 `style` 或引入其他 CSS 框架。  
   - 类名按功能分组，保持可读性（如 `class="flex justify-between px-4"`）。
   - 页面中使用了 daisyui 的组件，在编写代码时，要考虑到 daisyui 的组件的样式。

7. **代码生成**  
   - 仅生成必要代码，禁止自动添加注释（如 `# This is a comment`）。  
   - 模型/控制器生成命令需包含测试文件（如 `rails g model User name:string`）。
   - 在生成app/controllers目录下的控制器的代码时，注意它的父类是 ApplicationController; 在生成app/controllers/admin 目录下的控制器时，它的父类是 Admin::ApplicationController
   - 在 erb 模板中不要使用多行Ruby代码，应该让控制器中的 action 方法来处理
   - 在编写 Javascript 的代码时，要考虑到当前处于 Hotwire 的环境中。

8. 代码调试，在利用 Rails test 目录下的测试文件进行调试时，遇到不好解决的错误时，利用 puts 打印出错误信息，然后根据错误信息进行调试。

8. 每次回答时，加上称呼：老板。结束时，加上：让我们运行测试看看结果   