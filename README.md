这是使用Ruby on Rails 8实现的后台业务管理项目。


## Get started

### 本地开发环境

```
<NAME_EMAIL>:sancatalyst/catalyst-admin-backend.git
cd catalyst-admin-backend
cp config/database.yml.sample config/database.yml
cp doc/deploy/env.sample .env.development.local
```

将 development.key 这个文件放在目录中 config/credentials 目录中，这个文件不应该提交到git仓库.

依赖：

- Ruby 3.4.3
- mysql-server 8.0
- redis-server 7
- dynamodb

```
sudo apt-get install redis-server
sudo apt-get install mysql-server
sudo apt-get install libmysqlclient-dev
sudo apt install mysql-client-core-8.0
sudo apt install libvips
gem install bundler
bundle
rails db:create
rails db:prepare
bin/dev
```

配置好AWS的身份信息后，开通dynamodb后，通过`./bin/rails runner script/create_dynamo_tables.rb` 创建必要的数据表。
手动启动 `rails redis_stream:processor` 用来处理 stream 消息队列，将消息存入 dynamodb 中。

## 部署

### 使用 Docker Compose 部署

1. 确保服务器已安装 Docker 和 Docker Compose

```
mkdir -p catalyst-admin/{redis-data,credentials,storage,public_static}
```

2. 创建 `.env` 文件并配置必要的环境变量：
```bash
# 数据库配置
CATALYST_ADMIN_BACKEND_DATABASE_PASSWORD=your_secure_password
MYSQL_ROOT_PASSWORD=your_root_password

# 生产环境配置
RAILS_ENV=production
RAILS_SERVE_STATIC_FILES=true
RAILS_LOG_TO_STDOUT=true

# ID hash 加密的种子，注意不要有重复的字母和数字
SQIDS_ALPHABET=JXdfzs43aR7
```

4. 项目根目录下的 `docker-compose.prod.yml` 文件配置了以下服务：
   - `web`: Rails 应用服务
     - 使用项目 Dockerfile 构建
     - 暴露 80 端口
     - 挂载 storage 和 log 目录
   - `db`: MySQL 8.0 数据库服务
     - 自动创建数据库和用户
     - 数据持久化存储
   - 服务间通过 `app-network` 网络通信

5. 构建并启动服务：
```bash
docker compose -f docker-compose.prod.yml up -d --build
```

7. 服务访问：
- API 服务运行在 http://your-server-ip:80
- MySQL 数据库运行在 localhost:3306

### 使用 GitHub Actions 部署

项目已配置 GitHub Actions 工作流，当推送新的版本标签时会自动构建并发布 Docker 镜像：

1. 创建新的版本标签：
```bash
git tag v1.0.0
git push origin v1.0.0
```

2. 在 GitHub Packages 中查看构建的镜像：
```
ghcr.io/sancatalyst/catalyst-admin-backend
```

## 开发工作流

### Git Hooks 设置

项目配置了 Git pre-commit hook，会在每次提交前自动执行代码格式化。

安装 Git hooks：
```bash
./script/install_git_hooks.sh
```

### 代码格式化

代码提交前会自动执行 `./script/format_code.sh` 进行代码格式化。

手动格式化代码：
```bash
./script/format_code.sh
# 或者直接使用 RuboCop
bin/rubocop -f github -a
```

跳过 pre-commit hook（不推荐）：
```bash
git commit --no-verify
```